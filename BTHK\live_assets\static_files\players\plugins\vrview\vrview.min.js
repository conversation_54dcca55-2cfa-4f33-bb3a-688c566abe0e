/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,r,n){function a(s,o){if(!r[s]){if(!t[s]){var u="function"==typeof require&&require;if(!o&&u)return u(s,!0);if(i)return i(s,!0);var l=new Error("Cannot find module '"+s+"'");throw l.code="MODULE_NOT_FOUND",l}var c=r[s]={exports:{}};t[s][0].call(c.exports,function(e){var r=t[s][1][e];return a(r||e)},c,c.exports,e,t,r,n)}return r[s].exports}for(var i="function"==typeof require&&require,s=0;s<n.length;s++)a(n[s]);return a}({1:[function(e,t,r){"use strict";var n={isMediaStarted:!1,isMediaLoaded:!1,creationQueue:[],prepareSettings:function(e){n.isLoaded?n.createInstance(e):(n.loadScript(e),n.creationQueue.push(e))},loadScript:function(e){if(!n.isMediaStarted){if("undefined"!=typeof VRView)n.createInstance(e);else{var t=document.createElement("script"),r=document.getElementsByTagName("script")[0],a=!1;e.options.path="string"==typeof e.options.path?e.options.path:"https://googlevr.github.io/vrview/build/vrview.min.js",t.src=e.options.path,t.onload=t.onreadystatechange=function(){a||this.readyState&&void 0!==this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(a=!0,n.mediaReady(),t.onload=t.onreadystatechange=null)},r.parentNode.insertBefore(t,r)}n.isMediaStarted=!0}},mediaReady:function(){for(n.isLoaded=!0,n.isMediaLoaded=!0;n.creationQueue.length>0;){var e=n.creationQueue.pop();n.createInstance(e)}},createInstance:function(e){var t=new VRView.Player("#"+e.id,e.options);window["__ready__"+e.id](t)}},a={name:"vrview",options:{prefix:"vrview"},canPlayType:function(e){return~["video/mp4","application/x-mpegurl","vnd.apple.mpegurl","application/dash+xml"].indexOf(e.toLowerCase())},create:function(e,t,r){var a=[],i={},s=null,o=!0,u=1,l=u;i.options=t,i.id=e.id+"_"+t.prefix,i.mediaElement=e;for(var c=mejs.html5media.properties,d=0,p=c.length;d<p;d++)!function(t){var r=t.substring(0,1).toUpperCase()+t.substring(1);i["get"+r]=function(){if(null!==s){switch(t){case"currentTime":return s.getCurrentTime();case"duration":return s.getDuration();case"volume":return u=s.getVolume();case"muted":return 0===u;case"paused":return o=s.isPaused;case"ended":return!1;case"src":return"";case"buffered":return{start:function(){return 0},end:function(){return 0},length:1};case"readyState":return 4}return null}return null},i["set"+r]=function(r){if(null!==s)switch(t){case"src":var n="string"==typeof r?r:r[0].src;s.setContentInfo({video:n});break;case"currentTime":s.setCurrentTime(r),setTimeout(function(){var t=mejs.Utils.createEvent("timeupdate",i);e.dispatchEvent(t)},50);break;case"volume":s.setVolume(r),setTimeout(function(){var t=mejs.Utils.createEvent("volumechange",i);e.dispatchEvent(t)},50);break;case"muted":u=r?0:l,s.setVolume(u),setTimeout(function(){var t=mejs.Utils.createEvent("volumechange",i);e.dispatchEvent(t)},50);break;case"readyState":var o=mejs.Utils.createEvent("canplay",i);e.dispatchEvent(o);break;default:console.log("VRView "+i.id,t,"UNSUPPORTED property")}else a.push({type:"set",propName:t,value:r})}}(c[d]);for(var v=mejs.html5media.methods,f=0,m=v.length;f<m;f++)!function(e){i[e]=function(){if(null!==s)switch(e){case"play":return s.play();case"pause":return s.pause();case"load":return null}else a.push({type:"call",methodName:e})}}(v[f]);var h=document.createElement("div");h.setAttribute("id",i.id),h.style.width="100%",h.style.height="100%",window["__ready__"+i.id]=function(t){e.vrPlayer=s=t;var r=h.querySelector("iframe");if(r.style.width="100%",r.style.height="100%",a.length)for(var n=0,o=a.length;n<o;n++){var u=a[n];if("set"===u.type){var l=u.propName,c=""+l.substring(0,1).toUpperCase()+l.substring(1);i["set"+c](u.value)}else"call"===u.type&&i[u.methodName]()}s.on("ready",function(){for(var t=mejs.html5media.events.concat(["mouseover","mouseout"]),r=0,n=t.length;r<n;r++)!function(r,n){s.on(t[r],function(){var n=mejs.Utils.createEvent(t[r],i);e.dispatchEvent(n)})}(r)})},e.originalNode.parentNode.insertBefore(h,e.originalNode),e.originalNode.style.display="none";var y={path:t.vrPath,is_stereo:t.vrIsStereo,is_autopan_off:t.vrIsAutopanOff,is_debug:t.vrDebug,default_yaw:t.vrDefaultYaw,is_yaw_only:t.vrIsYawOnly,loop:t.loop};if(r&&r.length>0)for(var g=0,w=r.length;g<w;g++)if(mejs.Renderers.renderers[t.prefix].canPlayType(r[g].type)){y.video=r[g].src,y.width="100%",y.height="100%";break}return n.prepareSettings({options:y,id:i.id}),i.hide=function(){i.pause(),s&&(h.style.display="none")},i.setSize=function(){},i.show=function(){s&&(h.style.display="")},i}};mejs.Renderers.add(a),Object.assign(mejs.MepDefaults,{vrPath:null,vrIsStereo:!0,vrIsAutopanOff:!0,vrDebug:!1,vrDefaultYaw:0,vrIsYawOnly:!1}),Object.assign(MediaElementPlayer.prototype,{buildvrview:function(e,t,r,n){var a=this;if(a.isVideo&&(!a.isVideo||null===a.media.rendererName||a.media.rendererName.match(/(native\_(dash|hls)|html5)/))){var i=document.createElement("div");e.detectFullscreenMode(),i.className=a.options.classPrefix+"button "+a.options.classPrefix+"vrview-button",i.innerHTML='<button type="button" aria-controls="'+a.id+'" title="VR" aria-label="VR" tabindex="0"></button>',i.addEventListener("click",function(){mejs.Features.HAS_TRUE_NATIVE_FULLSCREEN&&mejs.Features.IS_FULLSCREEN||e.isFullScreen?e.exitFullScreen():e.enterFullScreen()}),a.globalBind("keydown",function(t){27===(t.which||t.keyCode||0)&&(mejs.Features.HAS_TRUE_NATIVE_FULLSCREEN&&mejs.Features.IS_FULLSCREEN||e.isFullScreen)&&e.exitFullScreen()}),a.addControlElement(i,"vrview");var s=n.getSrc(),o=[{src:s,type:mejs.Utils.getTypeFromFile(s)}],u=mejs.Renderers.select(o,["vrview"]);n.changeRenderer(u.rendererName,o)}}})},{}]},{},[1]);