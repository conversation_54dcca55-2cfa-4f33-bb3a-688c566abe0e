'use strict';/*!
 * This is a `i18n` language object.
 *
 * Malay (for inter-country use - see the countries mentioned in infobox`s `native to` at https://en.wikipedia.org/wiki/Malay_language)
 *
 * <AUTHOR>   muhdnurhidayat (Twitter: @mnh48com)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.ms === undefined) {
		exports.ms = {
			'mejs.plural-form': 0,			
			'mejs.download-file': 'Muat Turun Fail',			
			'mejs.install-flash': 'Anda sedang menggunakan pelayar internet yang tidak mempunyai pemain Flash. Sila aktifkan pemalam pemain Flash anda atau muat turun versi terbaru dari https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Skrin penuh',			
			'mejs.play': 'Main',
			'mejs.pause': '<PERSON>a',			
			'mejs.time-slider': '<PERSON><PERSON><PERSON><PERSON>',
			'mejs.time-help-text': 'Gunakan kekunci Anak Panah Kiri/Kanan untuk bergerak satu saat, Anak Panah Atas/Bawah untuk bergerak sepuluh saat.',
			'mejs.live-broadcast' : 'Siaran Langsung',			
			'mejs.volume-help-text': 'Gunakan kekunci Anak Panah Atas/Bawah untuk menguatkan atau memperlahankan bunyi.',
			'mejs.unmute': 'Nyahsenyap',
			'mejs.mute': 'Senyap',
			'mejs.volume-slider': 'Lungsur Bunyi',			
			'mejs.video-player': 'Pemain Video',
			'mejs.audio-player': 'Pemain Audio',			
			'mejs.captions-subtitles': 'Sarikata',
			'mejs.captions-chapters': 'Bab',
			'mejs.none': 'Tiada',
			'mejs.afrikaans': 'Bahasa Afrikaans',
			'mejs.albanian': 'Bahasa Albania',
			'mejs.arabic': 'Bahasa Arab',
			'mejs.belarusian': 'Bahasa Belarus',
			'mejs.bulgarian': 'Bahasa Bulgaria',
			'mejs.catalan': 'Bahasa Catalonia',
			'mejs.chinese': 'Bahasa Cina',
			'mejs.chinese-simplified': 'Bahasa Cina (Ringkas)',
			'mejs.chinese-traditional': 'Bahasa Cina (Tradisional)',
			'mejs.croatian': 'Bahasa Croatia',
			'mejs.czech': 'Bahasa Czech',
			'mejs.danish': 'Bahasa Denmark',
			'mejs.dutch': 'Bahasa Belanda',
			'mejs.english': 'Bahasa Inggeris',
			'mejs.estonian': 'Bahasa Estonia',
			'mejs.filipino': 'Bahasa Filipino',
			'mejs.finnish': 'Bahasa Finland',
			'mejs.french': 'Bahasa Perancis',
			'mejs.galician': 'Bahasa Galicia',
			'mejs.german': 'Bahasa Jerman',
			'mejs.greek': 'Bahasa Greek',
			'mejs.haitian-creole': 'Bahasa Kreol Haiti',
			'mejs.hebrew': 'Bahasa Ibrani',
			'mejs.hindi': 'Bahasa Hindi',
			'mejs.hungarian': 'Bahasa Hungary',
			'mejs.icelandic': 'Bahasa Iceland',
			'mejs.indonesian': 'Bahasa Indonesia',
			'mejs.irish': 'Bahasa Ireland',
			'mejs.italian': 'Bahasa Itali',
			'mejs.japanese': 'Bahasa Jepun',
			'mejs.korean': 'Bahasa Korea',
			'mejs.latvian': 'Bahasa Latvia',
			'mejs.lithuanian': 'Bahasa Lithuania',
			'mejs.macedonian': 'Bahasa Macedonia',
			'mejs.malay': 'Bahasa Melayu',
			'mejs.maltese': 'Bahasa Malta',
			'mejs.norwegian': 'Bahasa Norway',
			'mejs.persian': 'Bahasa Parsi',
			'mejs.polish': 'Bahasa Poland',
			'mejs.portuguese': 'Bahasa Portugis',
			'mejs.romanian': 'Bahasa Romania',
			'mejs.russian': 'Bahasa Rusia',
			'mejs.serbian': 'Bahasa Serbia',
			'mejs.slovak': 'Bahasa Slovak',
			'mejs.slovenian': 'Bahasa Slovene',
			'mejs.spanish': 'Bahasa Sepanyol',
			'mejs.swahili': 'Bahasa Swahili',
			'mejs.swedish': 'Bahasa Sweden',
			'mejs.tagalog': 'Bahasa Tagalog',
			'mejs.thai': 'Bahasa Thai',
			'mejs.turkish': 'Bahasa Turki',
			'mejs.ukrainian': 'Bahasa Ukraine',
			'mejs.vietnamese': 'Bahasa Vietnam',
			'mejs.welsh': 'Bahasa Wales',
			'mejs.yiddish': 'Bahasa Yiddish'
		};
	}
})(mejs.i18n);