{"sectionsData": [], "reportsData": [], "pinedItems": [], "reqsData": [], "localusers": [{"id": "RMUID-1732471448255", "isApp": false, "browserName": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "operatingSystem": "Win32", "screenWidth": 1536, "screenHeight": 864, "isMobileDevice": false, "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "language": "ar", "platform": "Win32", "vendor": "Google Inc.", "product": "Gecko", "appVersion": "5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "appName": "Netscape", "appCodeName": "Mozilla", "screenColorDepth": 24, "screenPixelDepth": 24, "isTouchDevice": true, "isCookieEnabled": true, "timeZoneOffset": -180, "isJavaEnabled": false, "isDoNotTrackEnabled": false, "isOnline": true, "ip": "::ffff:**********0", "bandwidth": 0, "lastVisit": **********, "createdAt": **********, "blocked": false}, {"id": "RMUID-1732394823731", "isApp": true, "browserName": "Android", "operatingSystem": "Linux aarch64", "screenWidth": 384, "screenHeight": 854, "isMobileDevice": false, "userAgent": "Android", "language": "en-US", "platform": "Linux aarch64", "vendor": "Google Inc.", "product": "Gecko", "appVersion": "Android", "appName": "Netscape", "appCodeName": "Mozilla", "screenColorDepth": 24, "screenPixelDepth": 24, "isTouchDevice": true, "isCookieEnabled": true, "timeZoneOffset": -180, "isJavaEnabled": false, "isDoNotTrackEnabled": false, "isOnline": true, "ip": "::ffff:**********", "bandwidth": 0, "lastVisit": **********, "createdAt": **********, "blocked": false}, {"id": "RMUID-1733252029847", "isApp": true, "browserName": "Android", "operatingSystem": "Linux aarch64", "screenWidth": 393, "screenHeight": 895, "isMobileDevice": false, "userAgent": "Android", "language": "ar-EG", "platform": "Linux aarch64", "vendor": "Google Inc.", "product": "Gecko", "appVersion": "Android", "appName": "Netscape", "appCodeName": "Mozilla", "screenColorDepth": 24, "screenPixelDepth": 24, "isTouchDevice": true, "isCookieEnabled": true, "timeZoneOffset": -180, "isJavaEnabled": false, "isDoNotTrackEnabled": false, "isOnline": true, "ip": "::ffff:**********", "bandwidth": 0, "lastVisit": **********, "createdAt": **********, "blocked": false}], "cards": {}, "groups": [{"id": "ID-5030936314352321682115205525", "title": "فئة 1", "streamSpeed": 1200, "timeInSecs": 36000, "endInDays": 10, "bandwidth": 53687091200, "length": "6", "chars": "**********", "price": 300, "createdAt": 1682115205525}, {"id": "ID-18332179361240681682115229307", "title": "فئة 2", "streamSpeed": 2000, "timeInSecs": 54000, "endInDays": 30, "bandwidth": 107374182400, "length": "10", "chars": "**********", "price": 500, "createdAt": 1682115229307}], "users": [{"id": "mainAdmin", "name": "main admin", "username": "root", "email": "email", "password": "Aa12345+", "level": "0", "share_path": "", "lastLogin": 0, "allowed": "1", "about": "", "phoneNumber": "", "facebook": "", "twitter": "", "instagram": "", "userId": "mainAdmin", "imagePath": "mainAdmin.jpg", "birthDate": "1960-1-1", "activeVideoCallsPublic": "off", "showProfileUserSharing": "off", "activeVideoCalls": "off", "showProfile": "off"}], "settings": {"cardsEnabled": "no", "force_sync": "no", "interface": "OuterSpace", "adding_quran": "no", "auto_detect_added_files": "no", "all_stream_band": 0, "IS_OPEN_STREAM": "no", "CURRENT_APP_VERSION": "1.0.0", "APP_VERSION_CODE": 100, "main_name": "فاست نت", "main_desc": "ميديا سيرفر للعائله في كل مكان .", "estra7ah_type": "wireless", "port": 80, "main_phone": "735758308", "main_facebook": "https://www.facebook.com/YeTech.Official", "is_stop_constraction": "no", "is_only_app": "no", "main_active_players": "yes", "main_active_downloaders": "no", "reboot_every_4_hours": "no", "FTPServer": "no", "mubasher_port": "3333", "take_photos": "no", "get_from_internet_on_sync": "no", "sizes": "240p", "streamSpeed": 0, "autoBlockIDM": "no", "numOfConnectionPerIp": 10, "downloadActive": "yes", "devsSessions": {"huawei-android": 0, "smart-tv-android": 0, "kodi": 0, "samsung": 0, "android": 6, "webOS": 0, "iPhone": 0, "iPad": 0, "iPod": 0, "BlackBerry": 0, "Windows_Phone": 0, "win": 28}, "news": [], "exts": [], "ads": [], "views": 30, "APP_VERSION": "*******", "backup_path": "", "isAutoBackup": "yes", "isApprove": true, "show_sports": "yes"}}