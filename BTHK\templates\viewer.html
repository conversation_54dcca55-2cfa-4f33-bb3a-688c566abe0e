<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BTHK Live - البث المباشر</title>
    <link rel="icon" href="/interface/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #4CAF50;
        }
        
        .viewer-count {
            background: rgba(76, 175, 80, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-block;
            margin-top: 0.5rem;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 1rem;
            padding: 1rem;
        }
        
        .video-container {
            flex: 1;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
        }
        
        .video-player {
            flex: 1;
            background: #000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            min-height: 400px;
        }
        
        .video-player video {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }
        
        .no-stream {
            text-align: center;
            color: #ccc;
            font-size: 1.2rem;
        }
        
        .video-controls {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .control-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .control-btn:hover {
            background: #45a049;
        }
        
        .control-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .volume-control {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .volume-slider {
            width: 100px;
        }
        
        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 1rem;
            overflow-y: auto;
        }
        
        .sidebar h3 {
            margin-bottom: 1rem;
            color: #4CAF50;
            text-align: center;
        }
        
        .channel-list {
            list-style: none;
        }
        
        .channel-item {
            background: rgba(255, 255, 255, 0.1);
            margin-bottom: 0.5rem;
            padding: 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .channel-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }
        
        .channel-item.active {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.2);
        }
        
        .channel-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .channel-status {
            font-size: 0.8rem;
            color: #ccc;
        }
        
        .status-live {
            color: #4CAF50;
        }
        
        .status-offline {
            color: #f44336;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #ccc;
        }
        
        .error-message {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: auto;
            }
            
            .sidebar {
                width: 100%;
                order: -1;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎥 BTHK Live System</h1>
        <p>نظام البث المباشر</p>
        <div class="viewer-count">
            👥 المشاهدون: <span id="viewerCount">0</span>
        </div>
    </div>
    
    <div class="main-container">
        <div class="video-container">
            <div class="video-player" id="videoPlayer">
                <div class="no-stream">
                    <p>🔴 لا يوجد بث مباشر حالياً</p>
                    <p>اختر قناة من القائمة الجانبية</p>
                </div>
            </div>
            
            <div class="video-controls">
                <button class="control-btn" id="playBtn" disabled>▶️ تشغيل</button>
                <button class="control-btn" id="pauseBtn" disabled>⏸️ إيقاف</button>
                <button class="control-btn" id="stopBtn" disabled>⏹️ توقف</button>
                
                <div class="volume-control">
                    <span>🔊</span>
                    <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="50">
                </div>
                
                <button class="control-btn" id="fullscreenBtn" disabled>🔳 ملء الشاشة</button>
            </div>
        </div>
        
        <div class="sidebar">
            <h3>📺 قائمة القنوات</h3>
            <div id="channelList" class="loading">
                جاري تحميل القنوات...
            </div>
        </div>
    </div>
    
    <script src="/socket.io/socket.io.js"></script>
    <script>
        const socket = io();
        let currentVideo = null;
        let currentChannel = null;
        
        // DOM elements
        const videoPlayer = document.getElementById('videoPlayer');
        const channelList = document.getElementById('channelList');
        const viewerCount = document.getElementById('viewerCount');
        const playBtn = document.getElementById('playBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');
        const volumeSlider = document.getElementById('volumeSlider');
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        
        // Socket events
        socket.on('connect', () => {
            console.log('Connected to server');
            socket.emit('joinViewer');
            loadChannels();
        });
        
        socket.on('disconnect', () => {
            console.log('Disconnected from server');
        });
        
        socket.on('viewerCount', (count) => {
            viewerCount.textContent = count;
        });
        
        socket.on('channelAdded', (channel) => {
            loadChannels();
        });
        
        socket.on('channelDeleted', (data) => {
            loadChannels();
        });
        
        socket.on('channelSwitched', (channelId) => {
            // Handle remote channel switching if needed
        });
        
        // Load channels from API
        async function loadChannels() {
            try {
                const response = await fetch('/api/channels');
                const data = await response.json();
                
                if (data.channels && data.channels.length > 0) {
                    displayChannels(data.channels);
                } else {
                    channelList.innerHTML = '<p style="text-align: center; color: #ccc;">لا توجد قنوات متاحة</p>';
                }
            } catch (error) {
                console.error('Error loading channels:', error);
                channelList.innerHTML = '<div class="error-message">خطأ في تحميل القنوات</div>';
            }
        }
        
        // Display channels
        function displayChannels(channels) {
            const channelsHtml = channels.map(channel => `
                <div class="channel-item" data-id="${channel.id}" onclick="selectChannel(${channel.id}, '${channel.url}', '${channel.name}')">
                    <div class="channel-name">${channel.name}</div>
                    <div class="channel-status ${channel.status === 'active' ? 'status-live' : 'status-offline'}">
                        ${channel.status === 'active' ? '🔴 مباشر' : '⚫ غير متاح'}
                    </div>
                </div>
            `).join('');
            
            channelList.innerHTML = `<ul class="channel-list">${channelsHtml}</ul>`;
        }
        
        // Select and play channel
        function selectChannel(id, url, name) {
            // Remove active class from all channels
            document.querySelectorAll('.channel-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to selected channel
            document.querySelector(`[data-id="${id}"]`).classList.add('active');
            
            currentChannel = { id, url, name };
            playStream(url, name);
            
            // Notify other clients
            socket.emit('switchChannel', id);
        }
        
        // Play stream
        function playStream(url, name) {
            // Remove existing video
            if (currentVideo) {
                currentVideo.remove();
                currentVideo = null;
            }
            
            // Create new video element
            currentVideo = document.createElement('video');
            currentVideo.controls = true;
            currentVideo.autoplay = true;
            currentVideo.style.width = '100%';
            currentVideo.style.height = '100%';
            currentVideo.style.borderRadius = '8px';
            
            // Set video source
            currentVideo.src = url;
            
            // Clear player and add video
            videoPlayer.innerHTML = '';
            videoPlayer.appendChild(currentVideo);
            
            // Enable controls
            enableControls();
            
            // Handle video events
            currentVideo.addEventListener('loadstart', () => {
                console.log('Loading stream:', name);
            });
            
            currentVideo.addEventListener('error', (e) => {
                console.error('Video error:', e);
                showError('خطأ في تشغيل البث');
            });
            
            currentVideo.addEventListener('loadeddata', () => {
                console.log('Stream loaded successfully');
            });
        }
        
        // Enable video controls
        function enableControls() {
            playBtn.disabled = false;
            pauseBtn.disabled = false;
            stopBtn.disabled = false;
            fullscreenBtn.disabled = false;
        }
        
        // Disable video controls
        function disableControls() {
            playBtn.disabled = true;
            pauseBtn.disabled = true;
            stopBtn.disabled = true;
            fullscreenBtn.disabled = true;
        }
        
        // Show error message
        function showError(message) {
            videoPlayer.innerHTML = `
                <div class="error-message">
                    <p>❌ ${message}</p>
                    <p>يرجى المحاولة مرة أخرى أو اختيار قناة أخرى</p>
                </div>
            `;
            disableControls();
        }
        
        // Control button events
        playBtn.addEventListener('click', () => {
            if (currentVideo) {
                currentVideo.play();
            }
        });
        
        pauseBtn.addEventListener('click', () => {
            if (currentVideo) {
                currentVideo.pause();
            }
        });
        
        stopBtn.addEventListener('click', () => {
            if (currentVideo) {
                currentVideo.pause();
                currentVideo.currentTime = 0;
            }
        });
        
        volumeSlider.addEventListener('input', (e) => {
            if (currentVideo) {
                currentVideo.volume = e.target.value / 100;
            }
        });
        
        fullscreenBtn.addEventListener('click', () => {
            if (currentVideo) {
                if (currentVideo.requestFullscreen) {
                    currentVideo.requestFullscreen();
                } else if (currentVideo.webkitRequestFullscreen) {
                    currentVideo.webkitRequestFullscreen();
                } else if (currentVideo.msRequestFullscreen) {
                    currentVideo.msRequestFullscreen();
                }
            }
        });
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            socket.emit('leaveViewer');
        });
    </script>
</body>
</html>
