'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.loop'] = 'Commuta Bucle';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.loop'] = 'Přep<PERSON>ut smyčku';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.loop'] = 'Wiederholung (de-)aktivieren';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.loop'] = 'Alternar Repetición';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.loop'] = 'حلقه تعویض';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.loop'] = 'Répéter';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.loop'] = 'Uključi/isključi ponavljan<PERSON>';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.loop'] = 'Húzza át a kapcsolót';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.loop'] = 'Passare il ciclo';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.loop'] = 'トグルループ';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.loop'] = '루프 토글';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.loop'] = 'Schakellus';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.loop'] = 'Zapętl';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.loop'] = 'Loop alternativo';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.loop'] = 'Comutați buclă';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.loop'] = 'Зациклить воспроизведение';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.loop'] = 'Prepínať slučku';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.loop'] = 'Repetera';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.loop'] = 'Повторювати';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.loop'] = '切換循環';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.loop'] = '切换循环';
}