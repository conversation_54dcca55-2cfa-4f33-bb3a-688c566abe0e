(this.webpackJsonpestra7ah5=this.webpackJsonpestra7ah5||[]).push([[11],{381:function(e,t,n){"use strict";var a=n(0),r=n(27),i=n(4),c=n.n(i),s=n(6),o=n(5),l=n(1),u=(n(386),n(2));t.a=function(e){var t=Object(l.useState)([]),n=Object(o.a)(t,2),i=n[0],d=n[1];if(Object(l.useEffect)(Object(s.a)(c.a.mark((function e(){return c.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:d(localStorage.getItem("ads")?JSON.parse(localStorage.getItem("ads")):[]);case 1:case"end":return e.stop()}}),e)}))),[]),0===i.length)return Object(a.jsx)("div",{});var j=i;return 1===i.length&&(j=[].concat(Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i))),2===i.length&&(j=[].concat(Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i))),i.length>2&&(j=[].concat(Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i),Object(r.a)(i))),Object(a.jsxs)("div",{style:{paddingTop:e.padding?100:"auto"},children:[Object(a.jsx)("div",{className:"ads",style:{"grid-template-columns":"repeat("+j.length+", minmax("+(window.innerWidth<500?"200px":"300px")+", 2fr))"},children:j.map((function(e){return Object(a.jsxs)("a",{className:"ad ",href:""===e.url||null===e.url?"#":e.url,onClick:function(t){u.a.fetch(u.a.host+"add-clicked/"+e.title)},children:[Object(a.jsx)("div",{className:"adover",children:e.title}),e.imageName&&Object(a.jsx)("img",{src:u.a.host+"A-dImage/"+e.imageName,style:{width:"100%",height:"100%"}}),e.videoName&&Object(a.jsx)("video",{controls:!0,style:{width:"100%",height:"100%"},muted:!0,autoPlay:!0,children:Object(a.jsx)("source",{src:u.a.host+"A-dVideo/"+e.videoName,type:"video/mp4"})})]})}))})," "]})}},382:function(e,t,n){"use strict";t.a=function(e){return"series"===e||"series.tv"===e||"series.learn"===e||"series.ramadan"===e||"series.sports"===e||"series.deen"===e||"series.sports"===e||"series.kids"===e||"series.anime"===e}},386:function(e,t,n){},389:function(e,t,n){"use strict";var a=n(0),r=n(4),i=n.n(r),c=n(13),s=n(6),o=n(5),l=n(1),u=(n(390),n(8)),d=n(2);t.a=function(e){var t=e.name,n=(e.url_to_post,Object(l.useState)(null)),r=Object(o.a)(n,2),j=r[0],f=r[1],b=Object(l.useState)(e.defaultValue),p=Object(o.a)(b,2),m=p[0],h=p[1];function O(e){return v.apply(this,arguments)}function v(){return(v=Object(s.a)(i.a.mark((function t(n){var a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.url){t.next=2;break}return t.abrupt("return");case 2:return(a=new FormData).append(e.name,n),t.next=6,d.a.fetch(e.url,{method:"POST",body:a,type:"post"}).then((function(e){return e.json()}));case 6:"ok"==t.sent.msg&&u.Store.addNotification({title:"\u062a\u0645 \u0627\u0644\u062d\u0641\u0638 \u0628\u0646\u062c\u0627\u062d",message:" \u062a\u0645 \u062d\u0641\u0638 : "+e.title,type:"success",insert:"top",container:"bottom-left",animationIn:["animate__animated","animate__fadeIn"],animationOut:["animate__animated","animate__fadeOut"],dismiss:{duration:1700}});case 8:case"end":return t.stop()}}),t)})))).apply(this,arguments)}return Object(l.useEffect)((function(){e.defaultValue!=m&&(""!=m&&"undefined"!=typeof m&&O(e.defaultValue),h(e.defaultValue))}),[e]),Object(a.jsxs)("div",{className:"input-count",style:Object(c.a)({},e.style),children:[Object(a.jsx)("div",{className:"head",children:e.title}),Object(a.jsx)("input",{ref:e.localref,className:e.dir,name:t,style:Object(c.a)(Object(c.a)({},e.inputStyle),{},{direction:e.dir}),defaultValue:e.defaultValue,onKeyPress:e.onKeyPress,onChange:function(t){if(e.onChange)e.onChange(t);else if(e.saveOnChange){null!=j&&clearTimeout(j);var n=t.currentTarget.value;f(setTimeout(Object(s.a)(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:O(n),f(null);case 2:case"end":return e.stop()}}),e)}))),1300))}},type:e.type,placeholder:e.placeholder||e.title,required:e.required},e.key),Object(a.jsx)("div",{className:"checked ok "+e.dir,children:Object(a.jsx)("i",{className:"lni lni-"+e.icon})})]},e.key)}},390:function(e,t,n){},412:function(e,t,n){},413:function(e,t,n){},426:function(e,t,n){"use strict";n.r(t);var a=n(0),r=n(4),i=n.n(r),c=n(6),s=n(5),o=n(27),l=n(1);function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}function d(e,t){return(d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var j=new Map,f=new WeakMap,b=0,p=void 0;function m(e){return Object.keys(e).sort().filter((function(t){return void 0!==e[t]})).map((function(t){return t+"_"+("root"===t?(n=e.root)?(f.has(n)||(b+=1,f.set(n,b.toString())),f.get(n)):"0":e[t]);var n})).toString()}function h(e,t,n,a){if(void 0===n&&(n={}),void 0===a&&(a=p),"undefined"===typeof window.IntersectionObserver&&void 0!==a){var r=e.getBoundingClientRect();return t(a,{isIntersecting:a,target:e,intersectionRatio:"number"===typeof n.threshold?n.threshold:0,time:0,boundingClientRect:r,intersectionRect:r,rootBounds:r}),function(){}}var i=function(e){var t=m(e),n=j.get(t);if(!n){var a,r=new Map,i=new IntersectionObserver((function(t){t.forEach((function(t){var n,i=t.isIntersecting&&a.some((function(e){return t.intersectionRatio>=e}));e.trackVisibility&&"undefined"===typeof t.isVisible&&(t.isVisible=i),null==(n=r.get(t.target))||n.forEach((function(e){e(i,t)}))}))}),e);a=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:i,elements:r},j.set(t,n)}return n}(n),c=i.id,s=i.observer,o=i.elements,l=o.get(e)||[];return o.has(e)||o.set(e,l),l.push(t),s.observe(e),function(){l.splice(l.indexOf(t),1),0===l.length&&(o.delete(e),s.unobserve(e)),0===o.size&&(s.disconnect(),j.delete(c))}}var O=["children","as","triggerOnce","threshold","root","rootMargin","onChange","skip","trackVisibility","delay","initialInView","fallbackInView"];function v(e){return"function"!==typeof e.children}l.Component;n(412);var x=n(364),y=n(11),g=n(17);var w=n(389),k=n(13),S=(n(413),n(8)),C=n(2);var I=function(e){var t=function(){var t=Object(c.a)(i.a.mark((function t(n){var a;return i.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("function"==typeof e.onChange&&e.onChange(n.currentTarget.value),e.saveOnChange){t.next=3;break}return t.abrupt("return");case 3:return(a=new FormData).append(e.name,n.currentTarget.value),t.next=7,C.a.fetch(e.url,{method:"POST",body:a,type:"post"}).then((function(e){return e.json()}));case 7:"ok"==t.sent.msg&&S.Store.addNotification({title:"\u062a\u0645 \u0627\u0644\u062d\u0641\u0638 \u0628\u0646\u062c\u0627\u062d",message:" \u062a\u0645 \u062d\u0641\u0638 : "+e.title,type:"success",insert:"top",container:"bottom-left",animationIn:["animate__animated","animate__fadeIn"],animationOut:["animate__animated","animate__fadeOut"],dismiss:{duration:1700}});case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();return Object(a.jsxs)("div",{className:"select-count",style:Object(k.a)({},e.style),children:[Object(a.jsx)("div",{className:"head",children:e.title}),e.icon&&Object(a.jsx)("div",{className:"ic",children:Object(a.jsx)("i",{className:"lni lni-"+e.icon})}),Object(a.jsx)("select",{ref:e.localref,type:e.type,placeholder:e.title,required:e.required,onChange:t,name:e.name,children:e.options.map((function(e){return Object(a.jsx)("option",{value:e.value,selected:e.selected,children:e.text})}))})]})},N=n(374);n(368);function V(e){return C.a.fetch(C.a.host+"getItems/"+e.join("/"),{method:"GET"}).then((function(e){return e.json()}))}function _(e){return C.a.fetch(C.a.host+"getItemsFiltered/"+e.join("/"),{method:"GET"}).then((function(e){return e.json()}))}var F=n(375),T=n(373),E=n(376),R=n(377),A=n(378),P=n(379),q=n(382),B=n(381),M={};function D(e,t,n){if(!M[t])return M[t]={scroll:0,items:e,start:n};0===n?M[t].items=e:(M[t].start=n,M[t].items=[].concat(Object(o.a)(M[t].items),Object(o.a)(e.filter((function(e){return-1===M[t].items.indexOf(e.id)})))))}t.default=function(e){var t,n=200,r=Object(l.useState)(!1),u=Object(s.a)(r,2),d=u[0],j=u[1],f=Object(l.useState)(!1),b=Object(s.a)(f,2),p=b[0],m=b[1],O=Object(g.h)().pathname.replace("/items/",""),v=Object(l.useRef)(null),k=Object(l.useRef)(null),S=Object(l.useRef)(null),G=Object(l.useRef)(null),L=Object(l.useState)(null),W=Object(s.a)(L,2),Y=W[0],J=W[1],z=Object(l.useState)(null),K=Object(s.a)(z,2),Z=K[0],H=K[1],Q=Object(l.useState)(M[O]?M[O].items:[]),U=Object(s.a)(Q,2),X=U[0],$=U[1],ee=Object(l.useState)(M[O]?M[O].start:0),te=Object(s.a)(ee,2),ne=te[0],ae=te[1],re=function(e){var t,n=void 0===e?{}:e,a=n.threshold,r=n.delay,i=n.trackVisibility,c=n.rootMargin,s=n.root,o=n.triggerOnce,u=n.skip,d=n.initialInView,j=n.fallbackInView,f=n.onChange,b=l.useState(null),p=b[0],m=b[1],O=l.useRef(),v=l.useState({inView:!!d,entry:void 0}),x=v[0],y=v[1];O.current=f,l.useEffect((function(){if(!u&&p){var e=h(p,(function(t,n){y({inView:t,entry:n}),O.current&&O.current(t,n),n.isIntersecting&&o&&e&&(e(),e=void 0)}),{root:s,rootMargin:c,threshold:a,trackVisibility:i,delay:r},j);return function(){e&&e()}}}),[Array.isArray(a)?a.toString():a,p,s,c,o,u,i,j,r]);var g=null==(t=x.entry)?void 0:t.target;l.useEffect((function(){p||!g||o||u||y({inView:!!d,entry:void 0})}),[p,g,o,u,d]);var w=[m,x.inView,x.entry];return w.ref=w[0],w.inView=w[1],w.entry=w[2],w}({triggerOnce:!1,rootMargin:"0px 0px"}),ie=Object(s.a)(re,2),ce=ie[0],se=ie[1];function oe(){return(oe=Object(c.a)(i.a.mark((function e(){var t,a,r,c,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!p){e.next=2;break}return e.abrupt("return",[]);case 2:if(d){e.next=19;break}j(!0),t=le(t=v.current.value),a=k.current.value,r="all",c="all";try{r=S.current.value,c=G.current.value}catch(i){}return e.next=12,V([ne,n,O,c,r,a,t]);case 12:return D(s=e.sent,O,ne+n),$([].concat(Object(o.a)(X),Object(o.a)(s))),j(!1),ae(ne+n),s.length<n&&m(!0),e.abrupt("return",s);case 19:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function le(e){if("string"!=typeof e)return"";e=(e=(e=(e=(e=e.replace(/([^\u0621-\u063A\u0641-\u064A\u0660-\u0669a-zA-Z 0-9])/g,"")).replace(/(\u0622|\u0625|\u0623)/g,"\u0627")).replace(/(\u0629)/g,"\u0647")).replace(/(\u0626|\u0624)/g,"\u0621")).replace(/(\u0649)/g,"\u064a");for(var t=0;t<10;t++)e.replace(String.fromCharCode(1632+t),String.fromCharCode(48+t));return e=e.toLocaleLowerCase()}function ue(){return de.apply(this,arguments)}function de(){return(de=Object(c.a)(i.a.mark((function e(){var t,a,r,c,s;return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=le(t=v.current.value),a=k.current.value,r="all",c="all";try{r=S.current.value,c=G.current.value}catch(i){}return ae(0),e.next=9,_([0,n,O,c,r,a,t]);case 9:s=e.sent,$(s),j(!1);case 12:case"end":return e.stop()}}),e)})))).apply(this,arguments)}return Object(l.useEffect)((function(){se&&function(){oe.apply(this,arguments)}()}),[se]),Object(l.useEffect)((function(){setTimeout(Object(c.a)(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.t0=J,e.next=3,t=O,C.a.fetch(C.a.host+"getSecType/"+t,{method:"GET"}).then((function(e){return e.json()}));case 3:e.t1=e.sent,(0,e.t0)(e.t1);case 5:case"end":return e.stop()}var t}),e)}))),100),M[O]?window.scrollTo({top:M[O].scroll}):M[O]={scroll:0,items:[],start:ne}}),[O]),Object(l.useEffect)((function(){function e(){M[O]||(M[O]={scroll:0,items:[],start:ne}),0!==window.pageYOffset&&(M[O].scroll=window.pageYOffset)}return window.addEventListener("scroll",e),function(){window.removeEventListener("scroll",e)}}),[]),Object(a.jsxs)("div",{children:[Object(a.jsx)(x.a,{}),Object(a.jsx)(B.a,{padding:!0}),Object(a.jsxs)("div",{className:"items-count ",children:[Object(a.jsx)("div",{className:"bg-all"}),Object(a.jsx)("div",{className:"search-bar",children:Object(a.jsx)(y.Grid,{style:{padding:0},children:Object(a.jsxs)(y.Row,{children:[Y&&("movies"==Y.type||"serieses"==Y.type)&&Object(a.jsxs)(a.Fragment,{children:[Object(a.jsx)(y.Col,{xs:12,md:2,children:Object(a.jsx)(I,{onChange:function(){ue()},title:"\u0633\u0646\u0629 \u0627\u0644\u0627\u0646\u062a\u0627\u062c",options:[{text:"\u0627\u0644\u0643\u0644",value:"all"}].concat(Object(o.a)(new Array(30).fill(0).map((function(e,t){return{text:(new Date).getFullYear()-25+t,value:(new Date).getFullYear()-25+t}})))).concat(),type:"text",icon:"calendar",required:!1,name:"",localref:S,style:{width:"90%"}})}),Object(a.jsx)(y.Col,{xs:12,md:2,children:Object(a.jsx)(I,{onChange:function(){ue()},localref:G,title:"\u0627\u062e\u062a\u0631 \u0627\u0644\u0641\u0626\u0629",options:[{value:"all",text:"\u0627\u0644\u0643\u0644"},{value:"\u062d\u0631\u0643\u0629",text:"\u062d\u0631\u0643\u0629"},{value:"\u0627\u0646\u0645\u064a\u0634\u0646",text:"\u0627\u0646\u0645\u064a\u0634\u0646"},{value:"\u062e\u064a\u0627\u0644 \u0639\u0644\u0645\u064a",text:"\u062e\u064a\u0627\u0644 \u0639\u0644\u0645\u064a"},{value:"\u0645\u063a\u0627\u0645\u0631\u0629",text:"\u0645\u063a\u0627\u0645\u0631\u0629"},{value:"\u062d\u0631\u0628",text:"\u062d\u0631\u0628"},{value:"\u0633\u064a\u0631\u0629 \u0630\u0627\u062a\u064a\u0629",text:"\u0633\u064a\u0631\u0629 \u0630\u0627\u062a\u064a\u0629"},{value:"\u0643\u0648\u0645\u064a\u062f\u064a\u0627",text:"\u0643\u0648\u0645\u064a\u062f\u064a\u0627"},{value:"\u062c\u0631\u064a\u0645\u0629",text:"\u062c\u0631\u064a\u0645\u0629"},{value:"\u062f\u0631\u0627\u0645\u0627",text:"\u062f\u0631\u0627\u0645\u0627"},{value:"\u062e\u064a\u0627\u0644",text:"\u062e\u064a\u0627\u0644"},{value:"\u062a\u0627\u0631\u064a\u062e\u064a",text:"\u062a\u0627\u0631\u064a\u062e\u064a"},{value:"\u0631\u0639\u0628",text:"\u0631\u0639\u0628"},{value:"\u0648\u0627\u0642\u0639\u064a\u0629",text:"\u0648\u0627\u0642\u0639\u064a\u0629"},{value:"\u063a\u0645\u0648\u0636",text:"\u063a\u0645\u0648\u0636"},{value:"\u0630\u0639\u0631",text:"\u0630\u0639\u0631"},{value:"\u0641\u0644\u0633\u0641\u0629",text:"\u0641\u0644\u0633\u0641\u0629"},{value:"\u0631\u0648\u0645\u0627\u0646\u0633\u064a",text:"\u0631\u0648\u0645\u0627\u0646\u0633\u064a"},{value:"\u0642\u0635\u0629 \u0637\u0648\u064a\u0644\u0629",text:"\u0642\u0635\u0629 \u0637\u0648\u064a\u0644\u0629"},{value:"\u062e\u064a\u0627\u0644",text:"\u062e\u064a\u0627\u0644"},{value:"\u0645\u0648\u0633\u064a\u0642\u064a",text:"\u0645\u0648\u0633\u064a\u0642\u064a"},{value:"\u0631\u064a\u0627\u0636\u064a",text:"\u0631\u064a\u0627\u0636\u064a"}],type:"text",icon:"tag",required:!1,name:"",style:{width:"90%"}})})]}),Object(a.jsx)(y.Col,{xs:12,md:6,children:Object(a.jsx)(w.a,{localref:v,title:"\u0627\u062f\u062e\u0644 \u0643\u0645\u0644\u0629 \u0644\u0644\u0628\u062d\u062b",type:"text",required:!1,icon:"search",name:"",onChange:function(e){$([]),j(!0),null!=Z&&clearTimeout(Z),H(setTimeout(Object(c.a)(i.a.mark((function e(){return i.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:ue(),H(null);case 2:case"end":return e.stop()}}),e)}))),500))},style:{width:"90%"}})}),Object(a.jsx)(y.Col,{xs:12,md:2,children:Object(a.jsx)(I,{onChange:function(){ue()},localref:k,title:"\u062a\u0631\u062a\u064a\u0628 \u062d\u0633\u0628",options:[{value:"recent",text:"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0627\u0636\u0627\u0641\u0629"},{value:"year",text:"\u0633\u0646\u0629 \u0627\u0644\u0627\u0646\u062a\u0627\u062c"},{value:"ratingImDB",text:"\u0627\u0644\u062a\u0642\u064a\u064a\u0645"},{value:"alpha",text:"\u062d\u0633\u0628 \u0627\u0644\u0627\u062d\u0631\u0641"}],type:"text",icon:"database",required:!1,name:"",style:{width:"90%"}})})]})})}),Y&&Object(a.jsx)(F.a,{icon:"play",title:Y.name||"\u0644\u0627\u064a\u0648\u062c\u062f \u0639\u0646\u0648\u0627\u0646",number:Y.count&&Y.count.views||(t=Y.type,"movies"==t?"\u0627\u0641\u0644\u0627\u0645":"serieses"==t?"\u0645\u0633\u0644\u0633\u0644\u0627\u062a":"serieses.sports"==t?" \u0631\u064a\u0627\u0636\u064a\u0629":"serieses.deen"==t?"\u0645\u0633\u0644\u0633\u0644\u0627\u062a \u062f\u064a\u0646\u064a\u0629":"serieses.ramadan"==t?"\u0645\u0633\u0644\u0633\u0644\u0627\u062a \u0631\u0645\u0636\u0627\u0646\u064a\u0629":"serieses.tv"==t?"\u0628\u0631\u0627\u0645\u062c \u062a\u0644\u0641\u0632\u064a\u0648\u0646\u064a\u0629":"serieses.learn"==t?"\u062a\u0639\u0644\u064a\u0645 \u0648\u062a\u062f\u0631\u064a\u0628":"serieses.kids"==t?"\u0645\u0633\u0644\u0633\u0644\u0627\u062a \u0627\u0637\u0641\u0627\u0644":"serieses.anime"==t?"\u0645\u0633\u0644\u0633\u0644\u0627\u062a \u0627\u0646\u0645\u064a":"vidsSameFolder"==t?"\u0641\u064a\u062f\u064a\u0648":"books"==t?"\u0643\u062a\u0628":"apps"==t?"\u062a\u0637\u0628\u064a\u0642\u0627\u062a":"imagesSameFolder"==t?"\u0635\u0648\u0631":"audioSameFolder"==t?"\u0635\u0648\u062a\u064a\u0627\u062a":"appsSameFolder"==t?"\u062a\u0637\u0628\u064a\u0642\u0627\u062a":"booksSameFolder"==t?"\u0643\u062a\u0628":"albums"==t?"\u0635\u0648\u062a\u064a\u0627\u062a":t),isVIP:"yes"===Y.isVIP}),Object(a.jsx)(y.Grid,{style:{padding:0},children:Object(a.jsx)(y.Row,{children:X&&X.map((function(e,t){return Object(a.jsxs)(y.Col,{xs:window.innerWidth>490?4:6,md:window.innerWidth<1200?3:2,children:[("app"===e.type||"appsSameFolder"===e.type)&&Object(a.jsx)("div",{style:{marginBottom:40,display:"inline-table"},children:Object(a.jsx)(E.a,{item:e,centerIcon:"download"})}),"books"===e.type||"booksSameFolder"===e.type&&Object(a.jsx)(R.a,{item:e,centerIcon:"link"}),"imagesSameFolder"===e.type&&Object(a.jsx)(A.a,{item:e,centerIcon:"download"}),("audioSameFolder"===e.type||"singer"===e.type||"album"===e.type)&&Object(a.jsx)(P.a,{item:e,centerIcon:"play"}),Object(q.a)(e.type)&&Object(a.jsx)("div",{style:{marginBottom:40,display:"inline-table"},children:Object(a.jsx)(N.a,{item:e,centerIcon:"play"})}),"vidsSameFolder"===e.type&&Object(a.jsx)("div",{style:{marginBottom:10,display:"inline-table"},children:Object(a.jsx)(T.a,{item:e,centerIcon:"play"})}),("movie"===e.type||"movies"===e.type)&&Object(a.jsx)("div",{style:{marginBottom:40,display:"inline-table"},children:Object(a.jsx)(N.a,{item:e,centerIcon:"play"},e.id)})]},e.name+"items")}))})}),!p&&Object(a.jsx)("div",{children:Object(a.jsx)("div",{className:"bottom",ref:ce,children:"\u064a\u062a\u0645 \u0627\u0644\u062a\u062d\u0645\u064a\u0644 \u0627\u0646\u062a\u0638\u0631 \u0642\u0644\u064a\u0644\u0627\u064b ..."})}),p&&d&&Object(a.jsx)("div",{style:{padding:20},children:"\u064a\u062a\u0645 \u0627\u0644\u062a\u062d\u0645\u064a\u0644 \u0627\u0646\u062a\u0638\u0631 \u0642\u0644\u064a\u0644\u0627\u064b ..."}),p&&Object(a.jsx)("div",{className:"loading-place",children:"\u0644\u0627\u064a\u0648\u062c\u062f \u0627\u0644\u0645\u0632\u064a\u062f"})]}),Object(a.jsx)("div",{style:{clear:"both"}})]})}}}]);