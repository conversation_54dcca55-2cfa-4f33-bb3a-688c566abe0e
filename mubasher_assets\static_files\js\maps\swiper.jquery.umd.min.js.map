{"version": 3, "sources": ["swiper.jquery.umd.js"], "names": ["root", "factory", "define", "amd", "exports", "module", "require", "Swiper", "j<PERSON><PERSON><PERSON>", "this", "$", "addLibraryPlugin", "lib", "fn", "swiper", "params", "firstInstance", "each", "s", "container", "round", "a", "Math", "floor", "autoplay", "autoplayDelay", "activeSlide", "slides", "eq", "activeIndex", "attr", "autoplayTimeoutId", "setTimeout", "loop", "fixLoop", "_slideNext", "emit", "isEnd", "autoplayStopOnLast", "stopAutoplay", "_slideTo", "findElementInEvent", "e", "selector", "el", "target", "is", "parents", "nodeType", "found", "index", "_el", "length", "initObserver", "options", "ObserverFunc", "window", "MutationObserver", "WebkitMutationObserver", "observer", "mutations", "for<PERSON>ach", "mutation", "onResize", "observe", "attributes", "childList", "characterData", "observers", "push", "handleKeyboard", "originalEvent", "kc", "keyCode", "charCode", "allowSwipeToNext", "isHorizontal", "allowSwipeToPrev", "shift<PERSON>ey", "altKey", "ctrl<PERSON>ey", "metaKey", "document", "activeElement", "nodeName", "toLowerCase", "inView", "slideClass", "slideActiveClass", "windowScroll", "left", "pageXOffset", "top", "pageYOffset", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "swiperOffset", "offset", "rtl", "scrollLeft", "swiperCoord", "width", "height", "i", "point", "preventDefault", "returnValue", "slideNext", "slidePrev", "isEventSupported", "eventName", "isSupported", "element", "createElement", "setAttribute", "implementation", "hasFeature", "handleMousewheel", "delta", "rtlFactor", "data", "normalizeWheel", "mousewheelForceToAxis", "abs", "pixelX", "pixelY", "mousewheelInvert", "freeMode", "position", "getWrapperTranslate", "mousewheelSensitivity", "wasBeginning", "isBeginning", "wasEnd", "minTranslate", "maxTranslate", "setWrapperTransition", "setWrapperTranslate", "updateProgress", "updateActiveIndex", "updateClasses", "freeModeSticky", "clearTimeout", "mousewheel", "timeout", "slideReset", "lazyLoading", "lazy", "load", "autoplayDisableOnInteraction", "Date", "getTime", "lastScrollTime", "animating", "mousewheelReleaseOnEdges", "event", "PIXEL_STEP", "LINE_HEIGHT", "PAGE_HEIGHT", "sX", "sY", "pX", "pY", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "axis", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "spinX", "spinY", "setParallaxTransform", "progress", "p", "indexOf", "parseInt", "transform", "normalizeEventName", "toUpperCase", "substring", "defaults", "direction", "touchEventsTarget", "initialSlide", "speed", "iOSEdgeSwipeDetection", "iOSEdgeSwipeThreshold", "freeModeMomentum", "freeModeMomentumRatio", "freeModeMomentumBounce", "freeModeMomentumBounceRatio", "freeModeMomentumVelocityRatio", "freeModeMinimumVelocity", "autoHeight", "setWrapperSize", "virtualTranslate", "effect", "coverflow", "rotate", "stretch", "depth", "modifier", "slideShadows", "flip", "limitRotation", "cube", "shadow", "shadowOffset", "shadowScale", "fade", "crossFade", "parallax", "zoom", "zoomMax", "zoomMin", "zoomToggle", "scrollbar", "scrollbarHide", "scrollbarDraggable", "scrollbarSnapOnRelease", "keyboardControl", "mousewheelControl", "mousewheelEventsTarged", "<PERSON><PERSON><PERSON>", "hashnavWatchState", "history", "replaceState", "breakpoints", "undefined", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerColumn", "slidesPerColumnFill", "slidesPerGroup", "centeredSlides", "slidesOffsetBefore", "slidesOffsetAfter", "roundLengths", "touchRatio", "touchAngle", "simulate<PERSON>ouch", "shortSwipes", "longSwipes", "longSwipesRatio", "longSwipesMs", "follow<PERSON><PERSON>", "onlyEx<PERSON><PERSON>", "threshold", "touchMoveStopPropagation", "touchReleaseOnEdges", "uniqueNavElements", "pagination", "paginationElement", "paginationClickable", "paginationHide", "paginationBulletRender", "paginationProgressRender", "paginationFractionRender", "paginationCustomRender", "paginationType", "resistance", "resistanceRatio", "nextButton", "prevButton", "watchSlidesProgress", "watchSlidesVisibility", "grabCursor", "preventClicks", "preventClicksPropagation", "slideToClickedSlide", "lazyLoadingInPrevNext", "lazyLoadingInPrevNextAmount", "lazyLoadingOnTransitionStart", "preloadImages", "updateOnImagesReady", "loopAdditionalSlides", "loopedSlides", "control", "controlInverse", "controlBy", "normalizeSlideIndex", "swi<PERSON><PERSON><PERSON><PERSON>", "noSwiping", "noSwipingClass", "passiveListeners", "containerModifierClass", "slideDuplicateActiveClass", "slideVisibleClass", "slideDuplicateClass", "slideNextClass", "slideDuplicateNextClass", "slidePrevClass", "slideDuplicatePrevClass", "wrapperClass", "bulletClass", "bulletActiveClass", "buttonDisabledClass", "paginationCurrentClass", "paginationTotalClass", "paginationHiddenClass", "paginationProgressbarClass", "paginationClickableClass", "paginationModifierClass", "lazyLoadingClass", "lazyStatusLoadingClass", "lazyStatusLoadedClass", "lazyPreloaderClass", "notificationClass", "preloaderClass", "zoomContainerClass", "observeParents", "a11y", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "paginationBulletMessage", "runCallbacksOnInit", "initialVirtualTranslate", "originalParams", "param", "Dom7", "deepParam", "def", "deepDef", "classNames", "Zepto", "currentBreakpoint", "getActiveBreakpoint", "breakpoint", "points", "hasOwnProperty", "sort", "b", "setBreakpoint", "breakPointsParams", "needsReLoop", "destroyLoop", "reLoop", "swipers", "support", "flexbox", "transforms3d", "touch", "wrapper", "children", "paginationContainer", "find", "addClass", "dir", "css", "wrongRTL", "device", "android", "join", "translate", "velocity", "lockSwipeToNext", "unsetGrabCursor", "lockSwipeToPrev", "lockSwipes", "unlockSwipeToNext", "setGrabCursor", "unlockSwipeToPrev", "unlockSwipes", "moving", "style", "cursor", "imagesToLoad", "imagesLoaded", "loadImage", "imgElement", "src", "srcset", "sizes", "checkForComplete", "callback", "onReady", "image", "complete", "Image", "onload", "onerror", "_onReady", "update", "currentSrc", "getAttribute", "autoplaying", "autoplayPaused", "startAutoplay", "internal", "pauseAutoplay", "transitionEnd", "snapGrid", "updateAutoHeight", "activeSlides", "newHeight", "ceil", "offsetHeight", "updateContainerSize", "clientWidth", "clientHeight", "size", "updateSlidesSize", "slidesGrid", "slidesSizesGrid", "slidePosition", "prevSlideSize", "parseFloat", "replace", "virtualSize", "marginLeft", "marginTop", "marginRight", "marginBottom", "slidesNumberEvenToRows", "max", "slideSize", "slidesPerRow", "numFullColumns", "slide", "newSlideOrderIndex", "column", "row", "-webkit-box-ordinal-group", "-moz-box-ordinal-group", "-ms-flex-order", "-webkit-order", "order", "outerWidth", "outerHeight", "swiperSlideSize", "newSlidesGrid", "updateSlidesOffset", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "offsetCenter", "removeClass", "slideProgress", "slideBefore", "slideAfter", "isVisible", "translatesDiff", "newActiveIndex", "snapIndex", "previousIndex", "updateRealIndex", "realIndex", "hasClass", "nextSlide", "next", "prevSlide", "prev", "current", "total", "bullets", "text", "scale", "scaleX", "scaleY", "transition", "html", "disable", "enable", "updatePagination", "paginationHTML", "numberOfBullets", "initPagination", "updateTranslate", "forceSetTranslate", "newTranslate", "min", "set", "translated", "controller", "spline", "slideTo", "forceUpdatePagination", "slideChangedBySlideTo", "touchEventsDesktop", "start", "move", "end", "navigator", "pointer<PERSON><PERSON>bled", "msPointer<PERSON><PERSON><PERSON>", "touchEvents", "initEvents", "detach", "actionDom", "action", "moveCapture", "nested", "browser", "ie", "onTouchStart", "onTouchMove", "onTouchEnd", "passiveListener", "passive", "capture", "ios", "onClickNext", "onEnterKey", "onClickPrev", "onClickIndex", "attachEvents", "detachEvents", "allowClick", "stopPropagation", "stopImmediatePropagation", "updateClickedSlide", "slideFound", "clickedSlide", "clickedIndex", "slideToIndex", "isTouched", "isMoved", "allowTouchCallbacks", "touchStartTime", "isScrolling", "currentTranslate", "startTranslate", "allowThresholdMove", "clickTimeout", "allowMomentumBounce", "formElements", "lastClickTime", "now", "velocities", "touches", "startX", "startY", "currentX", "currentY", "diff", "isTouchEvent", "startMoving", "type", "which", "targetTouches", "pageX", "pageY", "swipeDirection", "blur", "preventedByNestedSwiper", "atan2", "PI", "ieTouch", "trigger", "disableParentSwiper", "pow", "time", "touchEndTime", "timeDiff", "toggleClass", "currentPos", "lastMoveEvent", "pop", "velocityEvent", "distance", "momentumDuration", "momentumDistance", "newPosition", "afterBouncePosition", "doBounce", "bounceAmount", "j", "onTransitionStart", "onTransitionEnd", "stopIndex", "groupSize", "ratio", "slideIndex", "runCallbacks", "lteIE9", "setHistory", "setHash", "clientLeft", "_slidePrev", "disableTouchControl", "enableTouchControl", "duration", "byController", "effects", "setTransition", "x", "y", "z", "setTranslate", "getTranslate", "matrix", "curTransform", "curStyle", "transformMatrix", "getComputedStyle", "WebKitCSSMatrix", "webkitTransform", "split", "map", "MozTransform", "OTransform", "MsTransform", "msTransform", "getPropertyValue", "toString", "m41", "m42", "initObservers", "containerParents", "disconnectObservers", "disconnect", "createLoop", "remove", "prependSlides", "appendSlides", "append", "cloneNode", "prepend", "removeAttr", "updatePosition", "oldIndex", "newIndex", "appendSlide", "prependSlide", "removeSlide", "slidesIndexes", "indexToRemove", "removeAllSlides", "tx", "ty", "slideOpacity", "opacity", "eventTriggered", "triggerEvents", "rotateY", "rotateX", "zIndex", "shadowBefore", "shadowAfter", "cubeShadow", "wrapperRotate", "slideAngle", "tz", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "transform-origin", "shadowAngle", "multiplier", "sin", "cos", "scale1", "scale2", "zFactor", "<PERSON><PERSON><PERSON><PERSON>", "isUiWebView", "center", "slideOffset", "offsetMultiplier", "translateZ", "translateY", "translateX", "slideTransform", "ws", "<PERSON><PERSON><PERSON><PERSON>", "initialImageLoaded", "loadImageInSlide", "loadInDuplicate", "img", "add", "_img", "background", "slideOriginalIndex", "originalSlide", "duplicatedSlide", "amount", "spv", "maxIndex", "minIndex", "setDragPosition", "sb", "pointerPosition", "clientX", "clientY", "track", "dragSize", "positionMin", "moveDivider", "positionMax", "dragStart", "dragTimeout", "drag", "dragMove", "dragEnd", "draggableEvents", "enableDraggable", "on", "disableDraggable", "off", "trackSize", "offsetWidth", "divider", "display", "newPos", "newSize", "LinearSpline", "lastIndex", "i1", "i3", "interpolate", "x2", "binarySearch", "guess", "array", "val", "getInterpolateFunction", "c", "setControlledTranslate", "controlledTranslate", "controlled", "isArray", "setControlledTransition", "onHashCange", "newHash", "location", "hash", "activeSlideHash", "initialized", "init", "slideHash", "destroy", "pushState", "paths", "get<PERSON>ath<PERSON><PERSON><PERSON>", "key", "value", "scrollToSlide", "addEventListener", "setHistoryPopState", "pathArray", "pathname", "slice", "slugify", "includes", "slideHistory", "disableKeyboardControl", "enableKeyboardControl", "userAgent", "disableMousewheelControl", "enableMousewheelControl", "parallaxDuration", "currentScale", "isScaling", "gesture", "slideWidth", "slideHeight", "imageWrap", "minX", "minY", "maxX", "maxY", "touchesStart", "touchesCurrent", "prevPositionX", "prevPositionY", "prevTime", "getDistanceBetweenTouches", "x1", "y1", "y2", "sqrt", "onGestureStart", "gestures", "scaleStart", "parent", "onGestureChange", "scaleMove", "onGestureEnd", "changedTouches", "os", "scaledWidth", "scaledHeight", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "toggleZoom", "touchX", "touchY", "offsetX", "offsetY", "diffX", "diffY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "_plugins", "plugin", "plugins", "callPlugins", "arguments", "emitterEventListeners", "handler", "splice", "once", "_handler", "makeFocusable", "$el", "addRole", "role", "addLabel", "label", "notify", "click", "liveRegion", "message", "notification", "bullet", "hashnavReplaceState", "cleanupStyles", "deleteInstance", "removeEventListener", "prototype", "ua", "test", "arr", "Object", "apply", "msMaxTouchPoints", "maxTouchPoints", "div", "innerHTML", "getElementsByTagName", "match", "ipad", "ipod", "iphone", "Modernizr", "DocumentTouch", "csstransforms3d", "styles", "supportsPassive", "opts", "defineProperty", "get", "domLib", "fireCallBack", "call", "events", "dom", "elStyle", "webkitTransitionDuration", "MsTransitionDuration", "msTransitionDuration", "MozTransitionDuration", "OTransitionDuration", "transitionDuration", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;CAcC,SAAUA,EAAMC,GAChB,YAEsB,mBAAXC,SAAyBA,OAAOC,IAE1CD,QAAQ,UAAWD,GACU,gBAAZG,SAIjBC,OAAOD,QAAUH,EAAQK,QAAQ,WAGjCN,EAAKO,OAASN,EAAQD,EAAKQ,SAE3BC,KAAM,SAAUC,GACjB,YA26IG,SAASC,GAAiBC,GACtBA,EAAIC,GAAGC,OAAS,SAAUC,GACtB,GAAIC,EAKJ,OAJAJ,GAAIH,MAAMQ,KAAK,WACX,GAAIC,GAAI,GAAIX,GAAOE,KAAMM,EACpBC,KAAeA,EAAgBE,KAEjCF,GA76If,GAAIT,GAAS,SAAUY,EAAWJ,GAqgB9B,QAASK,GAAMC,GACX,MAAOC,MAAKC,MAAMF,GAuEtB,QAASG,KACL,GAAIC,GAAgBP,EAAEH,OAAOS,SACzBE,EAAcR,EAAES,OAAOC,GAAGV,EAAEW,YAC5BH,GAAYI,KAAK,0BACjBL,EAAgBC,EAAYI,KAAK,yBAA2BZ,EAAEH,OAAOS,UAEzEN,EAAEa,kBAAoBC,WAAW,WACzBd,EAAEH,OAAOkB,MACTf,EAAEgB,UACFhB,EAAEiB,aACFjB,EAAEkB,KAAK,aAAclB,IAGhBA,EAAEmB,MAKEtB,EAAOuB,mBAKRpB,EAAEqB,gBAJFrB,EAAEsB,SAAS,GACXtB,EAAEkB,KAAK,aAAclB,KANzBA,EAAEiB,aACFjB,EAAEkB,KAAK,aAAclB,KAY9BO,GAmvBP,QAASgB,GAAmBC,EAAGC,GAC3B,GAAIC,GAAKlC,EAAEgC,EAAEG,OACb,KAAKD,EAAGE,GAAGH,GACP,GAAwB,gBAAbA,GACPC,EAAKA,EAAGG,QAAQJ,OAEf,IAAIA,EAASK,SAAU,CACxB,GAAIC,EAIJ,OAHAL,GAAGG,UAAU9B,KAAK,SAAUiC,EAAOC,GAC3BA,IAAQR,IAAUM,EAAQN,KAE7BM,EACON,EADA,OAIpB,GAAkB,IAAdC,EAAGQ,OAGP,MAAOR,GAAG,GAk2Bd,QAASS,GAAaR,EAAQS,GAC1BA,EAAUA,KAEV,IAAIC,GAAeC,OAAOC,kBAAoBD,OAAOE,uBACjDC,EAAW,GAAIJ,GAAa,SAAUK,GACtCA,EAAUC,QAAQ,SAAUC,GACxB5C,EAAE6C,UAAS,GACX7C,EAAEkB,KAAK,mBAAoBlB,EAAG4C,MAItCH,GAASK,QAAQnB,GACboB,WAA0C,mBAAvBX,GAAQW,YAAoCX,EAAQW,WACvEC,UAAwC,mBAAtBZ,GAAQY,WAAmCZ,EAAQY,UACrEC,cAAgD,mBAA1Bb,GAAQa,eAAuCb,EAAQa,gBAGjFjD,EAAEkD,UAAUC,KAAKV,GAm+BrB,QAASW,GAAe5B,GAChBA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,cAC3B,IAAIC,GAAK9B,EAAE+B,SAAW/B,EAAEgC,QAExB,KAAKxD,EAAEH,OAAO4D,mBAAqBzD,EAAE0D,gBAAyB,KAAPJ,IAActD,EAAE0D,gBAAyB,KAAPJ,GACrF,OAAO,CAEX,KAAKtD,EAAEH,OAAO8D,mBAAqB3D,EAAE0D,gBAAyB,KAAPJ,IAActD,EAAE0D,gBAAyB,KAAPJ,GACrF,OAAO,CAEX,MAAI9B,EAAEoC,UAAYpC,EAAEqC,QAAUrC,EAAEsC,SAAWtC,EAAEuC,SAGzCC,SAASC,eAAiBD,SAASC,cAAcC,WAA+D,UAAlDF,SAASC,cAAcC,SAASC,eAA+E,aAAlDH,SAASC,cAAcC,SAASC,gBAA/J,CAGA,GAAW,KAAPb,GAAoB,KAAPA,GAAoB,KAAPA,GAAoB,KAAPA,EAAW,CAClD,GAAIc,IAAS,CAEb,IAAIpE,EAAEC,UAAU4B,QAAQ,IAAM7B,EAAEH,OAAOwE,YAAYnC,OAAS,GAAqE,IAAhElC,EAAEC,UAAU4B,QAAQ,IAAM7B,EAAEH,OAAOyE,kBAAkBpC,OAClH,MAEJ,IAAIqC,IACAC,KAAMlC,OAAOmC,YACbC,IAAKpC,OAAOqC,aAEZC,EAActC,OAAOuC,WACrBC,EAAexC,OAAOyC,YACtBC,EAAehF,EAAEC,UAAUgF,QAC3BjF,GAAEkF,MAAKF,EAAaR,KAAOQ,EAAaR,KAAOxE,EAAEC,UAAU,GAAGkF,WAOlE,KAAK,GANDC,KACCJ,EAAaR,KAAMQ,EAAaN,MAChCM,EAAaR,KAAOxE,EAAEqF,MAAOL,EAAaN,MAC1CM,EAAaR,KAAMQ,EAAaN,IAAM1E,EAAEsF,SACxCN,EAAaR,KAAOxE,EAAEqF,MAAOL,EAAaN,IAAM1E,EAAEsF,SAE9CC,EAAI,EAAGA,EAAIH,EAAYlD,OAAQqD,IAAK,CACzC,GAAIC,GAAQJ,EAAYG,EAEpBC,GAAM,IAAMjB,EAAaC,MAAQgB,EAAM,IAAMjB,EAAaC,KAAOI,GACjEY,EAAM,IAAMjB,EAAaG,KAAOc,EAAM,IAAMjB,EAAaG,IAAMI,IAE/DV,GAAS,GAIjB,IAAKA,EAAQ,OAEbpE,EAAE0D,gBACS,KAAPJ,GAAoB,KAAPA,IACT9B,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,IAEb,KAAPpC,IAActD,EAAEkF,KAAgB,KAAP5B,GAAatD,EAAEkF,MAAMlF,EAAE2F,aACzC,KAAPrC,IAActD,EAAEkF,KAAgB,KAAP5B,GAAatD,EAAEkF,MAAMlF,EAAE4F,cAG1C,KAAPtC,GAAoB,KAAPA,IACT9B,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,GAEd,KAAPpC,GAAWtD,EAAE2F,YACN,KAAPrC,GAAWtD,EAAE4F,cAgCzB,QAASC,KACL,GAAIC,GAAY,UACZC,EAAcD,IAAa9B,SAE/B,KAAK+B,EAAa,CACd,GAAIC,GAAUhC,SAASiC,cAAc,MACrCD,GAAQE,aAAaJ,EAAW,WAChCC,EAA4C,kBAAvBC,GAAQF,GAajC,OAVKC,GACD/B,SAASmC,gBACTnC,SAASmC,eAAeC,YAGxBpC,SAASmC,eAAeC,WAAW,GAAI,OAAQ,IAE/CL,EAAc/B,SAASmC,eAAeC,WAAW,eAAgB,QAG9DL,EAGX,QAASM,GAAiB7E,GAClBA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,cAC3B,IAAIiD,GAAQ,EACRC,EAAYvG,EAAEkF,KAAM,EAAK,EAEzBsB,EAAOC,EAAgBjF,EAE3B,IAAIxB,EAAEH,OAAO6G,sBACT,GAAI1G,EAAE0D,eAAgB,CAClB,KAAItD,KAAKuG,IAAIH,EAAKI,QAAUxG,KAAKuG,IAAIH,EAAKK,SACrC,MAD8CP,GAAQE,EAAKI,OAASL,MAGxE,CACD,KAAInG,KAAKuG,IAAIH,EAAKK,QAAUzG,KAAKuG,IAAIH,EAAKI,SACrC,MAD8CN,GAAQE,EAAKK,WAKpEP,GAAQlG,KAAKuG,IAAIH,EAAKI,QAAUxG,KAAKuG,IAAIH,EAAKK,SAAYL,EAAKI,OAASL,GAAcC,EAAKK,MAG/F,IAAc,IAAVP,EAAJ,CAIA,GAFItG,EAAEH,OAAOiH,mBAAkBR,GAASA,GAEnCtG,EAAEH,OAAOkH,SAoBT,CAED,GAAIC,GAAWhH,EAAEiH,sBAAwBX,EAAQtG,EAAEH,OAAOqH,sBACtDC,EAAenH,EAAEoH,YACjBC,EAASrH,EAAEmB,KAgCf,IA9BI6F,GAAYhH,EAAEsH,iBAAgBN,EAAWhH,EAAEsH,gBAC3CN,GAAYhH,EAAEuH,iBAAgBP,EAAWhH,EAAEuH,gBAE/CvH,EAAEwH,qBAAqB,GACvBxH,EAAEyH,oBAAoBT,GACtBhH,EAAE0H,iBACF1H,EAAE2H,sBAEGR,GAAgBnH,EAAEoH,cAAgBC,GAAUrH,EAAEmB,QAC/CnB,EAAE4H,gBAGF5H,EAAEH,OAAOgI,gBACTC,aAAa9H,EAAE+H,WAAWC,SAC1BhI,EAAE+H,WAAWC,QAAUlH,WAAW,WAC9Bd,EAAEiI,cACH,MAGCjI,EAAEH,OAAOqI,aAAelI,EAAEmI,MAC1BnI,EAAEmI,KAAKC,OAIfpI,EAAEkB,KAAK,WAAYlB,EAAGwB,GAGlBxB,EAAEH,OAAOS,UAAYN,EAAEH,OAAOwI,8BAA8BrI,EAAEqB,eAGjD,IAAb2F,GAAkBA,IAAahH,EAAEuH,eAAgB,WAxDjC,CACpB,IAAI,GAAKjF,QAAOgG,MAAQC,UAAYvI,EAAE+H,WAAWS,eAAiB,GAC9D,GAAIlC,EAAQ,EACR,GAAMtG,EAAEmB,QAASnB,EAAEH,OAAOkB,MAAUf,EAAEyI,WAIjC,GAAIzI,EAAEH,OAAO6I,yBAA0B,OAAO,MAH/C1I,GAAE2F,YACF3F,EAAEkB,KAAK,WAAYlB,EAAGwB,OAK1B,IAAMxB,EAAEoH,cAAepH,EAAEH,OAAOkB,MAAUf,EAAEyI,WAIvC,GAAIzI,EAAEH,OAAO6I,yBAA0B,OAAO,MAH/C1I,GAAE4F,YACF5F,EAAEkB,KAAK,WAAYlB,EAAGwB,EAKlCxB,GAAE+H,WAAWS,gBAAiB,GAAKlG,QAAOgG,MAAQC,UA4CtD,MAFI/G,GAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,GACd,GA0HX,QAASe,GAA2BkC,GAEhC,GAAIC,GAAa,GACbC,EAAc,GACdC,EAAc,IAEdC,EAAK,EAAGC,EAAK,EACbC,EAAK,EAAGC,EAAK,CAkDjB,OA/CI,UAAYP,KACZK,EAAKL,EAAMQ,QAEX,cAAgBR,KAChBK,GAAML,EAAMS,WAAa,KAEzB,eAAiBT,KACjBK,GAAML,EAAMU,YAAc,KAE1B,eAAiBV,KACjBI,GAAMJ,EAAMW,YAAc,KAI1B,QAAUX,IAASA,EAAMY,OAASZ,EAAMa,kBACxCT,EAAKC,EACLA,EAAK,GAGTC,EAAKF,EAAKH,EACVM,EAAKF,EAAKJ,EAEN,UAAYD,KACZO,EAAKP,EAAMc,QAEX,UAAYd,KACZM,EAAKN,EAAMe,SAGVT,GAAMC,IAAOP,EAAMgB,YACI,IAApBhB,EAAMgB,WACNV,GAAMJ,EACNK,GAAML,IAENI,GAAMH,EACNI,GAAMJ,IAKVG,IAAOF,IACPA,EAAME,EAAK,GAAK,EAAK,GAErBC,IAAOF,IACPA,EAAME,EAAK,GAAK,EAAK,IAIrBU,MAAOb,EACPc,MAAOb,EACPpC,OAAQqC,EACRpC,OAAQqC,GAOhB,QAASY,GAAqBpI,EAAIqI,GAC9BrI,EAAKlC,EAAEkC,EACP,IAAIsI,GAAGf,EAAIC,EACP3C,EAAYvG,EAAEkF,KAAM,EAAK,CAE7B8E,GAAItI,EAAGd,KAAK,yBAA2B,IACvCqI,EAAKvH,EAAGd,KAAK,0BACbsI,EAAKxH,EAAGd,KAAK,0BACTqI,GAAMC,GACND,EAAKA,GAAM,IACXC,EAAKA,GAAM,KAGPlJ,EAAE0D,gBACFuF,EAAKe,EACLd,EAAK,MAGLA,EAAKc,EACLf,EAAK,KAKTA,EADA,EAAKgB,QAAQ,MAAQ,EAChBC,SAASjB,EAAI,IAAMc,EAAWxD,EAAY,IAG1C0C,EAAKc,EAAWxD,EAAY,KAGjC2C,EADA,EAAKe,QAAQ,MAAQ,EAChBC,SAAShB,EAAI,IAAMa,EAAW,IAG9Bb,EAAKa,EAAW,KAGzBrI,EAAGyI,UAAU,eAAiBlB,EAAK,KAAOC,EAAK,SAyZnD,QAASkB,GAAoBtE,GASzB,MARgC,KAA5BA,EAAUmE,QAAQ,QAEdnE,EADAA,EAAU,KAAOA,EAAU,GAAGuE,cAClB,KAAOvE,EAAU,GAAGuE,cAAgBvE,EAAUwE,UAAU,GAGxD,KAAOxE,GAGpBA,EAjhIX,KAAMvG,eAAgBF,IAAS,MAAO,IAAIA,GAAOY,EAAWJ,EAE5D,IAAI0K,IACAC,UAAW,aACXC,kBAAmB,YACnBC,aAAc,EACdC,MAAO,IAEPrK,UAAU,EACV+H,8BAA8B,EAC9BjH,oBAAoB,EAEpBwJ,uBAAuB,EACvBC,sBAAuB,GAEvB9D,UAAU,EACV+D,kBAAkB,EAClBC,sBAAuB,EACvBC,wBAAwB,EACxBC,4BAA6B,EAC7BC,8BAA+B,EAC/BrD,gBAAgB,EAChBsD,wBAAyB,IAEzBC,YAAY,EAEZC,gBAAgB,EAEhBC,kBAAkB,EAElBC,OAAQ,QACRC,WACIC,OAAQ,GACRC,QAAS,EACTC,MAAO,IACPC,SAAU,EACVC,cAAe,GAEnBC,MACID,cAAe,EACfE,eAAe,GAEnBC,MACIH,cAAc,EACdI,QAAQ,EACRC,aAAc,GACdC,YAAa,KAEjBC,MACIC,WAAW,GAGfC,UAAU,EAEVC,MAAM,EACNC,QAAS,EACTC,QAAS,EACTC,YAAY,EAEZC,UAAW,KACXC,eAAe,EACfC,oBAAoB,EACpBC,wBAAwB,EAExBC,iBAAiB,EACjBC,mBAAmB,EACnBtE,0BAA0B,EAC1B5B,kBAAkB,EAClBJ,uBAAuB,EACvBQ,sBAAuB,EACvB+F,uBAAwB,YAExBC,SAAS,EACTC,mBAAmB,EAEnBC,SAAS,EAETC,cAAc,EAEdC,YAAaC,OAEbC,aAAc,EACdC,cAAe,EACfC,gBAAiB,EACjBC,oBAAqB,SACrBC,eAAgB,EAChBC,gBAAgB,EAChBC,mBAAoB,EACpBC,kBAAmB,EAEnBC,cAAc,EAEdC,WAAY,EACZC,WAAY,GACZC,eAAe,EACfC,aAAa,EACbC,YAAY,EACZC,gBAAiB,GACjBC,aAAc,IACdC,cAAc,EACdC,cAAc,EACdC,UAAW,EACXC,0BAA0B,EAC1BC,qBAAqB,EAErBC,mBAAmB,EAEnBC,WAAY,KACZC,kBAAmB,OACnBC,qBAAqB,EACrBC,gBAAgB,EAChBC,uBAAwB,KACxBC,yBAA0B,KAC1BC,yBAA0B,KAC1BC,uBAAwB,KACxBC,eAAgB,UAEhBC,YAAY,EACZC,gBAAiB,IAEjBC,WAAY,KACZC,WAAY,KAEZC,qBAAqB,EACrBC,uBAAuB,EAEvBC,YAAY,EAEZC,eAAe,EACfC,0BAA0B,EAC1BC,qBAAqB,EAErB9H,aAAa,EACb+H,uBAAuB,EACvBC,4BAA6B,EAC7BC,8BAA8B,EAE9BC,eAAe,EACfC,qBAAqB,EAErBtP,MAAM,EACNuP,qBAAsB,EACtBC,aAAc,KAEdC,QAASjD,OACTkD,gBAAgB,EAChBC,UAAW,QACXC,qBAAqB,EAErBhN,kBAAkB,EAClBF,kBAAkB,EAClBmN,aAAc,KACdC,WAAW,EACXC,eAAgB,oBAEhBC,kBAAkB,EAElBC,uBAAwB,oBACxB3M,WAAY,eACZC,iBAAkB,sBAClB2M,0BAA2B,gCAC3BC,kBAAmB,uBACnBC,oBAAqB,yBACrBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,aAAc,iBACdC,YAAa,2BACbC,kBAAmB,kCACnBC,oBAAqB,yBACrBC,uBAAwB,4BACxBC,qBAAsB,0BACtBC,sBAAuB,2BACvBC,2BAA4B,gCAC5BC,yBAA0B,8BAC1BC,wBAAyB,qBACzBC,iBAAkB,cAClBC,uBAAwB,sBACxBC,sBAAuB,qBACvBC,mBAAoB,wBACpBC,kBAAmB,sBACnBC,eAAgB,YAChBC,mBAAoB,wBAGpB/P,UAAU,EACVgQ,gBAAgB,EAEhBC,MAAM,EACNC,iBAAkB,iBAClBC,iBAAkB,aAClBC,kBAAmB,0BACnBC,iBAAkB,yBAClBC,wBAAyB,wBAEzBC,oBAAoB,GA8BpBC,EAA0BpT,GAAUA,EAAOyL,gBAE/CzL,GAASA,KACT,IAAIqT,KACJ,KAAK,GAAIC,KAAStT,GACd,GAA6B,gBAAlBA,GAAOsT,IAAyC,OAAlBtT,EAAOsT,KAAqBtT,EAAOsT,GAAOrR,UAAYjC,EAAOsT,KAAW7Q,QAAUzC,EAAOsT,KAAWnP,UAA6B,mBAAToP,OAAwBvT,EAAOsT,YAAkBC,OAA4B,mBAAX9T,SAA0BO,EAAOsT,YAAkB7T,SAOlR4T,EAAeC,GAAStT,EAAOsT,OAP6P,CAC5RD,EAAeC,KACf,KAAK,GAAIE,KAAaxT,GAAOsT,GACzBD,EAAeC,GAAOE,GAAaxT,EAAOsT,GAAOE,GAO7D,IAAK,GAAIC,KAAO/I,GACZ,GAA2B,mBAAhB1K,GAAOyT,GACdzT,EAAOyT,GAAO/I,EAAS+I,OAEtB,IAA2B,gBAAhBzT,GAAOyT,GACnB,IAAK,GAAIC,KAAWhJ,GAAS+I,GACW,mBAAzBzT,GAAOyT,GAAKC,KACnB1T,EAAOyT,GAAKC,GAAWhJ,EAAS+I,GAAKC,GAOrD,IAAIvT,GAAIT,IAcR,IAXAS,EAAEH,OAASA,EACXG,EAAEkT,eAAiBA,EAGnBlT,EAAEwT,cAIe,mBAANhU,IAAqC,mBAAT4T,QACnC5T,EAAI4T,OAES,mBAAN5T,KAEHA,EADgB,mBAAT4T,MACH9Q,OAAO8Q,MAAQ9Q,OAAOmR,OAASnR,OAAOhD,OAGtC8T,SAKZpT,EAAER,EAAIA,EAKNQ,EAAE0T,kBAAoBnG,OACtBvN,EAAE2T,oBAAsB,WAEpB,IAAK3T,EAAEH,OAAOyN,YAAa,OAAO,CAClC,IACiB9H,GADboO,GAAa,EACbC,IACJ,KAAMrO,IAASxF,GAAEH,OAAOyN,YAChBtN,EAAEH,OAAOyN,YAAYwG,eAAetO,IACpCqO,EAAO1Q,KAAKqC,EAGpBqO,GAAOE,KAAK,SAAU5T,EAAG6T,GACrB,MAAO9J,UAAS/J,EAAG,IAAM+J,SAAS8J,EAAG,KAEzC,KAAK,GAAIzO,GAAI,EAAGA,EAAIsO,EAAO3R,OAAQqD,IAC/BC,EAAQqO,EAAOtO,GACXC,GAASlD,OAAOuC,aAAe+O,IAC/BA,EAAapO,EAGrB,OAAOoO,IAAc,OAEzB5T,EAAEiU,cAAgB,WAEd,GAAIL,GAAa5T,EAAE2T,qBACnB,IAAIC,GAAc5T,EAAE0T,oBAAsBE,EAAY,CAClD,GAAIM,GAAoBN,IAAc5T,GAAEH,OAAOyN,YAActN,EAAEH,OAAOyN,YAAYsG,GAAc5T,EAAEkT,eAC9FiB,EAAcnU,EAAEH,OAAOkB,MAASmT,EAAkBzG,gBAAkBzN,EAAEH,OAAO4N,aACjF,KAAM,GAAI0F,KAASe,GACflU,EAAEH,OAAOsT,GAASe,EAAkBf,EAExCnT,GAAE0T,kBAAoBE,EACnBO,GAAenU,EAAEoU,aAChBpU,EAAEqU,QAAO,KAKjBrU,EAAEH,OAAOyN,aACTtN,EAAEiU,gBAMNjU,EAAEC,UAAYT,EAAES,GACW,IAAvBD,EAAEC,UAAUiC,QAAhB,CACA,GAAIlC,EAAEC,UAAUiC,OAAS,EAAG,CACxB,GAAIoS,KAKJ,OAJAtU,GAAEC,UAAUF,KAAK,WAEbuU,EAAQnR,KAAK,GAAI9D,GAAOE,KAAMM,MAE3ByU,EAIXtU,EAAEC,UAAU,GAAGL,OAASI,EACxBA,EAAEC,UAAUuG,KAAK,SAAUxG,GAE3BA,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyBhR,EAAEH,OAAO2K,WAEzDxK,EAAEH,OAAOkH,UACT/G,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,aAEnDhR,EAAEuU,QAAQC,UACXxU,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,cACpDhR,EAAEH,OAAO6N,gBAAkB,GAE3B1N,EAAEH,OAAOuL,YACTpL,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,eAGpDhR,EAAEH,OAAOyM,UAAYtM,EAAEH,OAAO+P,yBAC9B5P,EAAEH,OAAO8P,qBAAsB,GAG/B3P,EAAEH,OAAO+O,sBACT5O,EAAEH,OAAO2P,gBAAkB,IAG1B,OAAQ,YAAa,QAAQvF,QAAQjK,EAAEH,OAAO0L,SAAW,IACtDvL,EAAEuU,QAAQE,cACVzU,EAAEH,OAAO8P,qBAAsB,EAC/B3P,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,OAGpDhR,EAAEH,OAAO0L,OAAS,SAGF,UAApBvL,EAAEH,OAAO0L,QACTvL,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyBhR,EAAEH,OAAO0L,QAEzC,SAApBvL,EAAEH,OAAO0L,SACTvL,EAAEH,OAAO2P,gBAAkB,EAC3BxP,EAAEH,OAAO4N,cAAgB,EACzBzN,EAAEH,OAAO6N,gBAAkB,EAC3B1N,EAAEH,OAAO+N,eAAiB,EAC1B5N,EAAEH,OAAOgO,gBAAiB,EAC1B7N,EAAEH,OAAO2N,aAAe,EACxBxN,EAAEH,OAAOyL,kBAAmB,EAC5BtL,EAAEH,OAAOwL,gBAAiB,GAEN,SAApBrL,EAAEH,OAAO0L,QAAyC,SAApBvL,EAAEH,OAAO0L,SACvCvL,EAAEH,OAAO4N,cAAgB,EACzBzN,EAAEH,OAAO6N,gBAAkB,EAC3B1N,EAAEH,OAAO+N,eAAiB,EAC1B5N,EAAEH,OAAO8P,qBAAsB,EAC/B3P,EAAEH,OAAO2N,aAAe,EACxBxN,EAAEH,OAAOwL,gBAAiB,EACa,mBAA5B4H,KACPjT,EAAEH,OAAOyL,kBAAmB,IAKhCtL,EAAEH,OAAOgQ,YAAc7P,EAAEuU,QAAQG,QACjC1U,EAAEH,OAAOgQ,YAAa,GAI1B7P,EAAE2U,QAAU3U,EAAEC,UAAU2U,SAAS,IAAM5U,EAAEH,OAAO2R,cAG5CxR,EAAEH,OAAOiP,aACT9O,EAAE6U,oBAAsBrV,EAAEQ,EAAEH,OAAOiP,YAC/B9O,EAAEH,OAAOgP,mBAAoD,gBAAxB7O,GAAEH,OAAOiP,YAA2B9O,EAAE6U,oBAAoB3S,OAAS,GAAsD,IAAjDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAOiP,YAAY5M,SACnJlC,EAAE6U,oBAAsB7U,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAOiP,aAGtB,YAA5B9O,EAAEH,OAAOyP,gBAAgCtP,EAAEH,OAAOmP,oBAClDhP,EAAE6U,oBAAoBE,SAAS/U,EAAEH,OAAOoS,wBAA0B,aAGlEjS,EAAEH,OAAOmP,qBAAsB,EAEnChP,EAAE6U,oBAAoBE,SAAS/U,EAAEH,OAAOoS,wBAA0BjS,EAAEH,OAAOyP,kBAG3EtP,EAAEH,OAAO4P,YAAczP,EAAEH,OAAO6P,cAC5B1P,EAAEH,OAAO4P,aACTzP,EAAEyP,WAAajQ,EAAEQ,EAAEH,OAAO4P,YACtBzP,EAAEH,OAAOgP,mBAAoD,gBAAxB7O,GAAEH,OAAO4P,YAA2BzP,EAAEyP,WAAWvN,OAAS,GAAsD,IAAjDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO4P,YAAYvN,SAC1IlC,EAAEyP,WAAazP,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO4P,cAG7CzP,EAAEH,OAAO6P,aACT1P,EAAE0P,WAAalQ,EAAEQ,EAAEH,OAAO6P,YACtB1P,EAAEH,OAAOgP,mBAAoD,gBAAxB7O,GAAEH,OAAO6P,YAA2B1P,EAAE0P,WAAWxN,OAAS,GAAsD,IAAjDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO6P,YAAYxN,SAC1IlC,EAAE0P,WAAa1P,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO6P,eAMrD1P,EAAE0D,aAAe,WACb,MAA8B,eAAvB1D,EAAEH,OAAO2K,WAKpBxK,EAAEkF,IAAMlF,EAAE0D,iBAAwD,QAArC1D,EAAEC,UAAU,GAAG+U,IAAI7Q,eAA4D,QAAjCnE,EAAEC,UAAUgV,IAAI,cACvFjV,EAAEkF,KACFlF,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,OAIpDhR,EAAEkF,MACFlF,EAAEkV,SAAwC,gBAA7BlV,EAAE2U,QAAQM,IAAI,YAI3BjV,EAAEH,OAAO6N,gBAAkB,GAC3B1N,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,YAIpDhR,EAAEmV,OAAOC,SACTpV,EAAEwT,WAAWrQ,KAAKnD,EAAEH,OAAOmR,uBAAyB,WAIxDhR,EAAEC,UAAU8U,SAAS/U,EAAEwT,WAAW6B,KAAK,MAGvCrV,EAAEsV,UAAY,EAGdtV,EAAE+J,SAAW,EAGb/J,EAAEuV,SAAW,EAKbvV,EAAEwV,gBAAkB,WAChBxV,EAAEH,OAAO4D,kBAAmB,EACxBzD,EAAEH,OAAO8D,oBAAqB,GAAS3D,EAAEH,OAAOgQ,YAChD7P,EAAEyV,mBAGVzV,EAAE0V,gBAAkB,WAChB1V,EAAEH,OAAO8D,kBAAmB,EACxB3D,EAAEH,OAAO4D,oBAAqB,GAASzD,EAAEH,OAAOgQ,YAChD7P,EAAEyV,mBAGVzV,EAAE2V,WAAa,WACX3V,EAAEH,OAAO4D,iBAAmBzD,EAAEH,OAAO8D,kBAAmB,EACpD3D,EAAEH,OAAOgQ,YAAY7P,EAAEyV,mBAE/BzV,EAAE4V,kBAAoB,WAClB5V,EAAEH,OAAO4D,kBAAmB,EACxBzD,EAAEH,OAAO8D,oBAAqB,GAAQ3D,EAAEH,OAAOgQ,YAC/C7P,EAAE6V,iBAGV7V,EAAE8V,kBAAoB,WAClB9V,EAAEH,OAAO8D,kBAAmB,EACxB3D,EAAEH,OAAO4D,oBAAqB,GAAQzD,EAAEH,OAAOgQ,YAC/C7P,EAAE6V,iBAGV7V,EAAE+V,aAAe,WACb/V,EAAEH,OAAO4D,iBAAmBzD,EAAEH,OAAO8D,kBAAmB,EACpD3D,EAAEH,OAAOgQ,YAAY7P,EAAE6V,iBAY/B7V,EAAE6V,cAAgB,SAASG,GACvBhW,EAAEC,UAAU,GAAGgW,MAAMC,OAAS,OAC9BlW,EAAEC,UAAU,GAAGgW,MAAMC,OAASF,EAAS,mBAAqB,eAC5DhW,EAAEC,UAAU,GAAGgW,MAAMC,OAASF,EAAS,eAAiB,YACxDhW,EAAEC,UAAU,GAAGgW,MAAMC,OAASF,EAAS,WAAY,QAEvDhW,EAAEyV,gBAAkB,WAChBzV,EAAEC,UAAU,GAAGgW,MAAMC,OAAS,IAE9BlW,EAAEH,OAAOgQ,YACT7P,EAAE6V,gBAKN7V,EAAEmW,gBACFnW,EAAEoW,aAAe,EAEjBpW,EAAEqW,UAAY,SAAUC,EAAYC,EAAKC,EAAQC,EAAOC,EAAkBC,GAEtE,QAASC,KACDD,GAAUA,IAFlB,GAAIE,EAICP,GAAWQ,UAAaJ,EAmBzBE,IAlBIL,GACAM,EAAQ,GAAIvU,QAAOyU,MACnBF,EAAMG,OAASJ,EACfC,EAAMI,QAAUL,EACZH,IACAI,EAAMJ,MAAQA,GAEdD,IACAK,EAAML,OAASA,GAEfD,IACAM,EAAMN,IAAMA,IAGhBK,KAOZ5W,EAAEoQ,cAAgB,WAEd,QAAS8G,KACY,mBAANlX,IAA2B,OAANA,IACTuN,SAAnBvN,EAAEoW,cAA4BpW,EAAEoW,eAChCpW,EAAEoW,eAAiBpW,EAAEmW,aAAajU,SAC9BlC,EAAEH,OAAOwQ,qBAAqBrQ,EAAEmX,SACpCnX,EAAEkB,KAAK,gBAAiBlB,KANhCA,EAAEmW,aAAenW,EAAEC,UAAU6U,KAAK,MASlC,KAAK,GAAIvP,GAAI,EAAGA,EAAIvF,EAAEmW,aAAajU,OAAQqD,IACvCvF,EAAEqW,UAAUrW,EAAEmW,aAAa5Q,GAAKvF,EAAEmW,aAAa5Q,GAAG6R,YAAcpX,EAAEmW,aAAa5Q,GAAG8R,aAAa,OAAUrX,EAAEmW,aAAa5Q,GAAGiR,QAAUxW,EAAEmW,aAAa5Q,GAAG8R,aAAa,UAAYrX,EAAEmW,aAAa5Q,GAAGkR,OAASzW,EAAEmW,aAAa5Q,GAAG8R,aAAa,UAAU,EAAMH,IAOlQlX,EAAEa,kBAAoB0M,OACtBvN,EAAEsX,aAAc,EAChBtX,EAAEuX,gBAAiB,EA8BnBvX,EAAEwX,cAAgB,WACd,MAAmC,mBAAxBxX,GAAEa,sBACRb,EAAEH,OAAOS,YACVN,EAAEsX,cACNtX,EAAEsX,aAAc,EAChBtX,EAAEkB,KAAK,kBAAmBlB,OAC1BM,SAEJN,EAAEqB,aAAe,SAAUoW,GAClBzX,EAAEa,oBACHb,EAAEa,mBAAmBiH,aAAa9H,EAAEa,mBACxCb,EAAEsX,aAAc,EAChBtX,EAAEa,kBAAoB0M,OACtBvN,EAAEkB,KAAK,iBAAkBlB,KAE7BA,EAAE0X,cAAgB,SAAU/M,GACpB3K,EAAEuX,iBACFvX,EAAEa,mBAAmBiH,aAAa9H,EAAEa,mBACxCb,EAAEuX,gBAAiB,EACL,IAAV5M,GACA3K,EAAEuX,gBAAiB,EACnBjX,KAGAN,EAAE2U,QAAQgD,cAAc,WACf3X,IACLA,EAAEuX,gBAAiB,EACdvX,EAAEsX,YAIHhX,IAHAN,EAAEqB,oBAWlBrB,EAAEsH,aAAe,WACb,OAAStH,EAAE4X,SAAS,IAExB5X,EAAEuH,aAAe,WACb,OAASvH,EAAE4X,SAAS5X,EAAE4X,SAAS1V,OAAS,IAK5ClC,EAAE6X,iBAAmB,WACjB,GAAIC,MACAC,EAAY,CAGhB,IAA8B,SAA3B/X,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAO4N,cAAgB,EAC7D,IAAKlI,EAAI,EAAGA,EAAInF,KAAK4X,KAAKhY,EAAEH,OAAO4N,eAAgBlI,IAAK,CACpD,GAAIvD,GAAQhC,EAAEW,YAAc4E,CAC5B,IAAGvD,EAAQhC,EAAES,OAAOyB,OAAQ,KAC5B4V,GAAa3U,KAAKnD,EAAES,OAAOC,GAAGsB,GAAO,QAGzC8V,GAAa3U,KAAKnD,EAAES,OAAOC,GAAGV,EAAEW,aAAa,GAIjD,KAAK4E,EAAI,EAAGA,EAAIuS,EAAa5V,OAAQqD,IACjC,GAA+B,mBAApBuS,GAAavS,GAAoB,CACxC,GAAID,GAASwS,EAAavS,GAAG0S,YAC7BF,GAAYzS,EAASyS,EAAYzS,EAASyS,EAK9CA,GAAW/X,EAAE2U,QAAQM,IAAI,SAAU8C,EAAY,OAEvD/X,EAAEkY,oBAAsB,WACpB,GAAI7S,GAAOC,CAEPD,GAD0B,mBAAnBrF,GAAEH,OAAOwF,MACRrF,EAAEH,OAAOwF,MAGTrF,EAAEC,UAAU,GAAGkY,YAGvB7S,EAD2B,mBAApBtF,GAAEH,OAAOyF,OACPtF,EAAEH,OAAOyF,OAGTtF,EAAEC,UAAU,GAAGmY,aAEd,IAAV/S,GAAerF,EAAE0D,gBAA6B,IAAX4B,IAAiBtF,EAAE0D,iBAK1D2B,EAAQA,EAAQ6E,SAASlK,EAAEC,UAAUgV,IAAI,gBAAiB,IAAM/K,SAASlK,EAAEC,UAAUgV,IAAI,iBAAkB,IAC3G3P,EAASA,EAAS4E,SAASlK,EAAEC,UAAUgV,IAAI,eAAgB,IAAM/K,SAASlK,EAAEC,UAAUgV,IAAI,kBAAmB,IAG7GjV,EAAEqF,MAAQA,EACVrF,EAAEsF,OAASA,EACXtF,EAAEqY,KAAOrY,EAAE0D,eAAiB1D,EAAEqF,MAAQrF,EAAEsF,SAG5CtF,EAAEsY,iBAAmB,WACjBtY,EAAES,OAAST,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,YAC7CrE,EAAE4X,YACF5X,EAAEuY,cACFvY,EAAEwY,kBAEF,IAEIjT,GAFAiI,EAAexN,EAAEH,OAAO2N,aACxBiL,GAAiBzY,EAAEH,OAAOiO,mBAE1B4K,EAAgB,EAChB1W,EAAQ,CACZ,IAAsB,mBAAXhC,GAAEqY,KAAb,CAC4B,gBAAjB7K,IAA6BA,EAAavD,QAAQ,MAAQ,IACjEuD,EAAemL,WAAWnL,EAAaoL,QAAQ,IAAK,KAAO,IAAM5Y,EAAEqY,MAGvErY,EAAE6Y,aAAerL,EAEbxN,EAAEkF,IAAKlF,EAAES,OAAOwU,KAAK6D,WAAY,GAAIC,UAAW,KAC/C/Y,EAAES,OAAOwU,KAAK+D,YAAa,GAAIC,aAAc,IAElD,IAAIC,EACAlZ,GAAEH,OAAO6N,gBAAkB,IAEvBwL,EADA9Y,KAAKC,MAAML,EAAES,OAAOyB,OAASlC,EAAEH,OAAO6N,mBAAqB1N,EAAES,OAAOyB,OAASlC,EAAEH,OAAO6N,gBAC7D1N,EAAES,OAAOyB,OAGT9B,KAAK4X,KAAKhY,EAAES,OAAOyB,OAASlC,EAAEH,OAAO6N,iBAAmB1N,EAAEH,OAAO6N,gBAE/D,SAA3B1N,EAAEH,OAAO4N,eAA6D,QAAjCzN,EAAEH,OAAO8N,sBAC9CuL,EAAyB9Y,KAAK+Y,IAAID,EAAwBlZ,EAAEH,OAAO4N,cAAgBzN,EAAEH,OAAO6N,kBAKpG,IAAI0L,GACA1L,EAAkB1N,EAAEH,OAAO6N,gBAC3B2L,EAAeH,EAAyBxL,EACxC4L,EAAiBD,GAAgBrZ,EAAEH,OAAO6N,gBAAkB2L,EAAerZ,EAAES,OAAOyB,OACxF,KAAKqD,EAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CAClC6T,EAAY,CACZ,IAAIG,GAAQvZ,EAAES,OAAOC,GAAG6E,EACxB,IAAIvF,EAAEH,OAAO6N,gBAAkB,EAAG,CAE9B,GAAI8L,GACAC,EAAQC,CACyB,YAAjC1Z,EAAEH,OAAO8N,qBACT8L,EAASrZ,KAAKC,MAAMkF,EAAImI,GACxBgM,EAAMnU,EAAIkU,EAAS/L,GACf+L,EAASH,GAAmBG,IAAWH,GAAkBI,IAAQhM,EAAgB,MAC3EgM,GAAOhM,IACTgM,EAAM,EACND,KAGRD,EAAqBC,EAASC,EAAMR,EAAyBxL,EAC7D6L,EACKtE,KACG0E,4BAA6BH,EAC7BI,yBAA0BJ,EAC1BK,iBAAkBL,EAClBM,gBAAiBN,EACjBO,MAASP,MAIjBE,EAAMtZ,KAAKC,MAAMkF,EAAI8T,GACrBI,EAASlU,EAAImU,EAAML,GAEvBE,EACKtE,IACG,WAAajV,EAAE0D,eAAiB,MAAQ,QAC/B,IAARgW,GAAa1Z,EAAEH,OAAO2N,cAAkBxN,EAAEH,OAAO2N,aAAe,MAEpE5M,KAAK,qBAAsB6Y,GAC3B7Y,KAAK,kBAAmB8Y,GAGJ,SAAzBH,EAAMtE,IAAI,aACiB,SAA3BjV,EAAEH,OAAO4N,eACT2L,EAAYpZ,EAAE0D,eAAiB6V,EAAMS,YAAW,GAAQT,EAAMU,aAAY,GACtEja,EAAEH,OAAOmO,eAAcoL,EAAYlZ,EAAMkZ,MAG7CA,GAAapZ,EAAEqY,MAAQrY,EAAEH,OAAO4N,cAAgB,GAAKD,GAAgBxN,EAAEH,OAAO4N,cAC1EzN,EAAEH,OAAOmO,eAAcoL,EAAYlZ,EAAMkZ,IAEzCpZ,EAAE0D,eACF1D,EAAES,OAAO8E,GAAG0Q,MAAM5Q,MAAQ+T,EAAY,KAGtCpZ,EAAES,OAAO8E,GAAG0Q,MAAM3Q,OAAS8T,EAAY,MAG/CpZ,EAAES,OAAO8E,GAAG2U,gBAAkBd,EAC9BpZ,EAAEwY,gBAAgBrV,KAAKiW,GAGnBpZ,EAAEH,OAAOgO,gBACT4K,EAAgBA,EAAgBW,EAAY,EAAIV,EAAgB,EAAIlL,EAC1D,IAANjI,IAASkT,EAAgBA,EAAgBzY,EAAEqY,KAAO,EAAI7K,GACtDpN,KAAKuG,IAAI8R,GAAiB,OAAUA,EAAgB,GACpD,EAAUzY,EAAEH,OAAO+N,iBAAmB,GAAG5N,EAAE4X,SAASzU,KAAKsV,GAC7DzY,EAAEuY,WAAWpV,KAAKsV,KAGd,EAAUzY,EAAEH,OAAO+N,iBAAmB,GAAG5N,EAAE4X,SAASzU,KAAKsV,GAC7DzY,EAAEuY,WAAWpV,KAAKsV,GAClBA,EAAgBA,EAAgBW,EAAY5L,GAGhDxN,EAAE6Y,aAAeO,EAAY5L,EAE7BkL,EAAgBU,EAEhBpX,KAEJhC,EAAE6Y,YAAczY,KAAK+Y,IAAInZ,EAAE6Y,YAAa7Y,EAAEqY,MAAQrY,EAAEH,OAAOkO,iBAC3D,IAAIoM,EAWJ,IARIna,EAAEkF,KAAOlF,EAAEkV,WAAiC,UAApBlV,EAAEH,OAAO0L,QAA0C,cAApBvL,EAAEH,OAAO0L,SAChEvL,EAAE2U,QAAQM,KAAK5P,MAAOrF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAE7DxN,EAAEuU,QAAQC,UAAWxU,EAAEH,OAAOwL,iBAC3BrL,EAAE0D,eAAgB1D,EAAE2U,QAAQM,KAAK5P,MAAOrF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAC/ExN,EAAE2U,QAAQM,KAAK3P,OAAQtF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,QAGpExN,EAAEH,OAAO6N,gBAAkB,IAC3B1N,EAAE6Y,aAAeO,EAAYpZ,EAAEH,OAAO2N,cAAgB0L,EACtDlZ,EAAE6Y,YAAczY,KAAK4X,KAAKhY,EAAE6Y,YAAc7Y,EAAEH,OAAO6N,iBAAmB1N,EAAEH,OAAO2N,aAC3ExN,EAAE0D,eAAgB1D,EAAE2U,QAAQM,KAAK5P,MAAOrF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAC/ExN,EAAE2U,QAAQM,KAAK3P,OAAQtF,EAAE6Y,YAAc7Y,EAAEH,OAAO2N,aAAe,OAChExN,EAAEH,OAAOgO,gBAAgB,CAEzB,IADAsM,KACK5U,EAAI,EAAGA,EAAIvF,EAAE4X,SAAS1V,OAAQqD,IAC3BvF,EAAE4X,SAASrS,GAAKvF,EAAE6Y,YAAc7Y,EAAE4X,SAAS,IAAIuC,EAAchX,KAAKnD,EAAE4X,SAASrS,GAErFvF,GAAE4X,SAAWuC,EAKrB,IAAKna,EAAEH,OAAOgO,eAAgB,CAE1B,IADAsM,KACK5U,EAAI,EAAGA,EAAIvF,EAAE4X,SAAS1V,OAAQqD,IAC3BvF,EAAE4X,SAASrS,IAAMvF,EAAE6Y,YAAc7Y,EAAEqY,MACnC8B,EAAchX,KAAKnD,EAAE4X,SAASrS,GAGtCvF,GAAE4X,SAAWuC,EACT/Z,KAAKC,MAAML,EAAE6Y,YAAc7Y,EAAEqY,MAAQjY,KAAKC,MAAML,EAAE4X,SAAS5X,EAAE4X,SAAS1V,OAAS,IAAM,GACrFlC,EAAE4X,SAASzU,KAAKnD,EAAE6Y,YAAc7Y,EAAEqY,MAGhB,IAAtBrY,EAAE4X,SAAS1V,SAAclC,EAAE4X,UAAY,IAEb,IAA1B5X,EAAEH,OAAO2N,eACLxN,EAAE0D,eACE1D,EAAEkF,IAAKlF,EAAES,OAAOwU,KAAK6D,WAAYtL,EAAe,OAC/CxN,EAAES,OAAOwU,KAAK+D,YAAaxL,EAAe,OAE9CxN,EAAES,OAAOwU,KAAKgE,aAAczL,EAAe,QAEhDxN,EAAEH,OAAO8P,qBACT3P,EAAEoa,uBAGVpa,EAAEoa,mBAAqB,WACnB,IAAK,GAAI7U,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IACjCvF,EAAES,OAAO8E,GAAG8U,kBAAoBra,EAAE0D,eAAiB1D,EAAES,OAAO8E,GAAG+U,WAAata,EAAES,OAAO8E,GAAGgV,WAOhGva,EAAEwa,qBAAuB,SAAUlF,GAI/B,GAHyB,mBAAdA,KACPA,EAAYtV,EAAEsV,WAAa,GAEP,IAApBtV,EAAES,OAAOyB,OAAb,CAC6C,mBAAlClC,GAAES,OAAO,GAAG4Z,mBAAmCra,EAAEoa,oBAE5D,IAAIK,IAAgBnF,CAChBtV,GAAEkF,MAAKuV,EAAenF,GAG1BtV,EAAES,OAAOia,YAAY1a,EAAEH,OAAOqR,kBAC9B,KAAK,GAAI3L,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAO8E,GACjBoV,GAAiBF,GAAgBza,EAAEH,OAAOgO,eAAiB7N,EAAEsH,eAAiB,GAAKiS,EAAMc,oBAAsBd,EAAMW,gBAAkBla,EAAEH,OAAO2N,aACpJ,IAAIxN,EAAEH,OAAO+P,sBAAuB,CAChC,GAAIgL,KAAgBH,EAAelB,EAAMc,mBACrCQ,EAAaD,EAAc5a,EAAEwY,gBAAgBjT,GAC7CuV,EACCF,GAAe,GAAKA,EAAc5a,EAAEqY,MACpCwC,EAAa,GAAKA,GAAc7a,EAAEqY,MAClCuC,GAAe,GAAKC,GAAc7a,EAAEqY,IACrCyC,IACA9a,EAAES,OAAOC,GAAG6E,GAAGwP,SAAS/U,EAAEH,OAAOqR,mBAGzCqI,EAAMxP,SAAW/J,EAAEkF,KAAOyV,EAAgBA,KAGlD3a,EAAE0H,eAAiB,SAAU4N,GACA,mBAAdA,KACPA,EAAYtV,EAAEsV,WAAa,EAE/B,IAAIyF,GAAiB/a,EAAEuH,eAAiBvH,EAAEsH,eACtCH,EAAenH,EAAEoH,YACjBC,EAASrH,EAAEmB,KACQ,KAAnB4Z,GACA/a,EAAE+J,SAAW,EACb/J,EAAEoH,YAAcpH,EAAEmB,OAAQ,IAG1BnB,EAAE+J,UAAYuL,EAAYtV,EAAEsH,gBAAkB,EAC9CtH,EAAEoH,YAAcpH,EAAE+J,UAAY,EAC9B/J,EAAEmB,MAAQnB,EAAE+J,UAAY,GAExB/J,EAAEoH,cAAgBD,GAAcnH,EAAEkB,KAAK,mBAAoBlB,GAC3DA,EAAEmB,QAAUkG,GAAQrH,EAAEkB,KAAK,aAAclB,GAEzCA,EAAEH,OAAO8P,qBAAqB3P,EAAEwa,qBAAqBlF,GACzDtV,EAAEkB,KAAK,aAAclB,EAAGA,EAAE+J,WAE9B/J,EAAE2H,kBAAoB,WAClB,GACIqT,GAAgBzV,EAAG0V,EADnB3F,EAAYtV,EAAEkF,IAAMlF,EAAEsV,WAAatV,EAAEsV,SAEzC,KAAK/P,EAAI,EAAGA,EAAIvF,EAAEuY,WAAWrW,OAAQqD,IACE,mBAAxBvF,GAAEuY,WAAWhT,EAAI,GACpB+P,GAAatV,EAAEuY,WAAWhT,IAAM+P,EAAYtV,EAAEuY,WAAWhT,EAAI,IAAMvF,EAAEuY,WAAWhT,EAAI,GAAKvF,EAAEuY,WAAWhT,IAAM,EAC5GyV,EAAiBzV,EAEZ+P,GAAatV,EAAEuY,WAAWhT,IAAM+P,EAAYtV,EAAEuY,WAAWhT,EAAI,KAClEyV,EAAiBzV,EAAI,GAIrB+P,GAAatV,EAAEuY,WAAWhT,KAC1ByV,EAAiBzV,EAK1BvF,GAAEH,OAAO8Q,sBACJqK,EAAiB,GAA+B,mBAAnBA,MAAgCA,EAAiB,GAOtFC,EAAY7a,KAAKC,MAAM2a,EAAiBhb,EAAEH,OAAO+N,gBAC7CqN,GAAajb,EAAE4X,SAAS1V,SAAQ+Y,EAAYjb,EAAE4X,SAAS1V,OAAS,GAEhE8Y,IAAmBhb,EAAEW,cAGzBX,EAAEib,UAAYA,EACdjb,EAAEkb,cAAgBlb,EAAEW,YACpBX,EAAEW,YAAcqa,EAChBhb,EAAE4H,gBACF5H,EAAEmb,oBAENnb,EAAEmb,gBAAkB,WAChBnb,EAAEob,UAAYpb,EAAES,OAAOC,GAAGV,EAAEW,aAAaC,KAAK,4BAA8BZ,EAAEW,aAMlFX,EAAE4H,cAAgB,WACd5H,EAAES,OAAOia,YAAY1a,EAAEH,OAAOyE,iBAAmB,IAAMtE,EAAEH,OAAOuR,eAAiB,IAAMpR,EAAEH,OAAOyR,eAAiB,IAAMtR,EAAEH,OAAOoR,0BAA4B,IAAMjR,EAAEH,OAAOwR,wBAA0B,IAAMrR,EAAEH,OAAO0R,wBACpN,IAAI/Q,GAAcR,EAAES,OAAOC,GAAGV,EAAEW,YAEhCH,GAAYuU,SAAS/U,EAAEH,OAAOyE,kBAC1BzE,EAAOkB,OAEHP,EAAY6a,SAASrb,EAAEH,OAAOsR,qBAC9BnR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,SAAWrE,EAAEH,OAAOsR,oBAAsB,8BAAgCnR,EAAEob,UAAY,MAAMrG,SAAS/U,EAAEH,OAAOoR,2BAG/JjR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,oBAAsB,6BAA+BnR,EAAEob,UAAY,MAAMrG,SAAS/U,EAAEH,OAAOoR,2BAIjK,IAAIqK,GAAY9a,EAAY+a,KAAK,IAAMvb,EAAEH,OAAOwE,YAAY0Q,SAAS/U,EAAEH,OAAOuR,eAC1EpR,GAAEH,OAAOkB,MAA6B,IAArBua,EAAUpZ,SAC3BoZ,EAAYtb,EAAES,OAAOC,GAAG,GACxB4a,EAAUvG,SAAS/U,EAAEH,OAAOuR,gBAGhC,IAAIoK,GAAYhb,EAAYib,KAAK,IAAMzb,EAAEH,OAAOwE,YAAY0Q,SAAS/U,EAAEH,OAAOyR,eAsB9E,IArBItR,EAAEH,OAAOkB,MAA6B,IAArBya,EAAUtZ,SAC3BsZ,EAAYxb,EAAES,OAAOC,IAAG,GACxB8a,EAAUzG,SAAS/U,EAAEH,OAAOyR,iBAE5BzR,EAAOkB,OAEHua,EAAUD,SAASrb,EAAEH,OAAOsR,qBAC5BnR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,SAAWrE,EAAEH,OAAOsR,oBAAsB,8BAAgCmK,EAAU1a,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAOwR,yBAG7LrR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,oBAAsB,6BAA+BmK,EAAU1a,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAOwR,yBAEvLmK,EAAUH,SAASrb,EAAEH,OAAOsR,qBAC5BnR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,SAAWrE,EAAEH,OAAOsR,oBAAsB,8BAAgCqK,EAAU5a,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAO0R,yBAG7LvR,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,oBAAsB,6BAA+BqK,EAAU5a,KAAK,2BAA6B,MAAMmU,SAAS/U,EAAEH,OAAO0R,0BAK3LvR,EAAE6U,qBAAuB7U,EAAE6U,oBAAoB3S,OAAS,EAAG,CAE3D,GAAIwZ,GACAC,EAAQ3b,EAAEH,OAAOkB,KAAOX,KAAK4X,MAAMhY,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,cAAoBvQ,EAAEH,OAAO+N,gBAAkB5N,EAAE4X,SAAS1V,MAiCrH,IAhCIlC,EAAEH,OAAOkB,MACT2a,EAAUtb,KAAK4X,MAAMhY,EAAEW,YAAcX,EAAEuQ,cAAcvQ,EAAEH,OAAO+N,gBAC1D8N,EAAU1b,EAAES,OAAOyB,OAAS,EAAqB,EAAjBlC,EAAEuQ,eAClCmL,GAAqB1b,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,cAEzCmL,EAAUC,EAAQ,IAAGD,GAAoBC,GACzCD,EAAU,GAAiC,YAA5B1b,EAAEH,OAAOyP,iBAA8BoM,EAAUC,EAAQD,IAIxEA,EADuB,mBAAhB1b,GAAEib,UACCjb,EAAEib,UAGFjb,EAAEW,aAAe,EAIH,YAA5BX,EAAEH,OAAOyP,gBAAgCtP,EAAE4b,SAAW5b,EAAE4b,QAAQ1Z,OAAS,IACzElC,EAAE4b,QAAQlB,YAAY1a,EAAEH,OAAO6R,mBAC3B1R,EAAE6U,oBAAoB3S,OAAS,EAC/BlC,EAAE4b,QAAQ7b,KAAK,WACPP,EAAED,MAAMyC,UAAY0Z,GAASlc,EAAED,MAAMwV,SAAS/U,EAAEH,OAAO6R,qBAI/D1R,EAAE4b,QAAQlb,GAAGgb,GAAS3G,SAAS/U,EAAEH,OAAO6R,oBAGhB,aAA5B1R,EAAEH,OAAOyP,iBACTtP,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAO+R,wBAAwBiK,KAAKH,EAAU,GACjF1b,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAOgS,sBAAsBgK,KAAKF,IAEzC,aAA5B3b,EAAEH,OAAOyP,eAA+B,CACxC,GAAIwM,IAASJ,EAAU,GAAKC,EACxBI,EAASD,EACTE,EAAS,CACRhc,GAAE0D,iBACHsY,EAASF,EACTC,EAAS,GAEb/b,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAOkS,4BAA4B5H,UAAU,6BAA+B4R,EAAS,YAAcC,EAAS,KAAKC,WAAWjc,EAAEH,OAAO8K,OAE5I,WAA5B3K,EAAEH,OAAOyP,gBAA+BtP,EAAEH,OAAOwP,yBACjDrP,EAAE6U,oBAAoBqH,KAAKlc,EAAEH,OAAOwP,uBAAuBrP,EAAG0b,EAAU,EAAGC,IAC3E3b,EAAEkB,KAAK,uBAAwBlB,EAAGA,EAAE6U,oBAAoB,KAK3D7U,EAAEH,OAAOkB,OACNf,EAAEH,OAAO6P,YAAc1P,EAAE0P,YAAc1P,EAAE0P,WAAWxN,OAAS,IACzDlC,EAAEoH,aACFpH,EAAE0P,WAAWqF,SAAS/U,EAAEH,OAAO8R,qBAC3B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAKyJ,QAAQnc,EAAE0P,cAG9C1P,EAAE0P,WAAWgL,YAAY1a,EAAEH,OAAO8R,qBAC9B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAK0J,OAAOpc,EAAE0P,cAGjD1P,EAAEH,OAAO4P,YAAczP,EAAEyP,YAAczP,EAAEyP,WAAWvN,OAAS,IACzDlC,EAAEmB,OACFnB,EAAEyP,WAAWsF,SAAS/U,EAAEH,OAAO8R,qBAC3B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAKyJ,QAAQnc,EAAEyP,cAG9CzP,EAAEyP,WAAWiL,YAAY1a,EAAEH,OAAO8R,qBAC9B3R,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAK0J,OAAOpc,EAAEyP,gBAS7DzP,EAAEqc,iBAAmB,WACjB,GAAKrc,EAAEH,OAAOiP,YACV9O,EAAE6U,qBAAuB7U,EAAE6U,oBAAoB3S,OAAS,EAAG,CAC3D,GAAIoa,GAAiB,EACrB,IAAgC,YAA5Btc,EAAEH,OAAOyP,eAA8B,CAEvC,IAAK,GADDiN,GAAkBvc,EAAEH,OAAOkB,KAAOX,KAAK4X,MAAMhY,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,cAAoBvQ,EAAEH,OAAO+N,gBAAkB5N,EAAE4X,SAAS1V,OACtHqD,EAAI,EAAGA,EAAIgX,EAAiBhX,IAE7B+W,GADAtc,EAAEH,OAAOqP,uBACSlP,EAAEH,OAAOqP,uBAAuBlP,EAAGuF,EAAGvF,EAAEH,OAAO4R,aAG/C,IAAMzR,EAAEH,OAAOkP,kBAAkB,WAAa/O,EAAEH,OAAO4R,YAAc,OAASzR,EAAEH,OAAOkP,kBAAoB,GAGrI/O,GAAE6U,oBAAoBqH,KAAKI,GAC3Btc,EAAE4b,QAAU5b,EAAE6U,oBAAoBC,KAAK,IAAM9U,EAAEH,OAAO4R,aAClDzR,EAAEH,OAAOmP,qBAAuBhP,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MACnD1S,EAAE0S,KAAK8J,iBAGiB,aAA5Bxc,EAAEH,OAAOyP,iBAELgN,EADAtc,EAAEH,OAAOuP,yBACQpP,EAAEH,OAAOuP,yBAAyBpP,EAAGA,EAAEH,OAAO+R,uBAAwB5R,EAAEH,OAAOgS,sBAI5F,gBAAkB7R,EAAEH,OAAO+R,uBAAyB,4BAElC5R,EAAEH,OAAOgS,qBAAqB,YAExD7R,EAAE6U,oBAAoBqH,KAAKI,IAEC,aAA5Btc,EAAEH,OAAOyP,iBAELgN,EADAtc,EAAEH,OAAOsP,yBACQnP,EAAEH,OAAOsP,yBAAyBnP,EAAGA,EAAEH,OAAOkS,4BAG9C,gBAAkB/R,EAAEH,OAAOkS,2BAA6B,YAE7E/R,EAAE6U,oBAAoBqH,KAAKI,IAEC,WAA5Btc,EAAEH,OAAOyP,gBACTtP,EAAEkB,KAAK,uBAAwBlB,EAAGA,EAAE6U,oBAAoB,MAOpE7U,EAAEmX,OAAS,SAAUsF,GASjB,QAASC,KACW1c,EAAEkF,KAAOlF,EAAEsV,UAAYtV,EAAEsV,SACzCqH,GAAevc,KAAKwc,IAAIxc,KAAK+Y,IAAInZ,EAAEsV,UAAWtV,EAAEuH,gBAAiBvH,EAAEsH,gBACnEtH,EAAEyH,oBAAoBkV,GACtB3c,EAAE2H,oBACF3H,EAAE4H,gBAEN,GAfA5H,EAAEkY,sBACFlY,EAAEsY,mBACFtY,EAAE0H,iBACF1H,EAAEqc,mBACFrc,EAAE4H,gBACE5H,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAUkQ,MASZJ,EAAiB,CACjB,GAAIK,GAAYH,CACZ3c,GAAE+c,YAAc/c,EAAE+c,WAAWC,SAC7Bhd,EAAE+c,WAAWC,OAASzP,QAEtBvN,EAAEH,OAAOkH,UACT2V,IACI1c,EAAEH,OAAOuL,YACTpL,EAAE6X,qBAKFiF,GAD4B,SAA3B9c,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAO4N,cAAgB,IAAMzN,EAAEmB,QAAUnB,EAAEH,OAAOgO,eAC7E7N,EAAEid,QAAQjd,EAAES,OAAOyB,OAAS,EAAG,GAAG,GAAO,GAGzClC,EAAEid,QAAQjd,EAAEW,YAAa,GAAG,GAAO,GAE/Cmc,GACDJ,SAIH1c,GAAEH,OAAOuL,YACdpL,EAAE6X,oBAOV7X,EAAE6C,SAAW,SAAUqa,GAEfld,EAAEH,OAAOyN,aACTtN,EAAEiU,eAIN,IAAItQ,GAAmB3D,EAAEH,OAAO8D,iBAC5BF,EAAmBzD,EAAEH,OAAO4D,gBAChCzD,GAAEH,OAAO8D,iBAAmB3D,EAAEH,OAAO4D,kBAAmB,EAExDzD,EAAEkY,sBACFlY,EAAEsY,oBAC6B,SAA3BtY,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAOkH,UAAYmW,IAAuBld,EAAEqc,mBACnFrc,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAUkQ,MAEZ7c,EAAE+c,YAAc/c,EAAE+c,WAAWC,SAC7Bhd,EAAE+c,WAAWC,OAASzP,OAE1B,IAAI4P,IAAwB,CAC5B,IAAInd,EAAEH,OAAOkH,SAAU,CACnB,GAAI4V,GAAevc,KAAKwc,IAAIxc,KAAK+Y,IAAInZ,EAAEsV,UAAWtV,EAAEuH,gBAAiBvH,EAAEsH,eACvEtH,GAAEyH,oBAAoBkV,GACtB3c,EAAE2H,oBACF3H,EAAE4H,gBAEE5H,EAAEH,OAAOuL,YACTpL,EAAE6X,uBAIN7X,GAAE4H,gBAEEuV,GAD4B,SAA3Bnd,EAAEH,OAAO4N,eAA4BzN,EAAEH,OAAO4N,cAAgB,IAAMzN,EAAEmB,QAAUnB,EAAEH,OAAOgO,eAClE7N,EAAEid,QAAQjd,EAAES,OAAOyB,OAAS,EAAG,GAAG,GAAO,GAGzClC,EAAEid,QAAQjd,EAAEW,YAAa,GAAG,GAAO,EAG/DX,GAAEH,OAAOqI,cAAgBiV,GAAyBnd,EAAEmI,MACpDnI,EAAEmI,KAAKC,OAGXpI,EAAEH,OAAO8D,iBAAmBA,EAC5B3D,EAAEH,OAAO4D,iBAAmBA,GAQhCzD,EAAEod,oBAAsBC,MAAO,YAAaC,KAAM,YAAaC,IAAK,WAChEjb,OAAOkb,UAAUC,eAAgBzd,EAAEod,oBAAsBC,MAAO,cAAeC,KAAM,cAAeC,IAAK,aACpGjb,OAAOkb,UAAUE,mBAAkB1d,EAAEod,oBAAsBC,MAAO,gBAAiBC,KAAM,gBAAiBC,IAAK,gBACxHvd,EAAE2d,aACEN,MAAQrd,EAAEuU,QAAQG,QAAU1U,EAAEH,OAAOsO,cAAiB,aAAenO,EAAEod,mBAAmBC,MAC1FC,KAAOtd,EAAEuU,QAAQG,QAAU1U,EAAEH,OAAOsO,cAAgB,YAAcnO,EAAEod,mBAAmBE,KACvFC,IAAMvd,EAAEuU,QAAQG,QAAU1U,EAAEH,OAAOsO,cAAgB,WAAanO,EAAEod,mBAAmBG,MAKrFjb,OAAOkb,UAAUC,gBAAkBnb,OAAOkb,UAAUE,oBACpB,cAA/B1d,EAAEH,OAAO4K,kBAAoCzK,EAAEC,UAAYD,EAAE2U,SAASI,SAAS,cAAgB/U,EAAEH,OAAO2K,WAI7GxK,EAAE4d,WAAa,SAAUC,GACrB,GAAIC,GAAYD,EAAS,MAAQ,KAC7BE,EAASF,EAAS,sBAAwB,mBAC1CpT,EAAmD,cAA/BzK,EAAEH,OAAO4K,kBAAoCzK,EAAEC,UAAU,GAAKD,EAAE2U,QAAQ,GAC5FhT,EAAS3B,EAAEuU,QAAQG,MAAQjK,EAAoBzG,SAE/Cga,IAAche,EAAEH,OAAOoe,MAG3B,IAAIje,EAAEke,QAAQC,GACV1T,EAAkBsT,GAAQ/d,EAAE2d,YAAYN,MAAOrd,EAAEoe,cAAc,GAC/Dzc,EAAOoc,GAAQ/d,EAAE2d,YAAYL,KAAMtd,EAAEqe,YAAaL,GAClDrc,EAAOoc,GAAQ/d,EAAE2d,YAAYJ,IAAKvd,EAAEse,YAAY,OAE/C,CACD,GAAIte,EAAEuU,QAAQG,MAAO,CACjB,GAAI6J,KAA0C,eAAxBve,EAAE2d,YAAYN,QAA0Brd,EAAEuU,QAAQgK,kBAAmBve,EAAEH,OAAOkR,oBAAoByN,SAAS,EAAMC,SAAS,EAChJhU,GAAkBsT,GAAQ/d,EAAE2d,YAAYN,MAAOrd,EAAEoe,aAAcG,GAC/D9T,EAAkBsT,GAAQ/d,EAAE2d,YAAYL,KAAMtd,EAAEqe,YAAaL,GAC7DvT,EAAkBsT,GAAQ/d,EAAE2d,YAAYJ,IAAKvd,EAAEse,WAAYC,IAE1D1e,EAAOsO,gBAAkBnO,EAAEmV,OAAOuJ,MAAQ1e,EAAEmV,OAAOC,SAAavV,EAAOsO,gBAAkBnO,EAAEuU,QAAQG,OAAS1U,EAAEmV,OAAOuJ,OACtHjU,EAAkBsT,GAAQ,YAAa/d,EAAEoe,cAAc,GACvDpa,SAAS+Z,GAAQ,YAAa/d,EAAEqe,YAAaL,GAC7Cha,SAAS+Z,GAAQ,UAAW/d,EAAEse,YAAY,IAGlDhc,OAAOyb,GAAQ,SAAU/d,EAAE6C,UAGvB7C,EAAEH,OAAO4P,YAAczP,EAAEyP,YAAczP,EAAEyP,WAAWvN,OAAS,IAC7DlC,EAAEyP,WAAWqO,GAAW,QAAS9d,EAAE2e,aAC/B3e,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAEyP,WAAWqO,GAAW,UAAW9d,EAAE0S,KAAKkM,aAEvE5e,EAAEH,OAAO6P,YAAc1P,EAAE0P,YAAc1P,EAAE0P,WAAWxN,OAAS,IAC7DlC,EAAE0P,WAAWoO,GAAW,QAAS9d,EAAE6e,aAC/B7e,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0P,WAAWoO,GAAW,UAAW9d,EAAE0S,KAAKkM,aAEvE5e,EAAEH,OAAOiP,YAAc9O,EAAEH,OAAOmP,sBAChChP,EAAE6U,oBAAoBiJ,GAAW,QAAS,IAAM9d,EAAEH,OAAO4R,YAAazR,EAAE8e,cACpE9e,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE6U,oBAAoBiJ,GAAW,UAAW,IAAM9d,EAAEH,OAAO4R,YAAazR,EAAE0S,KAAKkM,cAI5G5e,EAAEH,OAAOiQ,eAAiB9P,EAAEH,OAAOkQ,2BAA0BtF,EAAkBsT,GAAQ,QAAS/d,EAAE8P,eAAe,IAEzH9P,EAAE+e,aAAe,WACb/e,EAAE4d,cAEN5d,EAAEgf,aAAe,WACbhf,EAAE4d,YAAW,IAOjB5d,EAAEif,YAAa,EACfjf,EAAE8P,cAAgB,SAAUtO,GACnBxB,EAAEif,aACCjf,EAAEH,OAAOiQ,eAAetO,EAAEiE,iBAC1BzF,EAAEH,OAAOkQ,0BAA4B/P,EAAEyI,YACvCjH,EAAE0d,kBACF1d,EAAE2d;EAKdnf,EAAE2e,YAAc,SAAUnd,GACtBA,EAAEiE,iBACEzF,EAAEmB,QAAUnB,EAAEH,OAAOkB,MACzBf,EAAE2F,aAEN3F,EAAE6e,YAAc,SAAUrd,GACtBA,EAAEiE,iBACEzF,EAAEoH,cAAgBpH,EAAEH,OAAOkB,MAC/Bf,EAAE4F,aAEN5F,EAAE8e,aAAe,SAAUtd,GACvBA,EAAEiE,gBACF,IAAIzD,GAAQxC,EAAED,MAAMyC,QAAUhC,EAAEH,OAAO+N,cACnC5N,GAAEH,OAAOkB,OAAMiB,GAAgBhC,EAAEuQ,cACrCvQ,EAAEid,QAAQjb,IA0BdhC,EAAEof,mBAAqB,SAAU5d,GAC7B,GAAI+X,GAAQhY,EAAmBC,EAAG,IAAMxB,EAAEH,OAAOwE,YAC7Cgb,GAAa,CACjB,IAAI9F,EACA,IAAK,GAAIhU,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAC7BvF,EAAES,OAAO8E,KAAOgU,IAAO8F,GAAa,EAIhD,KAAI9F,IAAS8F,EAOT,MAFArf,GAAEsf,aAAe/R,YACjBvN,EAAEuf,aAAehS,OAGrB,IARIvN,EAAEsf,aAAe/F,EACjBvZ,EAAEuf,aAAe/f,EAAE+Z,GAAOvX,QAO1BhC,EAAEH,OAAOmQ,qBAA0CzC,SAAnBvN,EAAEuf,cAA8Bvf,EAAEuf,eAAiBvf,EAAEW,YAAa,CAClG,GACIya,GADAoE,EAAexf,EAAEuf,YAGrB,IAAIvf,EAAEH,OAAOkB,KAAM,CACf,GAAIf,EAAEyI,UAAW,MACjB2S,GAAY5b,EAAEQ,EAAEsf,cAAc1e,KAAK,2BAC/BZ,EAAEH,OAAOgO,eACJ2R,EAAexf,EAAEuQ,aAAevQ,EAAEH,OAAO4N,cAAc,GAAO+R,EAAexf,EAAES,OAAOyB,OAASlC,EAAEuQ,aAAevQ,EAAEH,OAAO4N,cAAc,GACxIzN,EAAEgB,UACFwe,EAAexf,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,6BAA+B+W,EAAY,WAAapb,EAAEH,OAAOsR,oBAAsB,KAAKzQ,GAAG,GAAGsB,QAChKlB,WAAW,WACPd,EAAEid,QAAQuC,IACX,IAGHxf,EAAEid,QAAQuC,GAIVA,EAAexf,EAAES,OAAOyB,OAASlC,EAAEH,OAAO4N,eAC1CzN,EAAEgB,UACFwe,EAAexf,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,6BAA+B+W,EAAY,WAAapb,EAAEH,OAAOsR,oBAAsB,KAAKzQ,GAAG,GAAGsB,QAChKlB,WAAW,WACPd,EAAEid,QAAQuC,IACX,IAGHxf,EAAEid,QAAQuC,OAKlBxf,GAAEid,QAAQuC,IAKtB,IAAIC,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAI4BC,EAG5BC,EALAC,EAAe,yCAEfC,EAAgB9X,KAAK+X,MAErBC,IAIJtgB,GAAEyI,WAAY,EAGdzI,EAAEugB,SACEC,OAAQ,EACRC,OAAQ,EACRC,SAAU,EACVC,SAAU,EACVC,KAAM,EAIV,IAAIC,GAAcC,CAClB9gB,GAAEoe,aAAe,SAAU5c,GAGvB,GAFIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,eAC3Bwd,EAA0B,eAAXrf,EAAEuf,KACZF,KAAgB,SAAWrf,KAAiB,IAAZA,EAAEwf,MAAvC,CACA,GAAIhhB,EAAEH,OAAOgR,WAAatP,EAAmBC,EAAG,IAAMxB,EAAEH,OAAOiR,gBAE3D,YADA9Q,EAAEif,YAAa,EAGnB,KAAIjf,EAAEH,OAAO+Q,cACJrP,EAAmBC,EAAGxB,EAAEH,OAAO+Q,cADxC,CAIA,GAAI4P,GAASxgB,EAAEugB,QAAQG,SAAsB,eAAXlf,EAAEuf,KAAwBvf,EAAEyf,cAAc,GAAGC,MAAQ1f,EAAE0f,MACrFT,EAASzgB,EAAEugB,QAAQI,SAAsB,eAAXnf,EAAEuf,KAAwBvf,EAAEyf,cAAc,GAAGE,MAAQ3f,EAAE2f,KAGzF,MAAGnhB,EAAEmV,OAAOuJ,KAAO1e,EAAEH,OAAO+K,uBAAyB4V,GAAUxgB,EAAEH,OAAOgL,uBAAxE,CAgBA,GAZA4U,GAAY,EACZC,GAAU,EACVC,GAAsB,EACtBE,EAActS,OACduT,EAAcvT,OACdvN,EAAEugB,QAAQC,OAASA,EACnBxgB,EAAEugB,QAAQE,OAASA,EACnBb,EAAiBtX,KAAK+X,MACtBrgB,EAAEif,YAAa,EACfjf,EAAEkY,sBACFlY,EAAEohB,eAAiB7T,OACfvN,EAAEH,OAAO6O,UAAY,IAAGsR,GAAqB,GAClC,eAAXxe,EAAEuf,KAAuB,CACzB,GAAItb,IAAiB,CACjBjG,GAAEgC,EAAEG,QAAQC,GAAGue,KAAe1a,GAAiB,GAC/CzB,SAASC,eAAiBzE,EAAEwE,SAASC,eAAerC,GAAGue,IACvDnc,SAASC,cAAcod,OAEvB5b,GACAjE,EAAEiE,iBAGVzF,EAAEkB,KAAK,eAAgBlB,EAAGwB,OAG9BxB,EAAEqe,YAAc,SAAU7c,GAEtB,GADIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,gBACvBwd,GAA2B,cAAXrf,EAAEuf,KAAtB,CACA,GAAIvf,EAAE8f,wBAGF,MAFAthB,GAAEugB,QAAQC,OAAoB,cAAXhf,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGC,MAAQ1f,EAAE0f,WACzElhB,EAAEugB,QAAQE,OAAoB,cAAXjf,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGE,MAAQ3f,EAAE2f,MAG7E,IAAInhB,EAAEH,OAAO4O,aAQT,MANAzO,GAAEif,YAAa,OACXQ,IACAzf,EAAEugB,QAAQC,OAASxgB,EAAEugB,QAAQG,SAAsB,cAAXlf,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGC,MAAQ1f,EAAE0f,MAC9FlhB,EAAEugB,QAAQE,OAASzgB,EAAEugB,QAAQI,SAAsB,cAAXnf,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGE,MAAQ3f,EAAE2f,MAC9FvB,EAAiBtX,KAAK+X,OAI9B,IAAIQ,GAAgB7gB,EAAEH,OAAO+O,sBAAwB5O,EAAEH,OAAOkB,KAC1D,GAAKf,EAAE0D,gBAUH,GACK1D,EAAEugB,QAAQG,SAAW1gB,EAAEugB,QAAQC,QAAUxgB,EAAEsV,WAAatV,EAAEuH,gBAC1DvH,EAAEugB,QAAQG,SAAW1gB,EAAEugB,QAAQC,QAAUxgB,EAAEsV,WAAatV,EAAEsH,eAE3D,WAZJ,IACKtH,EAAEugB,QAAQI,SAAW3gB,EAAEugB,QAAQE,QAAUzgB,EAAEsV,WAAatV,EAAEuH,gBAC1DvH,EAAEugB,QAAQI,SAAW3gB,EAAEugB,QAAQE,QAAUzgB,EAAEsV,WAAatV,EAAEsH,eAE3D,MAYZ,IAAIuZ,GAAgB7c,SAASC,eACrBzC,EAAEG,SAAWqC,SAASC,eAAiBzE,EAAEgC,EAAEG,QAAQC,GAAGue,GAGtD,MAFAT,IAAU,OACV1f,EAAEif,YAAa,EAOvB,IAHIU,GACA3f,EAAEkB,KAAK,cAAelB,EAAGwB,KAEzBA,EAAEyf,eAAiBzf,EAAEyf,cAAc/e,OAAS,GAAhD,CAKA,GAHAlC,EAAEugB,QAAQG,SAAsB,cAAXlf,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGC,MAAQ1f,EAAE0f,MAC3ElhB,EAAEugB,QAAQI,SAAsB,cAAXnf,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGE,MAAQ3f,EAAE2f,MAEhD,mBAAhBtB,GAA6B,CACpC,GAAI3R,EACAlO,GAAE0D,gBAAkB1D,EAAEugB,QAAQI,WAAa3gB,EAAEugB,QAAQE,SAAWzgB,EAAE0D,gBAAkB1D,EAAEugB,QAAQG,WAAa1gB,EAAEugB,QAAQC,OACrHX,GAAc,GAGd3R,EAA4H,IAA/G9N,KAAKmhB,MAAMnhB,KAAKuG,IAAI3G,EAAEugB,QAAQI,SAAW3gB,EAAEugB,QAAQE,QAASrgB,KAAKuG,IAAI3G,EAAEugB,QAAQG,SAAW1gB,EAAEugB,QAAQC,SAAiBpgB,KAAKohB,GACvI3B,EAAc7f,EAAE0D,eAAiBwK,EAAalO,EAAEH,OAAOqO,WAAc,GAAKA,EAAalO,EAAEH,OAAOqO,YAWxG,GARI2R,GACA7f,EAAEkB,KAAK,sBAAuBlB,EAAGwB,GAEV,mBAAhBsf,IAA+B9gB,EAAEke,QAAQuD,UAC5CzhB,EAAEugB,QAAQG,WAAa1gB,EAAEugB,QAAQC,QAAUxgB,EAAEugB,QAAQI,WAAa3gB,EAAEugB,QAAQE,SAC5EK,GAAc,IAGjBrB,EAAL,CACA,GAAII,EAEA,YADAJ,GAAY,EAGhB,IAAKqB,IAAe9gB,EAAEke,QAAQuD,QAA9B,CAGAzhB,EAAEif,YAAa,EACfjf,EAAEkB,KAAK,eAAgBlB,EAAGwB,GAC1BA,EAAEiE,iBACEzF,EAAEH,OAAO8O,2BAA6B3O,EAAEH,OAAOoe,QAC/Czc,EAAE0d,kBAGDQ,IACG7f,EAAOkB,MACPf,EAAEgB,UAEN+e,EAAiB/f,EAAEiH,sBACnBjH,EAAEwH,qBAAqB,GACnBxH,EAAEyI,WACFzI,EAAE2U,QAAQ+M,QAAQ,oFAElB1hB,EAAEH,OAAOS,UAAYN,EAAEsX,cACnBtX,EAAEH,OAAOwI,6BACTrI,EAAEqB,eAGFrB,EAAE0X,iBAGVwI,GAAsB,GAElBlgB,EAAEH,OAAOgQ,YAAe7P,EAAEH,OAAO4D,oBAAqB,GAAQzD,EAAEH,OAAO8D,oBAAqB,GAC5F3D,EAAE6V,eAAc,IAGxB6J,GAAU,CAEV,IAAIkB,GAAO5gB,EAAEugB,QAAQK,KAAO5gB,EAAE0D,eAAiB1D,EAAEugB,QAAQG,SAAW1gB,EAAEugB,QAAQC,OAASxgB,EAAEugB,QAAQI,SAAW3gB,EAAEugB,QAAQE,MAEtHG,IAAc5gB,EAAEH,OAAOoO,WACnBjO,EAAEkF,MAAK0b,GAAQA,GAEnB5gB,EAAEohB,eAAiBR,EAAO,EAAI,OAAS,OACvCd,EAAmBc,EAAOb,CAE1B,IAAI4B,IAAsB,CAwB1B,IAvBKf,EAAO,GAAKd,EAAmB9f,EAAEsH,gBAClCqa,GAAsB,EAClB3hB,EAAEH,OAAO0P,aAAYuQ,EAAmB9f,EAAEsH,eAAiB,EAAIlH,KAAKwhB,KAAK5hB,EAAEsH,eAAiByY,EAAiBa,EAAM5gB,EAAEH,OAAO2P,mBAE3HoR,EAAO,GAAKd,EAAmB9f,EAAEuH,iBACtCoa,GAAsB,EAClB3hB,EAAEH,OAAO0P,aAAYuQ,EAAmB9f,EAAEuH,eAAiB,EAAInH,KAAKwhB,IAAI5hB,EAAEuH,eAAiBwY,EAAiBa,EAAM5gB,EAAEH,OAAO2P,mBAG/HmS,IACAngB,EAAE8f,yBAA0B,IAI3BthB,EAAEH,OAAO4D,kBAAyC,SAArBzD,EAAEohB,gBAA6BtB,EAAmBC,IAChFD,EAAmBC,IAElB/f,EAAEH,OAAO8D,kBAAyC,SAArB3D,EAAEohB,gBAA6BtB,EAAmBC,IAChFD,EAAmBC,GAKnB/f,EAAEH,OAAO6O,UAAY,EAAG,CACxB,KAAItO,KAAKuG,IAAIia,GAAQ5gB,EAAEH,OAAO6O,WAAasR,GAYvC,YADAF,EAAmBC,EAVnB,KAAKC,EAMD,MALAA,IAAqB,EACrBhgB,EAAEugB,QAAQC,OAASxgB,EAAEugB,QAAQG,SAC7B1gB,EAAEugB,QAAQE,OAASzgB,EAAEugB,QAAQI,SAC7Bb,EAAmBC,OACnB/f,EAAEugB,QAAQK,KAAO5gB,EAAE0D,eAAiB1D,EAAEugB,QAAQG,SAAW1gB,EAAEugB,QAAQC,OAASxgB,EAAEugB,QAAQI,SAAW3gB,EAAEugB,QAAQE,QAUlHzgB,EAAEH,OAAO2O,gBAGVxO,EAAEH,OAAOkH,UAAY/G,EAAEH,OAAO8P,sBAC9B3P,EAAE2H,oBAEF3H,EAAEH,OAAOkH,WAEiB,IAAtBuZ,EAAWpe,QACXoe,EAAWnd,MACP6D,SAAUhH,EAAEugB,QAAQvgB,EAAE0D,eAAiB,SAAW,UAClDme,KAAMjC,IAGdU,EAAWnd,MACP6D,SAAUhH,EAAEugB,QAAQvgB,EAAE0D,eAAiB,WAAa,YACpDme,MAAM,GAAKvf,QAAOgG,MAAQC,aAIlCvI,EAAE0H,eAAeoY,GAEjB9f,EAAEyH,oBAAoBqY,SAE1B9f,EAAEse,WAAa,SAAU9c,GAMrB,GALIA,EAAE6B,gBAAe7B,EAAIA,EAAE6B,eACvBsc,GACA3f,EAAEkB,KAAK,aAAclB,EAAGwB,GAE5Bme,GAAsB,EACjBF,EAAL,CAEIzf,EAAEH,OAAOgQ,YAAc6P,GAAWD,IAAezf,EAAEH,OAAO4D,oBAAqB,GAAQzD,EAAEH,OAAO8D,oBAAqB,IACrH3D,EAAE6V,eAAc,EAIpB,IAAIiM,GAAexZ,KAAK+X,MACpB0B,EAAWD,EAAelC,CA4B9B,IAzBI5f,EAAEif,aACFjf,EAAEof,mBAAmB5d,GACrBxB,EAAEkB,KAAK,QAASlB,EAAGwB,GACfugB,EAAW,KAAQD,EAAe1B,EAAiB,MAC/CH,GAAcnY,aAAamY,GAC/BA,EAAenf,WAAW,WACjBd,IACDA,EAAEH,OAAOoP,gBAAkBjP,EAAE6U,oBAAoB3S,OAAS,IAAM1C,EAAEgC,EAAEG,QAAQ0Z,SAASrb,EAAEH,OAAO4R,cAC9FzR,EAAE6U,oBAAoBmN,YAAYhiB,EAAEH,OAAOiS,uBAE/C9R,EAAEkB,KAAK,UAAWlB,EAAGwB,KACtB,MAGHugB,EAAW,KAAQD,EAAe1B,EAAiB,MAC/CH,GAAcnY,aAAamY,GAC/BjgB,EAAEkB,KAAK,cAAelB,EAAGwB,KAIjC4e,EAAgB9X,KAAK+X,MACrBvf,WAAW,WACHd,IAAGA,EAAEif,YAAa,IACvB,IAEEQ,IAAcC,IAAY1f,EAAEohB,gBAAqC,IAAnBphB,EAAEugB,QAAQK,MAAcd,IAAqBC,EAE5F,YADAN,EAAYC,GAAU,EAG1BD,GAAYC,GAAU,CAEtB,IAAIuC,EAOJ,IALIA,EADAjiB,EAAEH,OAAO2O,aACIxO,EAAEkF,IAAMlF,EAAEsV,WAAatV,EAAEsV,WAGxBwK,EAEd9f,EAAEH,OAAOkH,SAAU,CACnB,GAAIkb,GAAcjiB,EAAEsH,eAEhB,WADAtH,GAAEid,QAAQjd,EAAEW,YAGX,IAAIshB,GAAcjiB,EAAEuH,eAOrB,YANIvH,EAAES,OAAOyB,OAASlC,EAAE4X,SAAS1V,OAC7BlC,EAAEid,QAAQjd,EAAE4X,SAAS1V,OAAS,GAG9BlC,EAAEid,QAAQjd,EAAES,OAAOyB,OAAS,GAKpC,IAAIlC,EAAEH,OAAOiL,iBAAkB,CAC3B,GAAIwV,EAAWpe,OAAS,EAAG,CACvB,GAAIggB,GAAgB5B,EAAW6B,MAAOC,EAAgB9B,EAAW6B,MAE7DE,EAAWH,EAAclb,SAAWob,EAAcpb,SAClD6a,EAAOK,EAAcL,KAAOO,EAAcP,IAC9C7hB,GAAEuV,SAAW8M,EAAWR,EACxB7hB,EAAEuV,SAAWvV,EAAEuV,SAAW,EACtBnV,KAAKuG,IAAI3G,EAAEuV,UAAYvV,EAAEH,OAAOsL,0BAChCnL,EAAEuV,SAAW,IAIbsM,EAAO,MAAQ,GAAIvf,QAAOgG,MAAOC,UAAY2Z,EAAcL,KAAQ,OACnE7hB,EAAEuV,SAAW,OAGjBvV,GAAEuV,SAAW,CAEjBvV,GAAEuV,SAAWvV,EAAEuV,SAAWvV,EAAEH,OAAOqL,8BAEnCoV,EAAWpe,OAAS,CACpB,IAAIogB,GAAmB,IAAOtiB,EAAEH,OAAOkL,sBACnCwX,EAAmBviB,EAAEuV,SAAW+M,EAEhCE,EAAcxiB,EAAEsV,UAAYiN,CAC5BviB,GAAEkF,MAAKsd,GAAgBA,EAC3B,IACIC,GADAC,GAAW,EAEXC,EAAsC,GAAvBviB,KAAKuG,IAAI3G,EAAEuV,UAAiBvV,EAAEH,OAAOoL,2BACxD,IAAIuX,EAAcxiB,EAAEuH,eACZvH,EAAEH,OAAOmL,wBACLwX,EAAcxiB,EAAEuH,gBAAkBob,IAClCH,EAAcxiB,EAAEuH,eAAiBob,GAErCF,EAAsBziB,EAAEuH,eACxBmb,GAAW,EACXxC,GAAsB,GAGtBsC,EAAcxiB,EAAEuH,mBAGnB,IAAIib,EAAcxiB,EAAEsH,eACjBtH,EAAEH,OAAOmL,wBACLwX,EAAcxiB,EAAEsH,eAAiBqb,IACjCH,EAAcxiB,EAAEsH,eAAiBqb,GAErCF,EAAsBziB,EAAEsH,eACxBob,GAAW,EACXxC,GAAsB,GAGtBsC,EAAcxiB,EAAEsH,mBAGnB,IAAItH,EAAEH,OAAOgI,eAAgB,CAC9B,GACIyT,GADAsH,EAAI,CAER,KAAKA,EAAI,EAAGA,EAAI5iB,EAAE4X,SAAS1V,OAAQ0gB,GAAK,EACpC,GAAI5iB,EAAE4X,SAASgL,IAAMJ,EAAa,CAC9BlH,EAAYsH,CACZ,OAKJJ,EADApiB,KAAKuG,IAAI3G,EAAE4X,SAAS0D,GAAakH,GAAepiB,KAAKuG,IAAI3G,EAAE4X,SAAS0D,EAAY,GAAKkH,IAAqC,SAArBxiB,EAAEohB,eACzFphB,EAAE4X,SAAS0D,GAEXtb,EAAE4X,SAAS0D,EAAY,GAEpCtb,EAAEkF,MAAKsd,GAAgBA,GAGhC,GAAmB,IAAfxiB,EAAEuV,SAEE+M,EADAtiB,EAAEkF,IACiB9E,KAAKuG,MAAM6b,EAAcxiB,EAAEsV,WAAatV,EAAEuV,UAG1CnV,KAAKuG,KAAK6b,EAAcxiB,EAAEsV,WAAatV,EAAEuV,cAG/D,IAAIvV,EAAEH,OAAOgI,eAEd,WADA7H,GAAEiI,YAIFjI,GAAEH,OAAOmL,wBAA0B0X,GACnC1iB,EAAE0H,eAAe+a,GACjBziB,EAAEwH,qBAAqB8a,GACvBtiB,EAAEyH,oBAAoB+a,GACtBxiB,EAAE6iB,oBACF7iB,EAAEyI,WAAY,EACdzI,EAAE2U,QAAQgD,cAAc,WACf3X,GAAMkgB,IACXlgB,EAAEkB,KAAK,mBAAoBlB,GAE3BA,EAAEwH,qBAAqBxH,EAAEH,OAAO8K,OAChC3K,EAAEyH,oBAAoBgb,GACtBziB,EAAE2U,QAAQgD,cAAc,WACf3X,GACLA,EAAE8iB,wBAGH9iB,EAAEuV,UACTvV,EAAE0H,eAAe8a,GACjBxiB,EAAEwH,qBAAqB8a,GACvBtiB,EAAEyH,oBAAoB+a,GACtBxiB,EAAE6iB,oBACG7iB,EAAEyI,YACHzI,EAAEyI,WAAY,EACdzI,EAAE2U,QAAQgD,cAAc,WACf3X,GACLA,EAAE8iB,sBAKV9iB,EAAE0H,eAAe8a,GAGrBxiB,EAAE2H,oBAMN,cAJK3H,EAAEH,OAAOiL,kBAAoBiX,GAAY/hB,EAAEH,OAAO0O,gBACnDvO,EAAE0H,iBACF1H,EAAE2H,sBAMV,GAAIpC,GAAGwd,EAAY,EAAGC,EAAYhjB,EAAEwY,gBAAgB,EACpD,KAAKjT,EAAI,EAAGA,EAAIvF,EAAEuY,WAAWrW,OAAQqD,GAAKvF,EAAEH,OAAO+N,eACU,mBAA9C5N,GAAEuY,WAAWhT,EAAIvF,EAAEH,OAAO+N,gBAC7BqU,GAAcjiB,EAAEuY,WAAWhT,IAAM0c,EAAajiB,EAAEuY,WAAWhT,EAAIvF,EAAEH,OAAO+N,kBACxEmV,EAAYxd,EACZyd,EAAYhjB,EAAEuY,WAAWhT,EAAIvF,EAAEH,OAAO+N,gBAAkB5N,EAAEuY,WAAWhT,IAIrE0c,GAAcjiB,EAAEuY,WAAWhT,KAC3Bwd,EAAYxd,EACZyd,EAAYhjB,EAAEuY,WAAWvY,EAAEuY,WAAWrW,OAAS,GAAKlC,EAAEuY,WAAWvY,EAAEuY,WAAWrW,OAAS,GAMnG,IAAI+gB,IAAShB,EAAajiB,EAAEuY,WAAWwK,IAAcC,CAErD,IAAIjB,EAAW/hB,EAAEH,OAAO0O,aAAc,CAElC,IAAKvO,EAAEH,OAAOwO,WAEV,WADArO,GAAEid,QAAQjd,EAAEW,YAGS,UAArBX,EAAEohB,iBACE6B,GAASjjB,EAAEH,OAAOyO,gBAAiBtO,EAAEid,QAAQ8F,EAAY/iB,EAAEH,OAAO+N,gBACjE5N,EAAEid,QAAQ8F,IAGM,SAArB/iB,EAAEohB,iBACE6B,EAAS,EAAIjjB,EAAEH,OAAOyO,gBAAkBtO,EAAEid,QAAQ8F,EAAY/iB,EAAEH,OAAO+N,gBACtE5N,EAAEid,QAAQ8F,QAGlB,CAED,IAAK/iB,EAAEH,OAAOuO,YAEV,WADApO,GAAEid,QAAQjd,EAAEW,YAGS,UAArBX,EAAEohB,gBACFphB,EAAEid,QAAQ8F,EAAY/iB,EAAEH,OAAO+N,gBAGV,SAArB5N,EAAEohB,gBACFphB,EAAEid,QAAQ8F,MAOtB/iB,EAAEsB,SAAW,SAAU4hB,EAAYvY,GAC/B,MAAO3K,GAAEid,QAAQiG,EAAYvY,GAAO,GAAM,IAE9C3K,EAAEid,QAAU,SAAUiG,EAAYvY,EAAOwY,EAAc1L,GACvB,mBAAjB0L,KAA8BA,GAAe,GAC9B,mBAAfD,KAA4BA,EAAa,GAChDA,EAAa,IAAGA,EAAa,GACjCljB,EAAEib,UAAY7a,KAAKC,MAAM6iB,EAAaljB,EAAEH,OAAO+N,gBAC3C5N,EAAEib,WAAajb,EAAE4X,SAAS1V,SAAQlC,EAAEib,UAAYjb,EAAE4X,SAAS1V,OAAS,EAExE,IAAIoT,IAActV,EAAE4X,SAAS5X,EAAEib,UAc/B,IAZIjb,EAAEH,OAAOS,UAAYN,EAAEsX,cACnBG,IAAazX,EAAEH,OAAOwI,6BACtBrI,EAAE0X,cAAc/M,GAGhB3K,EAAEqB,gBAIVrB,EAAE0H,eAAe4N,GAGdtV,EAAEH,OAAO8Q,oBACR,IAAK,GAAIpL,GAAI,EAAGA,EAAIvF,EAAEuY,WAAWrW,OAAQqD,KAC/BnF,KAAKC,MAAkB,IAAZiV,IAAoBlV,KAAKC,MAAwB,IAAlBL,EAAEuY,WAAWhT,MACzD2d,EAAa3d,EAMzB,UAAKvF,EAAEH,OAAO4D,kBAAoB6R,EAAYtV,EAAEsV,WAAaA,EAAYtV,EAAEsH,qBAGtEtH,EAAEH,OAAO8D,kBAAoB2R,EAAYtV,EAAEsV,WAAaA,EAAYtV,EAAEuH,iBAClEvH,EAAEW,aAAe,KAAOuiB,KAIZ,mBAAVvY,KAAuBA,EAAQ3K,EAAEH,OAAO8K,OACnD3K,EAAEkb,cAAgBlb,EAAEW,aAAe,EACnCX,EAAEW,YAAcuiB,EAChBljB,EAAEmb,kBACGnb,EAAEkF,MAAQoQ,IAActV,EAAEsV,YAAgBtV,EAAEkF,KAAOoQ,IAActV,EAAEsV,WAEhEtV,EAAEH,OAAOuL,YACTpL,EAAE6X,mBAEN7X,EAAE4H,gBACsB,UAApB5H,EAAEH,OAAO0L,QACTvL,EAAEyH,oBAAoB6N,IAEnB,IAEXtV,EAAE4H,gBACF5H,EAAE6iB,kBAAkBM,GAEN,IAAVxY,GAAe3K,EAAEke,QAAQkF,QACzBpjB,EAAEyH,oBAAoB6N,GACtBtV,EAAEwH,qBAAqB,GACvBxH,EAAE8iB,gBAAgBK,KAGlBnjB,EAAEyH,oBAAoB6N,GACtBtV,EAAEwH,qBAAqBmD,GAClB3K,EAAEyI,YACHzI,EAAEyI,WAAY,EACdzI,EAAE2U,QAAQgD,cAAc,WACf3X,GACLA,EAAE8iB,gBAAgBK,QAMvB,MAGXnjB,EAAE6iB,kBAAoB,SAAUM,GACA,mBAAjBA,KAA8BA,GAAe,GACpDnjB,EAAEH,OAAOuL,YACTpL,EAAE6X,mBAEF7X,EAAEmI,MAAMnI,EAAEmI,KAAK0a,oBACfM,IACAnjB,EAAEkB,KAAK,oBAAqBlB,GACxBA,EAAEW,cAAgBX,EAAEkb,gBACpBlb,EAAEkB,KAAK,qBAAsBlB,GACzBA,EAAEW,YAAcX,EAAEkb,cAClBlb,EAAEkB,KAAK,mBAAoBlB,GAG3BA,EAAEkB,KAAK,mBAAoBlB,MAM3CA,EAAE8iB,gBAAkB,SAAUK,GAC1BnjB,EAAEyI,WAAY,EACdzI,EAAEwH,qBAAqB,GACK,mBAAjB2b,KAA8BA,GAAe,GACpDnjB,EAAEmI,MAAMnI,EAAEmI,KAAK2a,kBACfK,IACAnjB,EAAEkB,KAAK,kBAAmBlB,GACtBA,EAAEW,cAAgBX,EAAEkb,gBACpBlb,EAAEkB,KAAK,mBAAoBlB,GACvBA,EAAEW,YAAcX,EAAEkb,cAClBlb,EAAEkB,KAAK,iBAAkBlB,GAGzBA,EAAEkB,KAAK,iBAAkBlB,KAIjCA,EAAEH,OAAOuN,SAAWpN,EAAEoN,SACtBpN,EAAEoN,QAAQiW,WAAWrjB,EAAEH,OAAOuN,QAASpN,EAAEW,aAEzCX,EAAEH,OAAOqN,SAAWlN,EAAEkN,SACtBlN,EAAEkN,QAAQoW,WAIlBtjB,EAAE2F,UAAY,SAAUwd,EAAcxY,EAAO8M,GACzC,GAAIzX,EAAEH,OAAOkB,KAAM,CACf,GAAIf,EAAEyI,UAAW,OAAO,CACxBzI,GAAEgB,SACehB,GAAEC,UAAU,GAAGsjB,UAChC,OAAOvjB,GAAEid,QAAQjd,EAAEW,YAAcX,EAAEH,OAAO+N,eAAgBjD,EAAOwY,EAAc1L,GAE9E,MAAOzX,GAAEid,QAAQjd,EAAEW,YAAcX,EAAEH,OAAO+N,eAAgBjD,EAAOwY,EAAc1L,IAExFzX,EAAEiB,WAAa,SAAU0J,GACrB,MAAO3K,GAAE2F,WAAU,EAAMgF,GAAO,IAEpC3K,EAAE4F,UAAY,SAAUud,EAAcxY,EAAO8M,GACzC,GAAIzX,EAAEH,OAAOkB,KAAM,CACf,GAAIf,EAAEyI,UAAW,OAAO,CACxBzI,GAAEgB,SACehB,GAAEC,UAAU,GAAGsjB,UAChC,OAAOvjB,GAAEid,QAAQjd,EAAEW,YAAc,EAAGgK,EAAOwY,EAAc1L,GAExD,MAAOzX,GAAEid,QAAQjd,EAAEW,YAAc,EAAGgK,EAAOwY,EAAc1L,IAElEzX,EAAEwjB,WAAa,SAAU7Y,GACrB,MAAO3K,GAAE4F,WAAU,EAAM+E,GAAO,IAEpC3K,EAAEiI,WAAa,SAAUkb,EAAcxY,EAAO8M,GAC1C,MAAOzX,GAAEid,QAAQjd,EAAEW,YAAagK,EAAOwY,IAG3CnjB,EAAEyjB,oBAAsB,WAEpB,MADAzjB,GAAEH,OAAO4O,cAAe,GACjB,GAEXzO,EAAE0jB,mBAAqB,WAEnB,MADA1jB,GAAEH,OAAO4O,cAAe,GACjB,GAMXzO,EAAEwH,qBAAuB,SAAUmc,EAAUC,GACzC5jB,EAAE2U,QAAQsH,WAAW0H,GACG,UAApB3jB,EAAEH,OAAO0L,QAAsBvL,EAAE6jB,QAAQ7jB,EAAEH,OAAO0L,SAClDvL,EAAE6jB,QAAQ7jB,EAAEH,OAAO0L,QAAQuY,cAAcH,GAEzC3jB,EAAEH,OAAOyM,UAAYtM,EAAEsM,UACvBtM,EAAEsM,SAASwX,cAAcH,GAEzB3jB,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAUmX,cAAcH,GAE1B3jB,EAAEH,OAAO2Q,SAAWxQ,EAAE+c,YACtB/c,EAAE+c,WAAW+G,cAAcH,EAAUC,GAEzC5jB,EAAEkB,KAAK,kBAAmBlB,EAAG2jB,IAEjC3jB,EAAEyH,oBAAsB,SAAU6N,EAAW3N,EAAmBic,GAC5D,GAAIG,GAAI,EAAGC,EAAI,EAAGC,EAAI,CAClBjkB,GAAE0D,eACFqgB,EAAI/jB,EAAEkF,KAAOoQ,EAAYA,EAGzB0O,EAAI1O,EAGJtV,EAAEH,OAAOmO,eACT+V,EAAI7jB,EAAM6jB,GACVC,EAAI9jB,EAAM8jB,IAGThkB,EAAEH,OAAOyL,mBACNtL,EAAEuU,QAAQE,aAAczU,EAAE2U,QAAQxK,UAAU,eAAiB4Z,EAAI,OAASC,EAAI,OAASC,EAAI,OAC1FjkB,EAAE2U,QAAQxK,UAAU,aAAe4Z,EAAI,OAASC,EAAI,QAG7DhkB,EAAEsV,UAAYtV,EAAE0D,eAAiBqgB,EAAIC,CAGrC,IAAIja,GACAgR,EAAiB/a,EAAEuH,eAAiBvH,EAAEsH,cAEtCyC,GADmB,IAAnBgR,EACW,GAGCzF,EAAYtV,EAAEsH,gBAAkB,EAE5CyC,IAAa/J,EAAE+J,UACf/J,EAAE0H,eAAe4N,GAGjB3N,GAAmB3H,EAAE2H,oBACD,UAApB3H,EAAEH,OAAO0L,QAAsBvL,EAAE6jB,QAAQ7jB,EAAEH,OAAO0L,SAClDvL,EAAE6jB,QAAQ7jB,EAAEH,OAAO0L,QAAQ2Y,aAAalkB,EAAEsV,WAE1CtV,EAAEH,OAAOyM,UAAYtM,EAAEsM,UACvBtM,EAAEsM,SAAS4X,aAAalkB,EAAEsV,WAE1BtV,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACxB3M,EAAE2M,UAAUuX,aAAalkB,EAAEsV,WAE3BtV,EAAEH,OAAO2Q,SAAWxQ,EAAE+c,YACtB/c,EAAE+c,WAAWmH,aAAalkB,EAAEsV,UAAWsO,GAE3C5jB,EAAEkB,KAAK,iBAAkBlB,EAAGA,EAAEsV,YAGlCtV,EAAEmkB,aAAe,SAAUziB,EAAI6H,GAC3B,GAAI6a,GAAQC,EAAcC,EAAUC,CAOpC,OAJoB,mBAAThb,KACPA,EAAO,KAGPvJ,EAAEH,OAAOyL,iBACFtL,EAAEkF,KAAOlF,EAAEsV,UAAYtV,EAAEsV,WAGpCgP,EAAWhiB,OAAOkiB,iBAAiB9iB,EAAI,MACnCY,OAAOmiB,iBACPJ,EAAeC,EAASna,WAAama,EAASI,gBAC1CL,EAAaM,MAAM,KAAKziB,OAAS,IACjCmiB,EAAeA,EAAaM,MAAM,MAAMC,IAAI,SAASzkB,GACjD,MAAOA,GAAEyY,QAAQ,IAAI,OACtBvD,KAAK,OAIZkP,EAAkB,GAAIjiB,QAAOmiB,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAG5EE,EAAkBD,EAASO,cAAgBP,EAASQ,YAAcR,EAASS,aAAeT,EAASU,aAAgBV,EAASna,WAAama,EAASW,iBAAiB,aAAarM,QAAQ,aAAc,sBACtMwL,EAASG,EAAgBW,WAAWP,MAAM,MAGjC,MAATpb,IAGI8a,EADA/hB,OAAOmiB,gBACQF,EAAgBY,IAER,KAAlBf,EAAOliB,OACGyW,WAAWyL,EAAO,KAGlBzL,WAAWyL,EAAO,KAE5B,MAAT7a,IAGI8a,EADA/hB,OAAOmiB,gBACQF,EAAgBa,IAER,KAAlBhB,EAAOliB,OACGyW,WAAWyL,EAAO,KAGlBzL,WAAWyL,EAAO,KAErCpkB,EAAEkF,KAAOmf,IAAcA,GAAgBA,GACpCA,GAAgB,IAE3BrkB,EAAEiH,oBAAsB,SAAUsC,GAI9B,MAHoB,mBAATA,KACPA,EAAOvJ,EAAE0D,eAAiB,IAAM,KAE7B1D,EAAEmkB,aAAankB,EAAE2U,QAAQ,GAAIpL,IAMxCvJ,EAAEkD,aAoBFlD,EAAEqlB,cAAgB,WACd,GAAIrlB,EAAEH,OAAO4S,eAET,IAAK,GADD6S,GAAmBtlB,EAAEC,UAAU4B,UAC1B0D,EAAI,EAAGA,EAAI+f,EAAiBpjB,OAAQqD,IACzCpD,EAAamjB,EAAiB/f,GAKtCpD,GAAanC,EAAEC,UAAU,IAAK+C,WAAW,IAGzCb,EAAanC,EAAE2U,QAAQ,IAAK5R,YAAY,KAE5C/C,EAAEulB,oBAAsB,WACpB,IAAK,GAAIhgB,GAAI,EAAGA,EAAIvF,EAAEkD,UAAUhB,OAAQqD,IACpCvF,EAAEkD,UAAUqC,GAAGigB,YAEnBxlB,GAAEkD,cAMNlD,EAAEylB,WAAa,WAEXzlB,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,qBAAqBuU,QAEnF,IAAIjlB,GAAST,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAEjB,UAA3BrE,EAAEH,OAAO4N,eAA6BzN,EAAEH,OAAO0Q,eAAcvQ,EAAEH,OAAO0Q,aAAe9P,EAAOyB,QAE/FlC,EAAEuQ,aAAerG,SAASlK,EAAEH,OAAO0Q,cAAgBvQ,EAAEH,OAAO4N,cAAe,IAC3EzN,EAAEuQ,aAAevQ,EAAEuQ,aAAevQ,EAAEH,OAAOyQ,qBACvCtQ,EAAEuQ,aAAe9P,EAAOyB,SACxBlC,EAAEuQ,aAAe9P,EAAOyB,OAG5B,IAA2CqD,GAAvCogB,KAAoBC,IAOxB,KANAnlB,EAAOV,KAAK,SAAUiC,EAAON,GACzB,GAAI6X,GAAQ/Z,EAAED,KACVyC,GAAQhC,EAAEuQ,cAAcqV,EAAaziB,KAAKzB,GAC1CM,EAAQvB,EAAOyB,QAAUF,GAASvB,EAAOyB,OAASlC,EAAEuQ,cAAcoV,EAAcxiB,KAAKzB,GACzF6X,EAAM3Y,KAAK,0BAA2BoB,KAErCuD,EAAI,EAAGA,EAAIqgB,EAAa1jB,OAAQqD,IACjCvF,EAAE2U,QAAQkR,OAAOrmB,EAAEomB,EAAargB,GAAGugB,WAAU,IAAO/Q,SAAS/U,EAAEH,OAAOsR,qBAE1E,KAAK5L,EAAIogB,EAAczjB,OAAS,EAAGqD,GAAK,EAAGA,IACvCvF,EAAE2U,QAAQoR,QAAQvmB,EAAEmmB,EAAcpgB,GAAGugB,WAAU,IAAO/Q,SAAS/U,EAAEH,OAAOsR,uBAGhFnR,EAAEoU,YAAc,WACZpU,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,IAAMrE,EAAEH,OAAOsR,qBAAqBuU,SACnF1lB,EAAES,OAAOulB,WAAW,4BAExBhmB,EAAEqU,OAAS,SAAU4R,GACjB,GAAIC,GAAWlmB,EAAEW,YAAcX,EAAEuQ,YACjCvQ,GAAEoU,cACFpU,EAAEylB,aACFzlB,EAAEsY,mBACE2N,GACAjmB,EAAEid,QAAQiJ,EAAWlmB,EAAEuQ,aAAc,GAAG,IAIhDvQ,EAAEgB,QAAU,WACR,GAAImlB,EAEAnmB,GAAEW,YAAcX,EAAEuQ,cAClB4V,EAAWnmB,EAAES,OAAOyB,OAA0B,EAAjBlC,EAAEuQ,aAAmBvQ,EAAEW,YACpDwlB,GAAsBnmB,EAAEuQ,aACxBvQ,EAAEid,QAAQkJ,EAAU,GAAG,GAAO,KAGG,SAA3BnmB,EAAEH,OAAO4N,eAA4BzN,EAAEW,aAAgC,EAAjBX,EAAEuQ,cAAsBvQ,EAAEW,YAAcX,EAAES,OAAOyB,OAAkC,EAAzBlC,EAAEH,OAAO4N,iBAC/H0Y,GAAYnmB,EAAES,OAAOyB,OAASlC,EAAEW,YAAcX,EAAEuQ,aAChD4V,GAAsBnmB,EAAEuQ,aACxBvQ,EAAEid,QAAQkJ,EAAU,GAAG,GAAO,KAMtCnmB,EAAEomB,YAAc,SAAU3lB,GAItB,GAHIT,EAAEH,OAAOkB,MACTf,EAAEoU,cAEgB,gBAAX3T,IAAuBA,EAAOyB,OACrC,IAAK,GAAIqD,GAAI,EAAGA,EAAI9E,EAAOyB,OAAQqD,IAC3B9E,EAAO8E,IAAIvF,EAAE2U,QAAQkR,OAAOplB,EAAO8E,QAI3CvF,GAAE2U,QAAQkR,OAAOplB,EAEjBT,GAAEH,OAAOkB,MACTf,EAAEylB,aAEAzlB,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UACjCzC,EAAEmX,QAAO,IAGjBnX,EAAEqmB,aAAe,SAAU5lB,GACnBT,EAAEH,OAAOkB,MACTf,EAAEoU,aAEN,IAAI4G,GAAiBhb,EAAEW,YAAc,CACrC,IAAsB,gBAAXF,IAAuBA,EAAOyB,OAAQ,CAC7C,IAAK,GAAIqD,GAAI,EAAGA,EAAI9E,EAAOyB,OAAQqD,IAC3B9E,EAAO8E,IAAIvF,EAAE2U,QAAQoR,QAAQtlB,EAAO8E,GAE5CyV,GAAiBhb,EAAEW,YAAcF,EAAOyB,WAGxClC,GAAE2U,QAAQoR,QAAQtlB,EAElBT,GAAEH,OAAOkB,MACTf,EAAEylB,aAEAzlB,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UACjCzC,EAAEmX,QAAO,GAEbnX,EAAEid,QAAQjC,EAAgB,GAAG,IAEjChb,EAAEsmB,YAAc,SAAUC,GAClBvmB,EAAEH,OAAOkB,OACTf,EAAEoU,cACFpU,EAAES,OAAST,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,YAEjD,IACImiB,GADAxL,EAAiBhb,EAAEW,WAEvB,IAA6B,gBAAlB4lB,IAA8BA,EAAcrkB,OAAQ,CAC3D,IAAK,GAAIqD,GAAI,EAAGA,EAAIghB,EAAcrkB,OAAQqD,IACtCihB,EAAgBD,EAAchhB,GAC1BvF,EAAES,OAAO+lB,IAAgBxmB,EAAES,OAAOC,GAAG8lB,GAAed,SACpDc,EAAgBxL,GAAgBA,GAExCA,GAAiB5a,KAAK+Y,IAAI6B,EAAgB,OAG1CwL,GAAgBD,EACZvmB,EAAES,OAAO+lB,IAAgBxmB,EAAES,OAAOC,GAAG8lB,GAAed,SACpDc,EAAgBxL,GAAgBA,IACpCA,EAAiB5a,KAAK+Y,IAAI6B,EAAgB,EAG1Chb,GAAEH,OAAOkB,MACTf,EAAEylB,aAGAzlB,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UACjCzC,EAAEmX,QAAO,GAETnX,EAAEH,OAAOkB,KACTf,EAAEid,QAAQjC,EAAiBhb,EAAEuQ,aAAc,GAAG,GAG9CvQ,EAAEid,QAAQjC,EAAgB,GAAG,IAIrChb,EAAEymB,gBAAkB,WAEhB,IAAK,GADDF,MACKhhB,EAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IACjCghB,EAAcpjB,KAAKoC,EAEvBvF,GAAEsmB,YAAYC,IAOlBvmB,EAAE6jB,SACEzX,MACI8X,aAAc,WACV,IAAK,GAAI3e,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBN,EAASsU,EAAM,GAAGc,kBAClBqM,GAAMzhB,CACLjF,GAAEH,OAAOyL,mBAAkBob,GAAU1mB,EAAEsV,UAC5C,IAAIqR,GAAK,CACJ3mB,GAAE0D,iBACHijB,EAAKD,EACLA,EAAK,EAET,IAAIE,GAAe5mB,EAAEH,OAAOuM,KAAKC,UACzBjM,KAAK+Y,IAAI,EAAI/Y,KAAKuG,IAAI4S,EAAM,GAAGxP,UAAW,GAC1C,EAAI3J,KAAKwc,IAAIxc,KAAK+Y,IAAII,EAAM,GAAGxP,UAAU,GAAK,EACtDwP,GACKtE,KACG4R,QAASD,IAEZzc,UAAU,eAAiBuc,EAAK,OAASC,EAAK,cAK3D7C,cAAe,SAAUH,GAErB,GADA3jB,EAAES,OAAOwb,WAAW0H,GAChB3jB,EAAEH,OAAOyL,kBAAiC,IAAbqY,EAAgB,CAC7C,GAAImD,IAAiB,CACrB9mB,GAAES,OAAOkX,cAAc,WACnB,IAAImP,GACC9mB,EAAL,CACA8mB,GAAiB,EACjB9mB,EAAEyI,WAAY,CAEd,KAAK,GADDse,IAAiB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACzFxhB,EAAI,EAAGA,EAAIwhB,EAAc7kB,OAAQqD,IACtCvF,EAAE2U,QAAQ+M,QAAQqF,EAAcxhB,UAMpDuG,MACIoY,aAAc,WACV,IAAK,GAAI3e,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBwE,EAAWwP,EAAM,GAAGxP,QACpB/J,GAAEH,OAAOiM,KAAKC,gBACdhC,EAAW3J,KAAK+Y,IAAI/Y,KAAKwc,IAAIrD,EAAM,GAAGxP,SAAU,IAAI,GAExD,IAAI9E,GAASsU,EAAM,GAAGc,kBAClB5O,GAAS,IAAO1B,EAChBid,EAAUvb,EACVwb,EAAU,EACVP,GAAMzhB,EACN0hB,EAAK,CAaT,IAZK3mB,EAAE0D,eAME1D,EAAEkF,MACP8hB,GAAWA,IANXL,EAAKD,EACLA,EAAK,EACLO,GAAWD,EACXA,EAAU,GAMdzN,EAAM,GAAGtD,MAAMiR,QAAU9mB,KAAKuG,IAAIvG,KAAKF,MAAM6J,IAAa/J,EAAES,OAAOyB,OAE/DlC,EAAEH,OAAOiM,KAAKD,aAAc,CAE5B,GAAIsb,GAAennB,EAAE0D,eAAiB6V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFsS,EAAcpnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBqS,EAAajlB,SACbilB,EAAe3nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,OAAS,OAAS,YAC5F6V,EAAMsM,OAAOsB,IAEU,IAAvBC,EAAYllB,SACZklB,EAAc5nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,QAAU,UAAY,YAC/F6V,EAAMsM,OAAOuB,IAEbD,EAAajlB,SAAQilB,EAAa,GAAGlR,MAAM4Q,QAAUzmB,KAAK+Y,KAAKpP,EAAU,IACzEqd,EAAYllB,SAAQklB,EAAY,GAAGnR,MAAM4Q,QAAUzmB,KAAK+Y,IAAIpP,EAAU,IAG9EwP,EACKpP,UAAU,eAAiBuc,EAAK,OAASC,EAAK,oBAAsBM,EAAU,gBAAkBD,EAAU,UAGvHlD,cAAe,SAAUH,GAErB,GADA3jB,EAAES,OAAOwb,WAAW0H,GAAU7O,KAAK,gHAAgHmH,WAAW0H,GAC1J3jB,EAAEH,OAAOyL,kBAAiC,IAAbqY,EAAgB,CAC7C,GAAImD,IAAiB,CACrB9mB,GAAES,OAAOC,GAAGV,EAAEW,aAAagX,cAAc,WACrC,IAAImP,GACC9mB,GACAR,EAAED,MAAM8b,SAASrb,EAAEH,OAAOyE,kBAA/B,CACAwiB,GAAiB,EACjB9mB,EAAEyI,WAAY,CAEd,KAAK,GADDse,IAAiB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACzFxhB,EAAI,EAAGA,EAAIwhB,EAAc7kB,OAAQqD,IACtCvF,EAAE2U,QAAQ+M,QAAQqF,EAAcxhB,UAMpDyG,MACIkY,aAAc,WACV,GAAuBmD,GAAnBC,EAAgB,CAChBtnB,GAAEH,OAAOmM,KAAKC,SACVjM,EAAE0D,gBACF2jB,EAAarnB,EAAE2U,QAAQG,KAAK,uBACF,IAAtBuS,EAAWnlB,SACXmlB,EAAa7nB,EAAE,0CACfQ,EAAE2U,QAAQkR,OAAOwB,IAErBA,EAAWpS,KAAK3P,OAAQtF,EAAEqF,MAAQ,SAGlCgiB,EAAarnB,EAAEC,UAAU6U,KAAK,uBACJ,IAAtBuS,EAAWnlB,SACXmlB,EAAa7nB,EAAE,0CACfQ,EAAEC,UAAU4lB,OAAOwB,KAI/B,KAAK,GAAI9hB,GAAI,EAAGA,EAAIvF,EAAES,OAAOyB,OAAQqD,IAAK,CACtC,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBgiB,EAAiB,GAAJhiB,EACbrF,EAAQE,KAAKC,MAAMknB,EAAa,IAChCvnB,GAAEkF,MACFqiB,GAAcA,EACdrnB,EAAQE,KAAKC,OAAOknB,EAAa,KAErC,IAAIxd,GAAW3J,KAAK+Y,IAAI/Y,KAAKwc,IAAIrD,EAAM,GAAGxP,SAAU,IAAI,GACpD2c,EAAK,EAAGC,EAAK,EAAGa,EAAK,CACrBjiB,GAAI,IAAM,GACVmhB,EAAe,GAARxmB,EAAYF,EAAEqY,KACrBmP,EAAK,IAECjiB,EAAI,GAAK,IAAM,GACrBmhB,EAAK,EACLc,EAAe,GAARtnB,EAAYF,EAAEqY,OAEf9S,EAAI,GAAK,IAAM,GACrBmhB,EAAK1mB,EAAEqY,KAAe,EAARnY,EAAYF,EAAEqY,KAC5BmP,EAAKxnB,EAAEqY,OAED9S,EAAI,GAAK,IAAM,IACrBmhB,GAAO1mB,EAAEqY,KACTmP,EAAK,EAAIxnB,EAAEqY,KAAgB,EAATrY,EAAEqY,KAAWnY,GAE/BF,EAAEkF,MACFwhB,GAAMA,GAGL1mB,EAAE0D,iBACHijB,EAAKD,EACLA,EAAK,EAGT,IAAIvc,GAAY,YAAcnK,EAAE0D,eAAiB,GAAK6jB,GAAc,iBAAmBvnB,EAAE0D,eAAiB6jB,EAAa,GAAK,oBAAsBb,EAAK,OAASC,EAAK,OAASa,EAAK,KAMnL,IALIzd,GAAY,GAAKA,GAAW,IAC5Bud,EAAoB,GAAJ/hB,EAAoB,GAAXwE,EACrB/J,EAAEkF,MAAKoiB,EAAqB,IAAJ/hB,EAAoB,GAAXwE,IAEzCwP,EAAMpP,UAAUA,GACZnK,EAAEH,OAAOmM,KAAKH,aAAc,CAE5B,GAAIsb,GAAennB,EAAE0D,eAAiB6V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFsS,EAAcpnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBqS,EAAajlB,SACbilB,EAAe3nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,OAAS,OAAS,YAC5F6V,EAAMsM,OAAOsB,IAEU,IAAvBC,EAAYllB,SACZklB,EAAc5nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,QAAU,UAAY,YAC/F6V,EAAMsM,OAAOuB,IAEbD,EAAajlB,SAAQilB,EAAa,GAAGlR,MAAM4Q,QAAUzmB,KAAK+Y,KAAKpP,EAAU,IACzEqd,EAAYllB,SAAQklB,EAAY,GAAGnR,MAAM4Q,QAAUzmB,KAAK+Y,IAAIpP,EAAU,KAUlF,GAPA/J,EAAE2U,QAAQM,KACNwS,2BAA4B,YAAeznB,EAAEqY,KAAO,EAAK,KACzDqP,wBAAyB,YAAe1nB,EAAEqY,KAAO,EAAK,KACtDsP,uBAAwB,YAAe3nB,EAAEqY,KAAO,EAAK,KACrDuP,mBAAoB,YAAe5nB,EAAEqY,KAAO,EAAK,OAGjDrY,EAAEH,OAAOmM,KAAKC,OACd,GAAIjM,EAAE0D,eACF2jB,EAAWld,UAAU,qBAAuBnK,EAAEqF,MAAQ,EAAIrF,EAAEH,OAAOmM,KAAKE,cAAgB,QAAWlM,EAAEqF,MAAQ,EAAK,0CAA6CrF,EAAEH,OAAOmM,KAAgB,YAAI,SAE3L,CACD,GAAI6b,GAAcznB,KAAKuG,IAAI2gB,GAA4D,GAA3ClnB,KAAKC,MAAMD,KAAKuG,IAAI2gB,GAAiB,IAC7EQ,EAAa,KAAO1nB,KAAK2nB,IAAkB,EAAdF,EAAkBznB,KAAKohB,GAAK,KAAO,EAAIphB,KAAK4nB,IAAkB,EAAdH,EAAkBznB,KAAKohB,GAAK,KAAO,GAChHyG,EAASjoB,EAAEH,OAAOmM,KAAKG,YACvB+b,EAASloB,EAAEH,OAAOmM,KAAKG,YAAc2b,EACrC7iB,EAASjF,EAAEH,OAAOmM,KAAKE,YAC3Bmb,GAAWld,UAAU,WAAa8d,EAAS,QAAUC,EAAS,uBAAyBloB,EAAEsF,OAAS,EAAIL,GAAU,QAAWjF,EAAEsF,OAAS,EAAI4iB,EAAU,uBAG5J,GAAIC,GAAWnoB,EAAEooB,UAAYpoB,EAAEqoB,aAAiBroB,EAAEqY,KAAO,EAAK,CAC9DrY,GAAE2U,QAAQxK,UAAU,qBAAuBge,EAAU,gBAAkBnoB,EAAE0D,eAAiB,EAAI4jB,GAAiB,iBAAmBtnB,EAAE0D,gBAAkB4jB,EAAgB,GAAK,SAE/KxD,cAAe,SAAUH,GACrB3jB,EAAES,OAAOwb,WAAW0H,GAAU7O,KAAK,gHAAgHmH,WAAW0H,GAC1J3jB,EAAEH,OAAOmM,KAAKC,SAAWjM,EAAE0D,gBAC3B1D,EAAEC,UAAU6U,KAAK,uBAAuBmH,WAAW0H,KAI/DnY,WACI0Y,aAAc,WAMV,IAAK,GALD/Z,GAAYnK,EAAEsV,UACdgT,EAAStoB,EAAE0D,gBAAkByG,EAAYnK,EAAEqF,MAAQ,GAAK8E,EAAYnK,EAAEsF,OAAS,EAC/EmG,EAASzL,EAAE0D,eAAiB1D,EAAEH,OAAO2L,UAAUC,QAASzL,EAAEH,OAAO2L,UAAUC,OAC3E6J,EAAYtV,EAAEH,OAAO2L,UAAUG,MAE1BpG,EAAI,EAAGrD,EAASlC,EAAES,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpB6T,EAAYpZ,EAAEwY,gBAAgBjT,GAC9BgjB,EAAchP,EAAM,GAAGc,kBACvBmO,GAAoBF,EAASC,EAAcnP,EAAY,GAAKA,EAAYpZ,EAAEH,OAAO2L,UAAUI,SAE3Fob,EAAUhnB,EAAE0D,eAAiB+H,EAAS+c,EAAmB,EACzDvB,EAAUjnB,EAAE0D,eAAiB,EAAI+H,EAAS+c,EAE1CC,GAAcnT,EAAYlV,KAAKuG,IAAI6hB,GAEnCE,EAAa1oB,EAAE0D,eAAiB,EAAI1D,EAAEH,OAAO2L,UAAUE,QAAU,EACjEid,EAAa3oB,EAAE0D,eAAiB1D,EAAEH,OAAO2L,UAAUE,QAAU,EAAqB,CAGlFtL,MAAKuG,IAAIgiB,GAAc,OAAOA,EAAa,GAC3CvoB,KAAKuG,IAAI+hB,GAAc,OAAOA,EAAa,GAC3CtoB,KAAKuG,IAAI8hB,GAAc,OAAOA,EAAa,GAC3CroB,KAAKuG,IAAIqgB,GAAW,OAAOA,EAAU,GACrC5mB,KAAKuG,IAAIsgB,GAAW,OAAOA,EAAU,EAEzC,IAAI2B,GAAiB,eAAiBD,EAAa,MAAQD,EAAa,MAAQD,EAAa,gBAAkBxB,EAAU,gBAAkBD,EAAU,MAIrJ,IAFAzN,EAAMpP,UAAUye,GAChBrP,EAAM,GAAGtD,MAAMiR,QAAU9mB,KAAKuG,IAAIvG,KAAKF,MAAMsoB,IAAqB,EAC9DxoB,EAAEH,OAAO2L,UAAUK,aAAc,CAEjC,GAAIsb,GAAennB,EAAE0D,eAAiB6V,EAAMzE,KAAK,6BAA+ByE,EAAMzE,KAAK,4BACvFsS,EAAcpnB,EAAE0D,eAAiB6V,EAAMzE,KAAK,8BAAgCyE,EAAMzE,KAAK,8BAC/D,KAAxBqS,EAAajlB,SACbilB,EAAe3nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,OAAS,OAAS,YAC5F6V,EAAMsM,OAAOsB,IAEU,IAAvBC,EAAYllB,SACZklB,EAAc5nB,EAAE,oCAAsCQ,EAAE0D,eAAiB,QAAU,UAAY,YAC/F6V,EAAMsM,OAAOuB,IAEbD,EAAajlB,SAAQilB,EAAa,GAAGlR,MAAM4Q,QAAU2B,EAAmB,EAAIA,EAAmB,GAC/FpB,EAAYllB,SAAQklB,EAAY,GAAGnR,MAAM4Q,SAAY2B,EAAoB,GAAKA,EAAmB,IAK7G,GAAIxoB,EAAEke,QAAQC,GAAI,CACd,GAAI0K,GAAK7oB,EAAE2U,QAAQ,GAAGsB,KACtB4S,GAAGC,kBAAoBR,EAAS,WAGxCxE,cAAe,SAAUH,GACrB3jB,EAAES,OAAOwb,WAAW0H,GAAU7O,KAAK,gHAAgHmH,WAAW0H,MAQ1K3jB,EAAEmI,MACE4gB,oBAAoB,EACpBC,iBAAkB,SAAUhnB,EAAOinB,GAC/B,GAAqB,mBAAVjnB,KACoB,mBAApBinB,KAAiCA,GAAkB,GACtC,IAApBjpB,EAAES,OAAOyB,QAAb,CAEA,GAAIqX,GAAQvZ,EAAES,OAAOC,GAAGsB,GACpBknB,EAAM3P,EAAMzE,KAAK,IAAM9U,EAAEH,OAAOqS,iBAAmB,SAAWlS,EAAEH,OAAOuS,sBAAwB,UAAYpS,EAAEH,OAAOsS,uBAAyB,MAC7IoH,EAAM8B,SAASrb,EAAEH,OAAOqS,mBAAsBqH,EAAM8B,SAASrb,EAAEH,OAAOuS,wBAA2BmH,EAAM8B,SAASrb,EAAEH,OAAOsS,0BACzH+W,EAAMA,EAAIC,IAAI5P,EAAM,KAEL,IAAf2P,EAAIhnB,QAERgnB,EAAInpB,KAAK,WACL,GAAIqpB,GAAO5pB,EAAED,KACb6pB,GAAKrU,SAAS/U,EAAEH,OAAOsS,uBACvB,IAAIkX,GAAaD,EAAKxoB,KAAK,mBACvB2V,EAAM6S,EAAKxoB,KAAK,YAChB4V,EAAS4S,EAAKxoB,KAAK,eACnB6V,EAAQ2S,EAAKxoB,KAAK,aACtBZ,GAAEqW,UAAU+S,EAAK,GAAK7S,GAAO8S,EAAa7S,EAAQC,GAAO,EAAO,WAuB5D,GAtBI4S,GACAD,EAAKnU,IAAI,mBAAoB,QAAUoU,EAAa,MACpDD,EAAKpD,WAAW,qBAGZxP,IACA4S,EAAKxoB,KAAK,SAAU4V,GACpB4S,EAAKpD,WAAW,gBAEhBvP,IACA2S,EAAKxoB,KAAK,QAAS6V,GACnB2S,EAAKpD,WAAW,eAEhBzP,IACA6S,EAAKxoB,KAAK,MAAO2V,GACjB6S,EAAKpD,WAAW,cAKxBoD,EAAKrU,SAAS/U,EAAEH,OAAOuS,uBAAuBsI,YAAY1a,EAAEH,OAAOsS,wBACnEoH,EAAMzE,KAAK,IAAM9U,EAAEH,OAAOwS,mBAAqB,MAAQrS,EAAEH,OAAO0S,gBAAgBmT,SAC5E1lB,EAAEH,OAAOkB,MAAQkoB,EAAiB,CAClC,GAAIK,GAAqB/P,EAAM3Y,KAAK,0BACpC,IAAI2Y,EAAM8B,SAASrb,EAAEH,OAAOsR,qBAAsB,CAC9C,GAAIoY,GAAgBvpB,EAAE2U,QAAQC,SAAS,6BAA+B0U,EAAqB,WAAatpB,EAAEH,OAAOsR,oBAAsB,IACvInR,GAAEmI,KAAK6gB,iBAAiBO,EAAcvnB,SAAS,OAE9C,CACD,GAAIwnB,GAAkBxpB,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOsR,oBAAsB,6BAA+BmY,EAAqB,KAClItpB,GAAEmI,KAAK6gB,iBAAiBQ,EAAgBxnB,SAAS,IAGzDhC,EAAEkB,KAAK,mBAAoBlB,EAAGuZ,EAAM,GAAI6P,EAAK,MAGjDppB,EAAEkB,KAAK,kBAAmBlB,EAAGuZ,EAAM,GAAI6P,EAAK,QAIpDhhB,KAAM,WACF,GAAI7C,GACAkI,EAAgBzN,EAAEH,OAAO4N,aAK7B,IAJsB,SAAlBA,IACAA,EAAgB,GAEfzN,EAAEmI,KAAK4gB,qBAAoB/oB,EAAEmI,KAAK4gB,oBAAqB,GACxD/oB,EAAEH,OAAO+P,sBACT5P,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOqR,mBAAmBnR,KAAK,WACtDC,EAAEmI,KAAK6gB,iBAAiBxpB,EAAED,MAAMyC,eAIpC,IAAIyL,EAAgB,EAChB,IAAKlI,EAAIvF,EAAEW,YAAa4E,EAAIvF,EAAEW,YAAc8M,EAAgBlI,IACpDvF,EAAES,OAAO8E,IAAIvF,EAAEmI,KAAK6gB,iBAAiBzjB,OAI7CvF,GAAEmI,KAAK6gB,iBAAiBhpB,EAAEW,YAGlC,IAAIX,EAAEH,OAAOoQ,sBACT,GAAIxC,EAAgB,GAAMzN,EAAEH,OAAOqQ,6BAA+BlQ,EAAEH,OAAOqQ,4BAA8B,EAAI,CACzG,GAAIuZ,GAASzpB,EAAEH,OAAOqQ,4BAClBwZ,EAAMjc,EACNkc,EAAWvpB,KAAKwc,IAAI5c,EAAEW,YAAc+oB,EAAMtpB,KAAK+Y,IAAIsQ,EAAQC,GAAM1pB,EAAES,OAAOyB,QAC1E0nB,EAAWxpB,KAAK+Y,IAAInZ,EAAEW,YAAcP,KAAK+Y,IAAIuQ,EAAKD,GAAS,EAE/D,KAAKlkB,EAAIvF,EAAEW,YAAc8M,EAAelI,EAAIokB,EAAUpkB,IAC9CvF,EAAES,OAAO8E,IAAIvF,EAAEmI,KAAK6gB,iBAAiBzjB,EAG7C,KAAKA,EAAIqkB,EAAUrkB,EAAIvF,EAAEW,YAAc4E,IAC/BvF,EAAES,OAAO8E,IAAIvF,EAAEmI,KAAK6gB,iBAAiBzjB,OAG5C,CACD,GAAI+V,GAAYtb,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOuR,eAC9CkK,GAAUpZ,OAAS,GAAGlC,EAAEmI,KAAK6gB,iBAAiB1N,EAAUtZ,QAE5D,IAAIwZ,GAAYxb,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOyR,eAC9CkK,GAAUtZ,OAAS,GAAGlC,EAAEmI,KAAK6gB,iBAAiBxN,EAAUxZ,WAIxE6gB,kBAAmB,WACX7iB,EAAEH,OAAOqI,cACLlI,EAAEH,OAAOsQ,+BAAkCnQ,EAAEH,OAAOsQ,+BAAiCnQ,EAAEmI,KAAK4gB,qBAC5F/oB,EAAEmI,KAAKC,QAInB0a,gBAAiB,WACT9iB,EAAEH,OAAOqI,cAAgBlI,EAAEH,OAAOsQ,8BAClCnQ,EAAEmI,KAAKC,SASnBpI,EAAE2M,WACE8S,WAAW,EACXoK,gBAAiB,SAAUroB,GACvB,GAAIsoB,GAAK9pB,EAAE2M,UAGPod,EAAkB/pB,EAAE0D,eACP,eAAXlC,EAAEuf,MAAoC,cAAXvf,EAAEuf,KAAwBvf,EAAEyf,cAAc,GAAGC,MAAQ1f,EAAE0f,OAAS1f,EAAEwoB,QAClF,eAAXxoB,EAAEuf,MAAoC,cAAXvf,EAAEuf,KAAwBvf,EAAEyf,cAAc,GAAGE,MAAQ3f,EAAE2f,OAAS3f,EAAEyoB,QAC/FjjB,EAAW,EAAoB8iB,EAAGI,MAAMjlB,SAASjF,EAAE0D,eAAiB,OAAS,OAASomB,EAAGK,SAAW,EACpGC,GAAepqB,EAAEsH,eAAiBwiB,EAAGO,YACrCC,GAAetqB,EAAEuH,eAAiBuiB,EAAGO,WACrCrjB,GAAWojB,EACXpjB,EAAWojB,EAENpjB,EAAWsjB,IAChBtjB,EAAWsjB,GAEftjB,GAAYA,EAAW8iB,EAAGO,YAC1BrqB,EAAE0H,eAAeV,GACjBhH,EAAEyH,oBAAoBT,GAAU,IAEpCujB,UAAW,SAAU/oB,GACjB,GAAIsoB,GAAK9pB,EAAE2M,SACXmd,GAAGrK,WAAY,EACfje,EAAEiE,iBACFjE,EAAE0d,kBAEF4K,EAAGD,gBAAgBroB,GACnBsG,aAAagiB,EAAGU,aAEhBV,EAAGI,MAAMjO,WAAW,GAChBjc,EAAEH,OAAO+M,eACTkd,EAAGI,MAAMjV,IAAI,UAAW,GAE5BjV,EAAE2U,QAAQsH,WAAW,KACrB6N,EAAGW,KAAKxO,WAAW,KACnBjc,EAAEkB,KAAK,uBAAwBlB,IAEnC0qB,SAAU,SAAUlpB,GAChB,GAAIsoB,GAAK9pB,EAAE2M,SACNmd,GAAGrK,YACJje,EAAEiE,eAAgBjE,EAAEiE,iBACnBjE,EAAEkE,aAAc,EACrBokB,EAAGD,gBAAgBroB,GACnBxB,EAAE2U,QAAQsH,WAAW,GACrB6N,EAAGI,MAAMjO,WAAW,GACpB6N,EAAGW,KAAKxO,WAAW,GACnBjc,EAAEkB,KAAK,sBAAuBlB,KAElC2qB,QAAS,SAAUnpB,GACf,GAAIsoB,GAAK9pB,EAAE2M,SACNmd,GAAGrK,YACRqK,EAAGrK,WAAY,EACXzf,EAAEH,OAAO+M,gBACT9E,aAAagiB,EAAGU,aAChBV,EAAGU,YAAc1pB,WAAW,WACxBgpB,EAAGI,MAAMjV,IAAI,UAAW,GACxB6U,EAAGI,MAAMjO,WAAW,MACrB,MAGPjc,EAAEkB,KAAK,qBAAsBlB,GACzBA,EAAEH,OAAOiN,wBACT9M,EAAEiI,eAGV2iB,gBAAiB,WACb,MAAK5qB,GAAEH,OAAOsO,iBAAkB,GAAUnO,EAAEuU,QAAQG,MACxC1U,EAAE2d,YADqD3d,EAAEod,sBAGzEyN,gBAAiB,WACb,GAAIf,GAAK9pB,EAAE2M,UACPhL,EAAS3B,EAAEuU,QAAQG,MAAQoV,EAAGI,MAAQlmB,QAC1CxE,GAAEsqB,EAAGI,OAAOY,GAAGhB,EAAGc,gBAAgBvN,MAAOyM,EAAGS,WAC5C/qB,EAAEmC,GAAQmpB,GAAGhB,EAAGc,gBAAgBtN,KAAMwM,EAAGY,UACzClrB,EAAEmC,GAAQmpB,GAAGhB,EAAGc,gBAAgBrN,IAAKuM,EAAGa,UAE5CI,iBAAkB,WACd,GAAIjB,GAAK9pB,EAAE2M,UACPhL,EAAS3B,EAAEuU,QAAQG,MAAQoV,EAAGI,MAAQlmB,QAC1CxE,GAAEsqB,EAAGI,OAAOc,IAAIhrB,EAAE4qB,gBAAgBvN,MAAOyM,EAAGS,WAC5C/qB,EAAEmC,GAAQqpB,IAAIhrB,EAAE4qB,gBAAgBtN,KAAMwM,EAAGY,UACzClrB,EAAEmC,GAAQqpB,IAAIhrB,EAAE4qB,gBAAgBrN,IAAKuM,EAAGa,UAE5C9N,IAAK,WACD,GAAK7c,EAAEH,OAAO8M,UAAd,CACA,GAAImd,GAAK9pB,EAAE2M,SACXmd,GAAGI,MAAQ1qB,EAAEQ,EAAEH,OAAO8M,WAClB3M,EAAEH,OAAOgP,mBAAmD,gBAAvB7O,GAAEH,OAAO8M,WAA0Bmd,EAAGI,MAAMhoB,OAAS,GAAqD,IAAhDlC,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO8M,WAAWzK,SACpI4nB,EAAGI,MAAQlqB,EAAEC,UAAU6U,KAAK9U,EAAEH,OAAO8M,YAEzCmd,EAAGW,KAAOX,EAAGI,MAAMpV,KAAK,0BACD,IAAnBgV,EAAGW,KAAKvoB,SACR4nB,EAAGW,KAAOjrB,EAAE,6CACZsqB,EAAGI,MAAMrE,OAAOiE,EAAGW,OAEvBX,EAAGW,KAAK,GAAGxU,MAAM5Q,MAAQ,GACzBykB,EAAGW,KAAK,GAAGxU,MAAM3Q,OAAS,GAC1BwkB,EAAGmB,UAAYjrB,EAAE0D,eAAiBomB,EAAGI,MAAM,GAAGgB,YAAcpB,EAAGI,MAAM,GAAGjS,aAExE6R,EAAGqB,QAAUnrB,EAAEqY,KAAOrY,EAAE6Y,YACxBiR,EAAGO,YAAcP,EAAGqB,SAAWrB,EAAGmB,UAAYjrB,EAAEqY,MAChDyR,EAAGK,SAAWL,EAAGmB,UAAYnB,EAAGqB,QAE5BnrB,EAAE0D,eACFomB,EAAGW,KAAK,GAAGxU,MAAM5Q,MAAQykB,EAAGK,SAAW,KAGvCL,EAAGW,KAAK,GAAGxU,MAAM3Q,OAASwkB,EAAGK,SAAW,KAGxCL,EAAGqB,SAAW,EACdrB,EAAGI,MAAM,GAAGjU,MAAMmV,QAAU,OAG5BtB,EAAGI,MAAM,GAAGjU,MAAMmV,QAAU,GAE5BprB,EAAEH,OAAO+M,gBACTkd,EAAGI,MAAM,GAAGjU,MAAM4Q,QAAU,KAGpC3C,aAAc,WACV,GAAKlkB,EAAEH,OAAO8M,UAAd,CACA,GAGI0e,GAFAvB,EAAK9pB,EAAE2M,UAIP2e,GAHYtrB,EAAEsV,WAAa,EAGjBwU,EAAGK,SACjBkB,IAAUvB,EAAGmB,UAAYnB,EAAGK,UAAYnqB,EAAE+J,SACtC/J,EAAEkF,KAAOlF,EAAE0D,gBACX2nB,GAAUA,EACNA,EAAS,GACTC,EAAUxB,EAAGK,SAAWkB,EACxBA,EAAS,IAEHA,EAASvB,EAAGK,SAAWL,EAAGmB,YAChCK,EAAUxB,EAAGmB,UAAYI,IAIzBA,EAAS,GACTC,EAAUxB,EAAGK,SAAWkB,EACxBA,EAAS,GAEJA,EAASvB,EAAGK,SAAWL,EAAGmB,YAC/BK,EAAUxB,EAAGmB,UAAYI,GAG7BrrB,EAAE0D,gBACE1D,EAAEuU,QAAQE,aACVqV,EAAGW,KAAKtgB,UAAU,eAAiB,EAAW,aAG9C2f,EAAGW,KAAKtgB,UAAU,cAAgB,EAAW,OAEjD2f,EAAGW,KAAK,GAAGxU,MAAM5Q,MAAQimB,EAAU,OAG/BtrB,EAAEuU,QAAQE,aACVqV,EAAGW,KAAKtgB,UAAU,oBAAsB,EAAW,UAGnD2f,EAAGW,KAAKtgB,UAAU,cAAgB,EAAW,OAEjD2f,EAAGW,KAAK,GAAGxU,MAAM3Q,OAASgmB,EAAU,MAEpCtrB,EAAEH,OAAO+M,gBACT9E,aAAagiB,EAAG9hB,SAChB8hB,EAAGI,MAAM,GAAGjU,MAAM4Q,QAAU,EAC5BiD,EAAG9hB,QAAUlH,WAAW,WACpBgpB,EAAGI,MAAM,GAAGjU,MAAM4Q,QAAU,EAC5BiD,EAAGI,MAAMjO,WAAW,MACrB,QAGX6H,cAAe,SAAUH,GAChB3jB,EAAEH,OAAO8M,WACd3M,EAAE2M,UAAU8d,KAAKxO,WAAW0H,KAOpC3jB,EAAE+c,YACEwO,aAAc,SAAUxH,EAAGC,GACvBzkB,KAAKwkB,EAAIA,EACTxkB,KAAKykB,EAAIA,EACTzkB,KAAKisB,UAAYzH,EAAE7hB,OAAS,CAI5B,IAAIupB,GAAIC,CACAnsB,MAAKwkB,EAAE7hB,MAEf3C,MAAKosB,YAAc,SAAUC,GACzB,MAAKA,IAGLF,EAAKG,EAAatsB,KAAKwkB,EAAG6H,GAC1BH,EAAKC,EAAK,GAIDE,EAAKrsB,KAAKwkB,EAAE0H,KAAQlsB,KAAKykB,EAAE0H,GAAMnsB,KAAKykB,EAAEyH,KAASlsB,KAAKwkB,EAAE2H,GAAMnsB,KAAKwkB,EAAE0H,IAAOlsB,KAAKykB,EAAEyH,IAR5E,EAWpB,IAAII,GAAe,WACf,GAAIlC,GAAUC,EAAUkC,CACxB,OAAO,UAASC,EAAOC,GAGnB,IAFApC,GAAW,EACXD,EAAWoC,EAAM7pB,OACVynB,EAAWC,EAAW,GACrBmC,EAAMD,EAAQnC,EAAWC,GAAY,IAAMoC,EAC3CpC,EAAWkC,EAEXnC,EAAWmC,CAEnB,OAAOnC,QAKnBsC,uBAAwB,SAASC,GACzBlsB,EAAE+c,WAAWC,SAAQhd,EAAE+c,WAAWC,OAAShd,EAAEH,OAAOkB,KACpD,GAAIf,GAAE+c,WAAWwO,aAAavrB,EAAEuY,WAAY2T,EAAE3T,YAC9C,GAAIvY,GAAE+c,WAAWwO,aAAavrB,EAAE4X,SAAUsU,EAAEtU,YAEpDsM,aAAc,SAAU5O,EAAWsO,GAGhC,QAASuI,GAAuBD,GAK3B5W,EAAY4W,EAAEhnB,KAA8B,eAAvBgnB,EAAErsB,OAAO2K,WAA8BxK,EAAEsV,UAAYtV,EAAEsV,UACjD,UAAvBtV,EAAEH,OAAO6Q,YACT1Q,EAAE+c,WAAWkP,uBAAuBC,GAGpCE,GAAuBpsB,EAAE+c,WAAWC,OAAO2O,aAAarW,IAGxD8W,GAA8C,cAAvBpsB,EAAEH,OAAO6Q,YAChCoX,GAAcoE,EAAE3kB,eAAiB2kB,EAAE5kB,iBAAmBtH,EAAEuH,eAAiBvH,EAAEsH,gBAC3E8kB,GAAuB9W,EAAYtV,EAAEsH,gBAAkBwgB,EAAaoE,EAAE5kB,gBAGtEtH,EAAEH,OAAO4Q,iBACT2b,EAAsBF,EAAE3kB,eAAiB6kB,GAE7CF,EAAExkB,eAAe0kB,GACjBF,EAAEzkB,oBAAoB2kB,GAAqB,EAAOpsB,GAClDksB,EAAEvkB,oBAzBP,GACImgB,GAAYsE,EADZC,EAAarsB,EAAEH,OAAO2Q,OA2B1B,IAAIxQ,EAAEssB,QAAQD,GACV,IAAK,GAAI9mB,GAAI,EAAGA,EAAI8mB,EAAWnqB,OAAQqD,IAC/B8mB,EAAW9mB,KAAOqe,GAAgByI,EAAW9mB,YAAclG,IAC3D8sB,EAAuBE,EAAW9mB,QAIrC8mB,aAAsBhtB,IAAUukB,IAAiByI,GAEtDF,EAAuBE,IAG9BvI,cAAe,SAAUH,EAAUC,GAG/B,QAAS2I,GAAwBL,GAC7BA,EAAE1kB,qBAAqBmc,EAAU3jB,GAChB,IAAb2jB,IACAuI,EAAErJ,oBACFqJ,EAAEvX,QAAQgD,cAAc,WACf0U,IACDH,EAAErsB,OAAOkB,MAA+B,UAAvBf,EAAEH,OAAO6Q,WAC1Bwb,EAAElrB,UAENkrB,EAAEpJ,sBAXd,GACIvd,GADA8mB,EAAarsB,EAAEH,OAAO2Q,OAgB1B,IAAIxQ,EAAEssB,QAAQD,GACV,IAAK9mB,EAAI,EAAGA,EAAI8mB,EAAWnqB,OAAQqD,IAC3B8mB,EAAW9mB,KAAOqe,GAAgByI,EAAW9mB,YAAclG,IAC3DktB,EAAwBF,EAAW9mB,QAItC8mB,aAAsBhtB,IAAUukB,IAAiByI,GACtDE,EAAwBF;AAQpCrsB,EAAEkN,SACEsf,YAAa,SAAUhrB,EAAGrB,GACtB,GAAIssB,GAAUzoB,SAAS0oB,SAASC,KAAK/T,QAAQ,IAAK,IAC9CgU,EAAkB5sB,EAAES,OAAOC,GAAGV,EAAEW,aAAaC,KAAK,YAClD6rB,KAAYG,GACZ5sB,EAAEid,QAAQjd,EAAE2U,QAAQC,SAAS,IAAM5U,EAAEH,OAAOwE,WAAa,eAAiB,EAAY,MAAMrC,UAGpG+c,aAAc,SAAUlB,GACpB,GAAIE,GAASF,EAAS,MAAQ,IAC9Bre,GAAE8C,QAAQyb,GAAQ,aAAc/d,EAAEkN,QAAQsf,cAE9ClJ,QAAS,WACL,GAAKtjB,EAAEkN,QAAQ2f,aAAgB7sB,EAAEH,OAAOqN,QACxC,GAAIlN,EAAEH,OAAOwN,cAAgB/K,OAAO8K,SAAW9K,OAAO8K,QAAQC,aAC1D/K,OAAO8K,QAAQC,aAAa,KAAM,KAAO,IAAMrN,EAAES,OAAOC,GAAGV,EAAEW,aAAaC,KAAK,cAAgB,QAC5F,CACH,GAAI2Y,GAAQvZ,EAAES,OAAOC,GAAGV,EAAEW,aACtBgsB,EAAOpT,EAAM3Y,KAAK,cAAgB2Y,EAAM3Y,KAAK,eACjDoD,UAAS0oB,SAASC,KAAOA,GAAQ,KAGzCG,KAAM,WACF,GAAK9sB,EAAEH,OAAOqN,UAAWlN,EAAEH,OAAOuN,QAAlC,CACApN,EAAEkN,QAAQ2f,aAAc,CACxB,IAAIF,GAAO3oB,SAAS0oB,SAASC,KAAK/T,QAAQ,IAAK,GAC/C,IAAK+T,EAAL,CAEA,IAAK,GADDhiB,GAAQ,EACHpF,EAAI,EAAGrD,EAASlC,EAAES,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBwnB,EAAYxT,EAAM3Y,KAAK,cAAgB2Y,EAAM3Y,KAAK,eACtD,IAAImsB,IAAcJ,IAASpT,EAAM8B,SAASrb,EAAEH,OAAOsR,qBAAsB,CACrE,GAAInP,GAAQuX,EAAMvX,OAClBhC,GAAEid,QAAQjb,EAAO2I,EAAO3K,EAAEH,OAAOmT,oBAAoB,IAGzDhT,EAAEH,OAAOsN,mBAAmBnN,EAAEkN,QAAQ6R,kBAE9CiO,QAAS,WACDhtB,EAAEH,OAAOsN,mBAAmBnN,EAAEkN,QAAQ6R,cAAa,KAO/D/e,EAAEoN,SACE0f,KAAM,WACF,GAAK9sB,EAAEH,OAAOuN,QAAd,CACA,IAAK9K,OAAO8K,UAAY9K,OAAO8K,QAAQ6f,UAGnC,MAFAjtB,GAAEH,OAAOuN,SAAU,OACnBpN,EAAEH,OAAOqN,SAAU,EAGvBlN,GAAEoN,QAAQyf,aAAc,EACxBttB,KAAK2tB,MAAQ3tB,KAAK4tB,iBACb5tB,KAAK2tB,MAAME,KAAQ7tB,KAAK2tB,MAAMG,SACnC9tB,KAAK+tB,cAAc,EAAG/tB,KAAK2tB,MAAMG,MAAOrtB,EAAEH,OAAOmT,oBAC5ChT,EAAEH,OAAOwN,cACV/K,OAAOirB,iBAAiB,WAAYhuB,KAAKiuB,uBAGjDA,mBAAoB,WAChBxtB,EAAEoN,QAAQ8f,MAAQltB,EAAEoN,QAAQ+f,gBAC5BntB,EAAEoN,QAAQkgB,cAActtB,EAAEH,OAAO8K,MAAO3K,EAAEoN,QAAQ8f,MAAMG,OAAO,IAEnEF,cAAe,WACX,GAAIM,GAAYnrB,OAAOoqB,SAASgB,SAASC,MAAM,GAAGhJ,MAAM,KACpDhJ,EAAQ8R,EAAUvrB,OAClBkrB,EAAMK,EAAU9R,EAAQ,GACxB0R,EAAQI,EAAU9R,EAAQ,EAC9B,QAASyR,IAAKA,EAAKC,MAAOA,IAE9BhK,WAAY,SAAU+J,EAAKprB,GACvB,GAAKhC,EAAEoN,QAAQyf,aAAgB7sB,EAAEH,OAAOuN,QAAxC,CACA,GAAImM,GAAQvZ,EAAES,OAAOC,GAAGsB,GACpBqrB,EAAQ9tB,KAAKquB,QAAQrU,EAAM3Y,KAAK,gBAC/B0B,QAAOoqB,SAASgB,SAASG,SAAST,KACnCC,EAAQD,EAAM,IAAMC,GAEpBrtB,EAAEH,OAAOwN,aACT/K,OAAO8K,QAAQC,aAAa,KAAM,KAAMggB,GAExC/qB,OAAO8K,QAAQ6f,UAAU,KAAM,KAAMI,KAG7CO,QAAS,SAAS/R,GACd,MAAOA,GAAKqJ,WAAW/gB,cAClByU,QAAQ,OAAQ,KAChBA,QAAQ,YAAa,IACrBA,QAAQ,SAAU,KAClBA,QAAQ,MAAO,IACfA,QAAQ,MAAO,KAExB0U,cAAe,SAAS3iB,EAAO0iB,EAAOlK,GAClC,GAAIkK,EACA,IAAK,GAAI9nB,GAAI,EAAGrD,EAASlC,EAAES,OAAOyB,OAAQqD,EAAIrD,EAAQqD,IAAK,CACvD,GAAIgU,GAAQvZ,EAAES,OAAOC,GAAG6E,GACpBuoB,EAAevuB,KAAKquB,QAAQrU,EAAM3Y,KAAK,gBAC3C,IAAIktB,IAAiBT,IAAU9T,EAAM8B,SAASrb,EAAEH,OAAOsR,qBAAsB,CACzE,GAAInP,GAAQuX,EAAMvX,OAClBhC,GAAEid,QAAQjb,EAAO2I,EAAOwY,QAIhCnjB,GAAEid,QAAQ,EAAGtS,EAAOwY,KAyEhCnjB,EAAE+tB,uBAAyB,WACvB/tB,EAAEH,OAAOkN,iBAAkB,EAC3BvN,EAAEwE,UAAUgnB,IAAI,UAAW5nB,IAE/BpD,EAAEguB,sBAAwB,WACtBhuB,EAAEH,OAAOkN,iBAAkB,EAC3BvN,EAAEwE,UAAU8mB,GAAG,UAAW1nB,IAO9BpD,EAAE+H,YACEY,OAAO,EACPH,gBAAgB,GAAKlG,QAAOgG,MAAQC,WAEpCvI,EAAEH,OAAOmN,oBAMThN,EAAE+H,WAAWY,MAAS6U,UAAUyQ,UAAUhkB,QAAQ,YAAa,EAC3D,iBACApE,IACI,QAAU,cAkHtB7F,EAAEkuB,yBAA2B,WACzB,IAAKluB,EAAE+H,WAAWY,MAAO,OAAO,CAChC,IAAIhH,GAAS3B,EAAEC,SAKf,OAJwC,cAApCD,EAAEH,OAAOoN,yBACTtL,EAASnC,EAAEQ,EAAEH,OAAOoN,yBAExBtL,EAAOqpB,IAAIhrB,EAAE+H,WAAWY,MAAOtC,IACxB,GAGXrG,EAAEmuB,wBAA0B,WACxB,IAAKnuB,EAAE+H,WAAWY,MAAO,OAAO,CAChC,IAAIhH,GAAS3B,EAAEC,SAKf,OAJwC,cAApCD,EAAEH,OAAOoN,yBACTtL,EAASnC,EAAEQ,EAAEH,OAAOoN,yBAExBtL,EAAOmpB,GAAG9qB,EAAE+H,WAAWY,MAAOtC,IACvB,GAiNXrG,EAAEsM,UACE4X,aAAc,WACVlkB,EAAEC,UAAU2U,SAAS,8EAA8E7U,KAAK,WACpG+J,EAAqBvK,KAAMS,EAAE+J,YAGjC/J,EAAES,OAAOV,KAAK,WACV,GAAIwZ,GAAQ/Z,EAAED,KACdga,GAAMzE,KAAK,8EAA8E/U,KAAK,WAC1F,GAAIgK,GAAW3J,KAAKwc,IAAIxc,KAAK+Y,IAAII,EAAM,GAAGxP,UAAU,GAAK,EACzDD,GAAqBvK,KAAMwK,QAIvC+Z,cAAe,SAAUH,GACG,mBAAbA,KAA0BA,EAAW3jB,EAAEH,OAAO8K,OACzD3K,EAAEC,UAAU6U,KAAK,8EAA8E/U,KAAK,WAChG,GAAI2B,GAAKlC,EAAED,MACP6uB,EAAmBlkB,SAASxI,EAAGd,KAAK,iCAAkC,KAAO+iB,CAChE,KAAbA,IAAgByK,EAAmB,GACvC1sB,EAAGua,WAAWmS,OAS1BpuB,EAAEuM,MAEEuP,MAAO,EACPuS,aAAc,EACdC,WAAW,EACXC,SACIhV,MAAOhM,OACPihB,WAAYjhB,OACZkhB,YAAalhB,OACbsJ,MAAOtJ,OACPmhB,UAAWnhB,OACXf,QAASxM,EAAEH,OAAO2M,SAEtBqK,OACI4I,UAAWlS,OACXmS,QAASnS,OACTmT,SAAUnT,OACVoT,SAAUpT,OACVohB,KAAMphB,OACNqhB,KAAMrhB,OACNshB,KAAMthB,OACNuhB,KAAMvhB,OACNlI,MAAOkI,OACPjI,OAAQiI,OACRiT,OAAQjT,OACRkT,OAAQlT,OACRwhB,gBACAC,mBAEJzZ,UACIwO,EAAGxW,OACHyW,EAAGzW,OACH0hB,cAAe1hB,OACf2hB,cAAe3hB,OACf4hB,SAAU5hB,QAGd6hB,0BAA2B,SAAU5tB,GACjC,GAAIA,EAAEyf,cAAc/e,OAAS,EAAG,MAAO,EACvC,IAAImtB,GAAK7tB,EAAEyf,cAAc,GAAGC,MACxBoO,EAAK9tB,EAAEyf,cAAc,GAAGE,MACxByK,EAAKpqB,EAAEyf,cAAc,GAAGC,MACxBqO,EAAK/tB,EAAEyf,cAAc,GAAGE,MACxBkB,EAAWjiB,KAAKovB,KAAKpvB,KAAKwhB,IAAIgK,EAAKyD,EAAI,GAAKjvB,KAAKwhB,IAAI2N,EAAKD,EAAI,GAClE,OAAOjN,IAGXoN,eAAgB,SAAUjuB,GACtB,GAAIyiB,GAAIjkB,EAAEuM,IACV,KAAKvM,EAAEuU,QAAQmb,SAAU,CACrB,GAAe,eAAXluB,EAAEuf,MAAoC,eAAXvf,EAAEuf,MAAyBvf,EAAEyf,cAAc/e,OAAS,EAC/E,MAEJ+hB,GAAEsK,QAAQoB,WAAa1L,EAAEmL,0BAA0B5tB,GAEvD,MAAKyiB,GAAEsK,QAAQhV,OAAU0K,EAAEsK,QAAQhV,MAAMrX,SACrC+hB,EAAEsK,QAAQhV,MAAQ/Z,EAAED,MACW,IAA3B0kB,EAAEsK,QAAQhV,MAAMrX,SAAc+hB,EAAEsK,QAAQhV,MAAQvZ,EAAES,OAAOC,GAAGV,EAAEW,cAClEsjB,EAAEsK,QAAQ1X,MAAQoN,EAAEsK,QAAQhV,MAAMzE,KAAK,oBACvCmP,EAAEsK,QAAQG,UAAYzK,EAAEsK,QAAQ1X,MAAM+Y,OAAO,IAAM5vB,EAAEH,OAAO2S,oBAC5DyR,EAAEsK,QAAQ/hB,QAAUyX,EAAEsK,QAAQG,UAAU9tB,KAAK,qBAAuBZ,EAAEH,OAAO2M,QAC1C,IAA/ByX,EAAEsK,QAAQG,UAAUxsB,SAK5B+hB,EAAEsK,QAAQ1X,MAAMoF,WAAW,QAC3BgI,EAAEqK,WAAY,SALNrK,EAAEsK,QAAQ1X,MAAQtJ,SAO9BsiB,gBAAiB,SAAUruB,GACvB,GAAIyiB,GAAIjkB,EAAEuM,IACV,KAAKvM,EAAEuU,QAAQmb,SAAU,CACrB,GAAe,cAAXluB,EAAEuf,MAAmC,cAAXvf,EAAEuf,MAAwBvf,EAAEyf,cAAc/e,OAAS,EAC7E,MAEJ+hB,GAAEsK,QAAQuB,UAAY7L,EAAEmL,0BAA0B5tB,GAEjDyiB,EAAEsK,QAAQ1X,OAAoC,IAA3BoN,EAAEsK,QAAQ1X,MAAM3U,SACpClC,EAAEuU,QAAQmb,SACVzL,EAAEnI,MAAQta,EAAEsa,MAAQmI,EAAEoK,aAGtBpK,EAAEnI,MAASmI,EAAEsK,QAAQuB,UAAY7L,EAAEsK,QAAQoB,WAAc1L,EAAEoK,aAE3DpK,EAAEnI,MAAQmI,EAAEsK,QAAQ/hB,UACpByX,EAAEnI,MAAQmI,EAAEsK,QAAQ/hB,QAAU,EAAIpM,KAAKwhB,IAAKqC,EAAEnI,MAAQmI,EAAEsK,QAAQ/hB,QAAU,EAAI,KAE9EyX,EAAEnI,MAAQ9b,EAAEH,OAAO4M,UACnBwX,EAAEnI,MAAS9b,EAAEH,OAAO4M,QAAU,EAAIrM,KAAKwhB,IAAK5hB,EAAEH,OAAO4M,QAAUwX,EAAEnI,MAAQ,EAAI,KAEjFmI,EAAEsK,QAAQ1X,MAAM1M,UAAU,4BAA8B8Z,EAAEnI,MAAQ,OAEtEiU,aAAc,SAAUvuB,GACpB,GAAIyiB,GAAIjkB,EAAEuM,MACLvM,EAAEuU,QAAQmb,WACI,aAAXluB,EAAEuf,MAAkC,aAAXvf,EAAEuf,MAAuBvf,EAAEwuB,eAAe9tB,OAAS,IAI/E+hB,EAAEsK,QAAQ1X,OAAoC,IAA3BoN,EAAEsK,QAAQ1X,MAAM3U,SACxC+hB,EAAEnI,MAAQ1b,KAAK+Y,IAAI/Y,KAAKwc,IAAIqH,EAAEnI,MAAOmI,EAAEsK,QAAQ/hB,SAAUxM,EAAEH,OAAO4M,SAClEwX,EAAEsK,QAAQ1X,MAAMoF,WAAWjc,EAAEH,OAAO8K,OAAOR,UAAU,4BAA8B8Z,EAAEnI,MAAQ,KAC7FmI,EAAEoK,aAAepK,EAAEnI,MACnBmI,EAAEqK,WAAY,EACE,IAAZrK,EAAEnI,QAAamI,EAAEsK,QAAQhV,MAAQhM,UAEzC6Q,aAAc,SAAUpe,EAAGwB,GACvB,GAAIyiB,GAAIjkB,EAAEuM,IACL0X,GAAEsK,QAAQ1X,OAAoC,IAA3BoN,EAAEsK,QAAQ1X,MAAM3U,SACpC+hB,EAAEpN,MAAM4I,YACQ,YAAhBzf,EAAEmV,OAAO8a,IAAkBzuB,EAAEiE,iBACjCwe,EAAEpN,MAAM4I,WAAY,EACpBwE,EAAEpN,MAAMkY,aAAahL,EAAe,eAAXviB,EAAEuf,KAAwBvf,EAAEyf,cAAc,GAAGC,MAAQ1f,EAAE0f,MAChF+C,EAAEpN,MAAMkY,aAAa/K,EAAe,eAAXxiB,EAAEuf,KAAwBvf,EAAEyf,cAAc,GAAGE,MAAQ3f,EAAE2f,SAEpF9C,YAAa,SAAU7c,GACnB,GAAIyiB,GAAIjkB,EAAEuM,IACV,IAAK0X,EAAEsK,QAAQ1X,OAAoC,IAA3BoN,EAAEsK,QAAQ1X,MAAM3U,SACxClC,EAAEif,YAAa,EACVgF,EAAEpN,MAAM4I,WAAcwE,EAAEsK,QAAQhV,OAArC,CAEK0K,EAAEpN,MAAM6I,UACTuE,EAAEpN,MAAMxR,MAAQ4e,EAAEsK,QAAQ1X,MAAM,GAAGqU,YACnCjH,EAAEpN,MAAMvR,OAAS2e,EAAEsK,QAAQ1X,MAAM,GAAGoB,aACpCgM,EAAEpN,MAAM2J,OAASxgB,EAAEmkB,aAAaF,EAAEsK,QAAQG,UAAU,GAAI,MAAQ,EAChEzK,EAAEpN,MAAM4J,OAASzgB,EAAEmkB,aAAaF,EAAEsK,QAAQG,UAAU,GAAI,MAAQ,EAChEzK,EAAEsK,QAAQC,WAAavK,EAAEsK,QAAQhV,MAAM,GAAG2R,YAC1CjH,EAAEsK,QAAQE,YAAcxK,EAAEsK,QAAQhV,MAAM,GAAGtB,aAC3CgM,EAAEsK,QAAQG,UAAUzS,WAAW,GAGnC,IAAIiU,GAAcjM,EAAEpN,MAAMxR,MAAQ4e,EAAEnI,MAChCqU,EAAelM,EAAEpN,MAAMvR,OAAS2e,EAAEnI,KAEtC,MAAIoU,EAAcjM,EAAEsK,QAAQC,YAAc2B,EAAelM,EAAEsK,QAAQE,aAAnE,CAUA,GARAxK,EAAEpN,MAAM8X,KAAOvuB,KAAKwc,IAAKqH,EAAEsK,QAAQC,WAAa,EAAI0B,EAAc,EAAI,GACtEjM,EAAEpN,MAAMgY,MAAQ5K,EAAEpN,MAAM8X,KACxB1K,EAAEpN,MAAM+X,KAAOxuB,KAAKwc,IAAKqH,EAAEsK,QAAQE,YAAc,EAAI0B,EAAe,EAAI,GACxElM,EAAEpN,MAAMiY,MAAQ7K,EAAEpN,MAAM+X,KAExB3K,EAAEpN,MAAMmY,eAAejL,EAAe,cAAXviB,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGC,MAAQ1f,EAAE0f,MACjF+C,EAAEpN,MAAMmY,eAAehL,EAAe,cAAXxiB,EAAEuf,KAAuBvf,EAAEyf,cAAc,GAAGE,MAAQ3f,EAAE2f,OAE5E8C,EAAEpN,MAAM6I,UAAYuE,EAAEqK,UAAW,CAClC,GAAItuB,EAAE0D,gBACDtD,KAAKC,MAAM4jB,EAAEpN,MAAM8X,QAAUvuB,KAAKC,MAAM4jB,EAAEpN,MAAM2J,SAAWyD,EAAEpN,MAAMmY,eAAejL,EAAIE,EAAEpN,MAAMkY,aAAahL,GAC3G3jB,KAAKC,MAAM4jB,EAAEpN,MAAMgY,QAAUzuB,KAAKC,MAAM4jB,EAAEpN,MAAM2J,SAAWyD,EAAEpN,MAAMmY,eAAejL,EAAIE,EAAEpN,MAAMkY,aAAahL,EAG5G,YADAE,EAAEpN,MAAM4I,WAAY,EAGnB,KAAKzf,EAAE0D,gBACPtD,KAAKC,MAAM4jB,EAAEpN,MAAM+X,QAAUxuB,KAAKC,MAAM4jB,EAAEpN,MAAM4J,SAAWwD,EAAEpN,MAAMmY,eAAehL,EAAIC,EAAEpN,MAAMkY,aAAa/K,GAC3G5jB,KAAKC,MAAM4jB,EAAEpN,MAAMiY,QAAU1uB,KAAKC,MAAM4jB,EAAEpN,MAAM4J,SAAWwD,EAAEpN,MAAMmY,eAAehL,EAAIC,EAAEpN,MAAMkY,aAAa/K,EAG5G,YADAC,EAAEpN,MAAM4I,WAAY,GAI5Bje,EAAEiE,iBACFjE,EAAE0d,kBAEF+E,EAAEpN,MAAM6I,SAAU,EAClBuE,EAAEpN,MAAM6J,SAAWuD,EAAEpN,MAAMmY,eAAejL,EAAIE,EAAEpN,MAAMkY,aAAahL,EAAIE,EAAEpN,MAAM2J,OAC/EyD,EAAEpN,MAAM8J,SAAWsD,EAAEpN,MAAMmY,eAAehL,EAAIC,EAAEpN,MAAMkY,aAAa/K,EAAIC,EAAEpN,MAAM4J,OAE3EwD,EAAEpN,MAAM6J,SAAWuD,EAAEpN,MAAM8X,OAC3B1K,EAAEpN,MAAM6J,SAAYuD,EAAEpN,MAAM8X,KAAO,EAAIvuB,KAAKwhB,IAAKqC,EAAEpN,MAAM8X,KAAO1K,EAAEpN,MAAM6J,SAAW,EAAI,KAEvFuD,EAAEpN,MAAM6J,SAAWuD,EAAEpN,MAAMgY,OAC3B5K,EAAEpN,MAAM6J,SAAWuD,EAAEpN,MAAMgY,KAAO,EAAIzuB,KAAKwhB,IAAKqC,EAAEpN,MAAM6J,SAAWuD,EAAEpN,MAAMgY,KAAO,EAAI,KAGtF5K,EAAEpN,MAAM8J,SAAWsD,EAAEpN,MAAM+X,OAC3B3K,EAAEpN,MAAM8J,SAAYsD,EAAEpN,MAAM+X,KAAO,EAAIxuB,KAAKwhB,IAAKqC,EAAEpN,MAAM+X,KAAO3K,EAAEpN,MAAM8J,SAAW,EAAI,KAEvFsD,EAAEpN,MAAM8J,SAAWsD,EAAEpN,MAAMiY,OAC3B7K,EAAEpN,MAAM8J,SAAWsD,EAAEpN,MAAMiY,KAAO,EAAI1uB,KAAKwhB,IAAKqC,EAAEpN,MAAM8J,SAAWsD,EAAEpN,MAAMiY,KAAO,EAAI,KAIrF7K,EAAE1O,SAAS0Z,gBAAehL,EAAE1O,SAAS0Z,cAAgBhL,EAAEpN,MAAMmY,eAAejL,GAC5EE,EAAE1O,SAAS2Z,gBAAejL,EAAE1O,SAAS2Z,cAAgBjL,EAAEpN,MAAMmY,eAAehL,GAC5EC,EAAE1O,SAAS4Z,WAAUlL,EAAE1O,SAAS4Z,SAAW7mB,KAAK+X,OACrD4D,EAAE1O,SAASwO,GAAKE,EAAEpN,MAAMmY,eAAejL,EAAIE,EAAE1O,SAAS0Z,gBAAkB3mB,KAAK+X,MAAQ4D,EAAE1O,SAAS4Z,UAAY,EAC5GlL,EAAE1O,SAASyO,GAAKC,EAAEpN,MAAMmY,eAAehL,EAAIC,EAAE1O,SAAS2Z,gBAAkB5mB,KAAK+X,MAAQ4D,EAAE1O,SAAS4Z,UAAY,EACxG/uB,KAAKuG,IAAIsd,EAAEpN,MAAMmY,eAAejL,EAAIE,EAAE1O,SAAS0Z,eAAiB,IAAGhL,EAAE1O,SAASwO,EAAI,GAClF3jB,KAAKuG,IAAIsd,EAAEpN,MAAMmY,eAAehL,EAAIC,EAAE1O,SAAS2Z,eAAiB,IAAGjL,EAAE1O,SAASyO,EAAI,GACtFC,EAAE1O,SAAS0Z,cAAgBhL,EAAEpN,MAAMmY,eAAejL,EAClDE,EAAE1O,SAAS2Z,cAAgBjL,EAAEpN,MAAMmY,eAAehL,EAClDC,EAAE1O,SAAS4Z,SAAW7mB,KAAK+X,MAE3B4D,EAAEsK,QAAQG,UAAUvkB,UAAU,eAAiB8Z,EAAEpN,MAAM6J,SAAW,OAASuD,EAAEpN,MAAM8J,SAAW,YAElGrC,WAAY,SAAUte,EAAGwB,GACrB,GAAIyiB,GAAIjkB,EAAEuM,IACV,IAAK0X,EAAEsK,QAAQ1X,OAAoC,IAA3BoN,EAAEsK,QAAQ1X,MAAM3U,OAAxC,CACA,IAAK+hB,EAAEpN,MAAM4I,YAAcwE,EAAEpN,MAAM6I,QAG/B,MAFAuE,GAAEpN,MAAM4I,WAAY,OACpBwE,EAAEpN,MAAM6I,SAAU,EAGtBuE,GAAEpN,MAAM4I,WAAY,EACpBwE,EAAEpN,MAAM6I,SAAU,CAClB,IAAI0Q,GAAoB,IACpBC,EAAoB,IACpBC,EAAoBrM,EAAE1O,SAASwO,EAAIqM,EACnCG,EAAetM,EAAEpN,MAAM6J,SAAW4P,EAClCE,EAAoBvM,EAAE1O,SAASyO,EAAIqM,EACnCI,EAAexM,EAAEpN,MAAM8J,SAAW6P,CAGjB,KAAjBvM,EAAE1O,SAASwO,IAASqM,EAAoBhwB,KAAKuG,KAAK4pB,EAAetM,EAAEpN,MAAM6J,UAAYuD,EAAE1O,SAASwO,IAC/E,IAAjBE,EAAE1O,SAASyO,IAASqM,EAAoBjwB,KAAKuG,KAAK8pB,EAAexM,EAAEpN,MAAM8J,UAAYsD,EAAE1O,SAASyO,GACpG,IAAI1B,GAAmBliB,KAAK+Y,IAAIiX,EAAmBC,EAEnDpM,GAAEpN,MAAM6J,SAAW6P,EACnBtM,EAAEpN,MAAM8J,SAAW8P,CAGnB,IAAIP,GAAcjM,EAAEpN,MAAMxR,MAAQ4e,EAAEnI,MAChCqU,EAAelM,EAAEpN,MAAMvR,OAAS2e,EAAEnI,KACtCmI,GAAEpN,MAAM8X,KAAOvuB,KAAKwc,IAAKqH,EAAEsK,QAAQC,WAAa,EAAI0B,EAAc,EAAI,GACtEjM,EAAEpN,MAAMgY,MAAQ5K,EAAEpN,MAAM8X,KACxB1K,EAAEpN,MAAM+X,KAAOxuB,KAAKwc,IAAKqH,EAAEsK,QAAQE,YAAc,EAAI0B,EAAe,EAAI,GACxElM,EAAEpN,MAAMiY,MAAQ7K,EAAEpN,MAAM+X,KACxB3K,EAAEpN,MAAM6J,SAAWtgB,KAAK+Y,IAAI/Y,KAAKwc,IAAIqH,EAAEpN,MAAM6J,SAAUuD,EAAEpN,MAAMgY,MAAO5K,EAAEpN,MAAM8X,MAC9E1K,EAAEpN,MAAM8J,SAAWvgB,KAAK+Y,IAAI/Y,KAAKwc,IAAIqH,EAAEpN,MAAM8J,SAAUsD,EAAEpN,MAAMiY,MAAO7K,EAAEpN,MAAM+X,MAE9E3K,EAAEsK,QAAQG,UAAUzS,WAAWqG,GAAkBnY,UAAU,eAAiB8Z,EAAEpN,MAAM6J,SAAW,OAASuD,EAAEpN,MAAM8J,SAAW,WAE/HmC,gBAAiB,SAAU9iB,GACvB,GAAIikB,GAAIjkB,EAAEuM,IACN0X,GAAEsK,QAAQhV,OAASvZ,EAAEkb,gBAAkBlb,EAAEW,cACzCsjB,EAAEsK,QAAQ1X,MAAM1M,UAAU,+BAC1B8Z,EAAEsK,QAAQG,UAAUvkB,UAAU,sBAC9B8Z,EAAEsK,QAAQhV,MAAQ0K,EAAEsK,QAAQ1X,MAAQoN,EAAEsK,QAAQG,UAAYnhB,OAC1D0W,EAAEnI,MAAQmI,EAAEoK,aAAe,IAInCqC,WAAY,SAAU1wB,EAAGwB,GACrB,GAAIyiB,GAAIjkB,EAAEuM,IAMV,IALK0X,EAAEsK,QAAQhV,QACX0K,EAAEsK,QAAQhV,MAAQvZ,EAAEsf,aAAe9f,EAAEQ,EAAEsf,cAAgBtf,EAAES,OAAOC,GAAGV,EAAEW,aACrEsjB,EAAEsK,QAAQ1X,MAAQoN,EAAEsK,QAAQhV,MAAMzE,KAAK,oBACvCmP,EAAEsK,QAAQG,UAAYzK,EAAEsK,QAAQ1X,MAAM+Y,OAAO,IAAM5vB,EAAEH,OAAO2S,qBAE3DyR,EAAEsK,QAAQ1X,OAAoC,IAA3BoN,EAAEsK,QAAQ1X,MAAM3U,OAAxC,CAEA,GAAIyuB,GAAQC,EAAQC,EAASC,EAASC,EAAOC,EAAOrI,EAAYD,EAAYuI,EAAYC,EAAahB,EAAaC,EAAcgB,EAAeC,EAAeC,EAAeC,EAAe9C,EAAYC,CAElK,oBAA3BxK,GAAEpN,MAAMkY,aAAahL,GAAqBviB,GACjDmvB,EAAoB,aAAXnvB,EAAEuf,KAAsBvf,EAAEwuB,eAAe,GAAG9O,MAAQ1f,EAAE0f,MAC/D0P,EAAoB,aAAXpvB,EAAEuf,KAAsBvf,EAAEwuB,eAAe,GAAG7O,MAAQ3f,EAAE2f,QAG/DwP,EAAS1M,EAAEpN,MAAMkY,aAAahL,EAC9B6M,EAAS3M,EAAEpN,MAAMkY,aAAa/K,GAG9BC,EAAEnI,OAAqB,IAAZmI,EAAEnI,OAEbmI,EAAEnI,MAAQmI,EAAEoK,aAAe,EAC3BpK,EAAEsK,QAAQG,UAAUzS,WAAW,KAAK9R,UAAU,sBAC9C8Z,EAAEsK,QAAQ1X,MAAMoF,WAAW,KAAK9R,UAAU,+BAC1C8Z,EAAEsK,QAAQhV,MAAQhM,SAIlB0W,EAAEnI,MAAQmI,EAAEoK,aAAepK,EAAEsK,QAAQG,UAAU9tB,KAAK,qBAAuBZ,EAAEH,OAAO2M,QAChFhL,GACAgtB,EAAavK,EAAEsK,QAAQhV,MAAM,GAAG2R,YAChCuD,EAAcxK,EAAEsK,QAAQhV,MAAM,GAAGtB,aACjC4Y,EAAU5M,EAAEsK,QAAQhV,MAAMtU,SAAST,KACnCssB,EAAU7M,EAAEsK,QAAQhV,MAAMtU,SAASP,IACnCqsB,EAAQF,EAAUrC,EAAW,EAAImC,EACjCK,EAAQF,EAAUrC,EAAY,EAAImC,EAElCK,EAAahN,EAAEsK,QAAQ1X,MAAM,GAAGqU,YAChCgG,EAAcjN,EAAEsK,QAAQ1X,MAAM,GAAGoB,aACjCiY,EAAce,EAAahN,EAAEnI,MAC7BqU,EAAee,EAAcjN,EAAEnI,MAE/BqV,EAAgB/wB,KAAKwc,IAAK4R,EAAa,EAAI0B,EAAc,EAAI,GAC7DkB,EAAgBhxB,KAAKwc,IAAK6R,EAAc,EAAI0B,EAAe,EAAI,GAC/DkB,GAAiBF,EACjBG,GAAiBF,EAEjBzI,EAAaoI,EAAQ9M,EAAEnI,MACvB4M,EAAasI,EAAQ/M,EAAEnI,MAEnB6M,EAAawI,IACbxI,EAAcwI,GAEdxI,EAAa0I,IACb1I,EAAa0I,GAGb3I,EAAa0I,IACb1I,EAAc0I,GAEd1I,EAAa4I,IACb5I,EAAa4I,KAIjB3I,EAAa,EACbD,EAAa,GAEjBzE,EAAEsK,QAAQG,UAAUzS,WAAW,KAAK9R,UAAU,eAAiBwe,EAAa,OAASD,EAAa,SAClGzE,EAAEsK,QAAQ1X,MAAMoF,WAAW,KAAK9R,UAAU,4BAA8B8Z,EAAEnI,MAAQ,QAI1FiD,aAAc,SAAUlB,GACpB,GAAIE,GAASF,EAAS,MAAQ,IAE9B,IAAI7d,EAAEH,OAAO0M,KAAM,CACf,GACIgS,IADSve,EAAES,SAC+B,eAAxBT,EAAE2d,YAAYN,QAA0Brd,EAAEuU,QAAQgK,kBAAmBve,EAAEH,OAAOkR,oBAAoByN,SAAS,EAAMC,SAAS,GAE5Ize,GAAEuU,QAAQmb,UACV1vB,EAAES,OAAOsd,GAAQ,eAAgB/d,EAAEuM,KAAKkjB,eAAgBlR,GACxDve,EAAES,OAAOsd,GAAQ,gBAAiB/d,EAAEuM,KAAKsjB,gBAAiBtR,GAC1Dve,EAAES,OAAOsd,GAAQ,aAAc/d,EAAEuM,KAAKwjB,aAAcxR,IAEvB,eAAxBve,EAAE2d,YAAYN,QACnBrd,EAAES,OAAOsd,GAAQ/d,EAAE2d,YAAYN,MAAOrd,EAAEuM,KAAKkjB,eAAgBlR,GAC7Dve,EAAES,OAAOsd,GAAQ/d,EAAE2d,YAAYL,KAAMtd,EAAEuM,KAAKsjB,gBAAiBtR,GAC7Dve,EAAES,OAAOsd,GAAQ/d,EAAE2d,YAAYJ,IAAKvd,EAAEuM,KAAKwjB,aAAcxR,IAI7Dve,EAAE+d,GAAQ,aAAc/d,EAAEuM,KAAK6R,cAC/Bpe,EAAES,OAAOV,KAAK,SAAUiC,EAAOuX,GACvB/Z,EAAE+Z,GAAOzE,KAAK,IAAM9U,EAAEH,OAAO2S,oBAAoBtQ,OAAS,GAC1D1C,EAAE+Z,GAAOwE,GAAQ/d,EAAE2d,YAAYL,KAAMtd,EAAEuM,KAAK8R,eAGpDre,EAAE+d,GAAQ,WAAY/d,EAAEuM,KAAK+R,YAG7Bte,EAAE+d,GAAQ,gBAAiB/d,EAAEuM,KAAKuW,iBAC9B9iB,EAAEH,OAAO6M,YACT1M,EAAE8qB,GAAG,YAAa9qB,EAAEuM,KAAKmkB,cAIrC5D,KAAM,WACF9sB,EAAEuM,KAAKwS,gBAEXiO,QAAS,WACLhtB,EAAEuM,KAAKwS,cAAa,KAO5B/e,EAAEuxB,WACF,KAAK,GAAIC,KAAUxxB,GAAEyxB,QAAS,CAC1B,GAAIznB,GAAIhK,EAAEyxB,QAAQD,GAAQxxB,EAAGA,EAAEH,OAAO2xB,GAClCxnB,IAAGhK,EAAEuxB,SAASpuB,KAAK6G,GAkU3B,MA/TAhK,GAAE0xB,YAAc,SAAU5rB,GACtB,IAAK,GAAIP,GAAI,EAAGA,EAAIvF,EAAEuxB,SAASrvB,OAAQqD,IAC/BO,IAAa9F,GAAEuxB,SAAShsB,IACxBvF,EAAEuxB,SAAShsB,GAAGO,GAAW6rB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAmBvG3xB,EAAE4xB,yBAGF5xB,EAAEkB,KAAO,SAAU4E,GAEX9F,EAAEH,OAAOiG,IACT9F,EAAEH,OAAOiG,GAAW6rB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAE1F,IAAIpsB,EAEJ,IAAIvF,EAAE4xB,sBAAsB9rB,GACxB,IAAKP,EAAI,EAAGA,EAAIvF,EAAE4xB,sBAAsB9rB,GAAW5D,OAAQqD,IACvDvF,EAAE4xB,sBAAsB9rB,GAAWP,GAAGosB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAI5G3xB,GAAE0xB,aAAa1xB,EAAE0xB,YAAY5rB,EAAW6rB,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,KAElH3xB,EAAE8qB,GAAK,SAAUhlB,EAAW+rB,GAIxB,MAHA/rB,GAAYsE,EAAmBtE,GAC1B9F,EAAE4xB,sBAAsB9rB,KAAY9F,EAAE4xB,sBAAsB9rB,OACjE9F,EAAE4xB,sBAAsB9rB,GAAW3C,KAAK0uB,GACjC7xB,GAEXA,EAAEgrB,IAAM,SAAUllB,EAAW+rB,GACzB,GAAItsB,EAEJ,IADAO,EAAYsE,EAAmBtE,GACR,mBAAZ+rB,GAGP,MADA7xB,GAAE4xB,sBAAsB9rB,MACjB9F,CAEX,IAAKA,EAAE4xB,sBAAsB9rB,IAA4D,IAA9C9F,EAAE4xB,sBAAsB9rB,GAAW5D,OAA9E,CACA,IAAKqD,EAAI,EAAGA,EAAIvF,EAAE4xB,sBAAsB9rB,GAAW5D,OAAQqD,IACpDvF,EAAE4xB,sBAAsB9rB,GAAWP,KAAOssB,GAAS7xB,EAAE4xB,sBAAsB9rB,GAAWgsB,OAAOvsB,EAAG,EAEvG,OAAOvF,KAEXA,EAAE+xB,KAAO,SAAUjsB,EAAW+rB,GAC1B/rB,EAAYsE,EAAmBtE,EAC/B,IAAIksB,GAAW,WACXH,EAAQF,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,GAAIA,UAAU,IAC1E3xB,EAAEgrB,IAAIllB,EAAWksB,GAGrB,OADAhyB,GAAE8qB,GAAGhlB,EAAWksB,GACThyB,GAIXA,EAAE0S,MACEuf,cAAe,SAAUC,GAErB,MADAA,GAAItxB,KAAK,WAAY,KACdsxB,GAEXC,QAAS,SAAUD,EAAKE,GAEpB,MADAF,GAAItxB,KAAK,OAAQwxB,GACVF,GAGXG,SAAU,SAAUH,EAAKI,GAErB,MADAJ,GAAItxB,KAAK,aAAc0xB,GAChBJ,GAGX/V,QAAS,SAAU+V,GAEf,MADAA,GAAItxB,KAAK,iBAAiB,GACnBsxB,GAGX9V,OAAQ,SAAU8V,GAEd,MADAA,GAAItxB,KAAK,iBAAiB,GACnBsxB,GAGXtT,WAAY,SAAUjW,GACI,KAAlBA,EAAMpF,UACN/D,EAAEmJ,EAAMhH,QAAQC,GAAG5B,EAAEH,OAAO4P,aAC5BzP,EAAE2e,YAAYhW,GACV3I,EAAEmB,MACFnB,EAAE0S,KAAK6f,OAAOvyB,EAAEH,OAAOiT,kBAGvB9S,EAAE0S,KAAK6f,OAAOvyB,EAAEH,OAAO+S,mBAGtBpT,EAAEmJ,EAAMhH,QAAQC,GAAG5B,EAAEH,OAAO6P,cACjC1P,EAAE6e,YAAYlW,GACV3I,EAAEoH,YACFpH,EAAE0S,KAAK6f,OAAOvyB,EAAEH,OAAOgT,mBAGvB7S,EAAE0S,KAAK6f,OAAOvyB,EAAEH,OAAO8S,mBAG3BnT,EAAEmJ,EAAMhH,QAAQC,GAAG,IAAM5B,EAAEH,OAAO4R,cAClCjS,EAAEmJ,EAAMhH,QAAQ,GAAG6wB,UAI3BC,WAAYjzB,EAAE,gBAAkBQ,EAAEH,OAAOyS,kBAAoB,sDAE7DigB,OAAQ,SAAUG,GACd,GAAIC,GAAe3yB,EAAE0S,KAAK+f,UACE,KAAxBE,EAAazwB,SACjBywB,EAAazW,KAAK,IAClByW,EAAazW,KAAKwW,KAEtB5F,KAAM,WAEE9sB,EAAEH,OAAO4P,YAAczP,EAAEyP,YAAczP,EAAEyP,WAAWvN,OAAS,IAC7DlC,EAAE0S,KAAKuf,cAAcjyB,EAAEyP,YACvBzP,EAAE0S,KAAKyf,QAAQnyB,EAAEyP,WAAY,UAC7BzP,EAAE0S,KAAK2f,SAASryB,EAAEyP,WAAYzP,EAAEH,OAAO+S,mBAEvC5S,EAAEH,OAAO6P,YAAc1P,EAAE0P,YAAc1P,EAAE0P,WAAWxN,OAAS,IAC7DlC,EAAE0S,KAAKuf,cAAcjyB,EAAE0P,YACvB1P,EAAE0S,KAAKyf,QAAQnyB,EAAE0P,WAAY,UAC7B1P,EAAE0S,KAAK2f,SAASryB,EAAE0P,WAAY1P,EAAEH,OAAO8S,mBAG3CnT,EAAEQ,EAAEC,WAAW4lB,OAAO7lB,EAAE0S,KAAK+f,aAEjCjW,eAAgB,WACRxc,EAAEH,OAAOiP,YAAc9O,EAAEH,OAAOmP,qBAAuBhP,EAAE4b,SAAW5b,EAAE4b,QAAQ1Z,QAC9ElC,EAAE4b,QAAQ7b,KAAK,WACX,GAAI6yB,GAASpzB,EAAED,KACfS,GAAE0S,KAAKuf,cAAcW,GACrB5yB,EAAE0S,KAAKyf,QAAQS,EAAQ,UACvB5yB,EAAE0S,KAAK2f,SAASO,EAAQ5yB,EAAEH,OAAOkT,wBAAwB6F,QAAQ,YAAaga,EAAO5wB,QAAU,OAI3GgrB,QAAS,WACDhtB,EAAE0S,KAAK+f,YAAczyB,EAAE0S,KAAK+f,WAAWvwB,OAAS,GAAGlC,EAAE0S,KAAK+f,WAAW/M,WAQjF1lB,EAAE8sB,KAAO,WACD9sB,EAAEH,OAAOkB,MAAMf,EAAEylB,aACrBzlB,EAAEkY,sBACFlY,EAAEsY,mBACFtY,EAAEqc,mBACErc,EAAEH,OAAO8M,WAAa3M,EAAE2M,YACxB3M,EAAE2M,UAAUkQ,MACR7c,EAAEH,OAAOgN,oBACT7M,EAAE2M,UAAUke,mBAGI,UAApB7qB,EAAEH,OAAO0L,QAAsBvL,EAAE6jB,QAAQ7jB,EAAEH,OAAO0L,UAC7CvL,EAAEH,OAAOkB,MAAMf,EAAE0H,iBACtB1H,EAAE6jB,QAAQ7jB,EAAEH,OAAO0L,QAAQ2Y,gBAE3BlkB,EAAEH,OAAOkB,KACTf,EAAEid,QAAQjd,EAAEH,OAAO6K,aAAe1K,EAAEuQ,aAAc,EAAGvQ,EAAEH,OAAOmT,qBAG9DhT,EAAEid,QAAQjd,EAAEH,OAAO6K,aAAc,EAAG1K,EAAEH,OAAOmT,oBACf,IAA1BhT,EAAEH,OAAO6K,eACL1K,EAAEsM,UAAYtM,EAAEH,OAAOyM,UAAUtM,EAAEsM,SAAS4X,eAC5ClkB,EAAEmI,MAAQnI,EAAEH,OAAOqI,cACnBlI,EAAEmI,KAAKC,OACPpI,EAAEmI,KAAK4gB,oBAAqB,KAIxC/oB,EAAE+e,eACE/e,EAAEH,OAAO4C,UAAYzC,EAAEuU,QAAQ9R,UAC/BzC,EAAEqlB,gBAEFrlB,EAAEH,OAAOuQ,gBAAkBpQ,EAAEH,OAAOqI,aACpClI,EAAEoQ,gBAEFpQ,EAAEH,OAAO0M,MAAQvM,EAAEuM,MACnBvM,EAAEuM,KAAKugB,OAEP9sB,EAAEH,OAAOS,UACTN,EAAEwX,gBAEFxX,EAAEH,OAAOkN,iBACL/M,EAAEguB,uBAAuBhuB,EAAEguB,wBAE/BhuB,EAAEH,OAAOmN,mBACLhN,EAAEmuB,yBAAyBnuB,EAAEmuB,0BAGjCnuB,EAAEH,OAAOgzB,sBACT7yB,EAAEH,OAAOwN,aAAerN,EAAEH,OAAOgzB,qBAEjC7yB,EAAEH,OAAOuN,SACLpN,EAAEoN,SAASpN,EAAEoN,QAAQ0f,OAEzB9sB,EAAEH,OAAOqN,SACLlN,EAAEkN,SAASlN,EAAEkN,QAAQ4f,OAEzB9sB,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAKoa,OACpC9sB,EAAEkB,KAAK,SAAUlB,IAIrBA,EAAE8yB,cAAgB,WAEd9yB,EAAEC,UAAUya,YAAY1a,EAAEwT,WAAW6B,KAAK,MAAM2Q,WAAW,SAG3DhmB,EAAE2U,QAAQqR,WAAW,SAGjBhmB,EAAES,QAAUT,EAAES,OAAOyB,QACrBlC,EAAES,OACGia,aACC1a,EAAEH,OAAOqR,kBACTlR,EAAEH,OAAOyE,iBACTtE,EAAEH,OAAOuR,eACTpR,EAAEH,OAAOyR,gBACT+D,KAAK,MACN2Q,WAAW,SACXA,WAAW,sBACXA,WAAW,mBAIhBhmB,EAAE6U,qBAAuB7U,EAAE6U,oBAAoB3S,QAC/ClC,EAAE6U,oBAAoB6F,YAAY1a,EAAEH,OAAOiS,uBAE3C9R,EAAE4b,SAAW5b,EAAE4b,QAAQ1Z,QACvBlC,EAAE4b,QAAQlB,YAAY1a,EAAEH,OAAO6R,mBAI/B1R,EAAEH,OAAO6P,YAAYlQ,EAAEQ,EAAEH,OAAO6P,YAAYgL,YAAY1a,EAAEH,OAAO8R,qBACjE3R,EAAEH,OAAO4P,YAAYjQ,EAAEQ,EAAEH,OAAO4P,YAAYiL,YAAY1a,EAAEH,OAAO8R,qBAGjE3R,EAAEH,OAAO8M,WAAa3M,EAAE2M,YACpB3M,EAAE2M,UAAUud,OAASlqB,EAAE2M,UAAUud,MAAMhoB,QAAQlC,EAAE2M,UAAUud,MAAMlE,WAAW,SAC5EhmB,EAAE2M,UAAU8d,MAAQzqB,EAAE2M,UAAU8d,KAAKvoB,QAAQlC,EAAE2M,UAAU8d,KAAKzE,WAAW,WAKrFhmB,EAAEgtB,QAAU,SAAU+F,EAAgBD,GAElC9yB,EAAEgf,eAEFhf,EAAEqB,eAEErB,EAAEH,OAAO8M,WAAa3M,EAAE2M,WACpB3M,EAAEH,OAAOgN,oBACT7M,EAAE2M,UAAUoe,mBAIhB/qB,EAAEH,OAAOkB,MACTf,EAAEoU,cAGF0e,GACA9yB,EAAE8yB,gBAGN9yB,EAAEulB,sBAGEvlB,EAAEH,OAAO0M,MAAQvM,EAAEuM,MACnBvM,EAAEuM,KAAKygB,UAGPhtB,EAAEH,OAAOkN,iBACL/M,EAAE+tB,wBAAwB/tB,EAAE+tB,yBAEhC/tB,EAAEH,OAAOmN,mBACLhN,EAAEkuB,0BAA0BluB,EAAEkuB,2BAGlCluB,EAAEH,OAAO6S,MAAQ1S,EAAE0S,MAAM1S,EAAE0S,KAAKsa,UAEhChtB,EAAEH,OAAOuN,UAAYpN,EAAEH,OAAOwN,cAC9B/K,OAAO0wB,oBAAoB,WAAYhzB,EAAEoN,QAAQogB,oBAEjDxtB,EAAEH,OAAOqN,SAAWlN,EAAEkN,SACtBlN,EAAEkN,QAAQ8f,UAGdhtB,EAAEkB,KAAK,aAEH6xB,KAAmB,IAAO/yB,EAAI,OAGtCA,EAAE8sB,OAKK9sB,GAOXX,GAAO4zB,WACH7K,SAAU,WACN,GAAI8K,GAAK1V,UAAUyQ,UAAU9pB,aAC7B,OAAQ+uB,GAAGjpB,QAAQ,WAAa,GAAKipB,EAAGjpB,QAAQ,UAAY,GAAKipB,EAAGjpB,QAAQ,WAAa,KAE7Foe,YAAa,+CAA+C8K,KAAK3V,UAAUyQ,WAC3E3B,QAAS,SAAU8G,GACf,MAAgD,mBAAzCC,OAAOJ,UAAU/N,SAASoO,MAAMF,IAK3ClV,SACIC,GAAI7b,OAAOkb,UAAUC,gBAAkBnb,OAAOkb,UAAUE,iBACxD+D,QAAUnf,OAAOkb,UAAUE,kBAAoBpb,OAAOkb,UAAU+V,iBAAmB,GAAOjxB,OAAOkb,UAAUC,gBAAkBnb,OAAOkb,UAAUgW,eAAiB,EAC/JpQ,OAAQ,WAEJ,GAAIqQ,GAAMzvB,SAASiC,cAAc,MAIjC,OAFAwtB,GAAIC,UAAY,wCAEgC,IAAzCD,EAAIE,qBAAqB,KAAKzxB,WAM7CiT,OAAQ,WACJ,GAAI+d,GAAK1V,UAAUyQ,UACf7Y,EAAU8d,EAAGU,MAAM,+BACnBC,EAAOX,EAAGU,MAAM,wBAChBE,EAAOZ,EAAGU,MAAM,2BAChBG,GAAUF,GAAQX,EAAGU,MAAM,yBAC/B,QACIlV,IAAKmV,GAAQE,GAAUD,EACvB1e,QAASA,MAMjBb,SACIG,MAASpS,OAAO0xB,WAAaA,UAAUtf,SAAU,GAAS,WACtD,SAAW,gBAAkBpS,SAAWA,OAAO2xB,eAAiBjwB,mBAAoBiwB,mBAGxFxf,aAAgBnS,OAAO0xB,WAAaA,UAAUE,mBAAoB,GAAS,WACvE,GAAIT,GAAMzvB,SAASiC,cAAc,OAAOgQ,KACxC,OAAQ,qBAAuBwd,IAAO,kBAAoBA,IAAO,gBAAkBA,IAAO,iBAAmBA,IAAO,eAAiBA,MAGzIjf,QAAS,WAGL,IAAK,GAFDif,GAAMzvB,SAASiC,cAAc,OAAOgQ,MACpCke,EAAS,yKAA2KxP,MAAM,KACrLpf,EAAI,EAAGA,EAAI4uB,EAAOjyB,OAAQqD,IAC/B,GAAI4uB,EAAO5uB,IAAMkuB,GAAK,OAAO,KAIrChxB,SAAU,WACN,MAAQ,oBAAsBH,SAAU,0BAA4BA,WAGxEic,gBAAiB,WACb,GAAI6V,IAAkB,CACtB,KACI,GAAIC,GAAOhB,OAAOiB,kBAAmB,WACjCC,IAAK,WACDH,GAAkB,IAG1B9xB,QAAOirB,iBAAiB,sBAAuB,KAAM8G,GACvD,MAAO7yB,IACT,MAAO4yB,MAGX1E,SAAU,WACN,MAAO,kBAAoBptB,YAMnCmvB,YAQJhyB,EAAiBD,EAEjB,IAAIg1B,GAASh1B,CAuEhB,OAvDOg1B,KACM,iBAAmBA,GAAO70B,KAC5B60B,EAAO70B,GAAGgY,cAAgB,SAAUhB,GAGhC,QAAS8d,GAAajzB,GAElB,GAAIA,EAAEG,SAAWpC,KAEjB,IADAoX,EAAS+d,KAAKn1B,KAAMiC,GACf+D,EAAI,EAAGA,EAAIovB,EAAOzyB,OAAQqD,IAC3BqvB,EAAI5J,IAAI2J,EAAOpvB,GAAIkvB,GAP3B,GACIlvB,GADAovB,GAAU,sBAAuB,gBAAiB,iBAAkB,kBAAmB,mBACjFC,EAAMr1B,IAShB,IAAIoX,EACA,IAAKpR,EAAI,EAAGA,EAAIovB,EAAOzyB,OAAQqD,IAC3BqvB,EAAI9J,GAAG6J,EAAOpvB,GAAIkvB,EAG1B,OAAOl1B,QAGT,aAAei1B,GAAO70B,KACxB60B,EAAO70B,GAAGwK,UAAY,SAAUA,GAC5B,IAAK,GAAI5E,GAAI,EAAGA,EAAIhG,KAAK2C,OAAQqD,IAAK,CAClC,GAAIsvB,GAAUt1B,KAAKgG,GAAG0Q,KACtB4e,GAAQnQ,gBAAkBmQ,EAAQ9P,YAAc8P,EAAQ7P,YAAc6P,EAAQhQ,aAAegQ,EAAQ/P,WAAa+P,EAAQ1qB,UAAYA,EAE1I,MAAO5K,QAGT,cAAgBi1B,GAAO70B,KACzB60B,EAAO70B,GAAGsc,WAAa,SAAU0H,GACL,gBAAbA,KACPA,GAAsB,KAE1B,KAAK,GAAIpe,GAAI,EAAGA,EAAIhG,KAAK2C,OAAQqD,IAAK,CAClC,GAAIsvB,GAAUt1B,KAAKgG,GAAG0Q,KACtB4e,GAAQC,yBAA2BD,EAAQE,qBAAuBF,EAAQG,qBAAuBH,EAAQI,sBAAwBJ,EAAQK,oBAAsBL,EAAQM,mBAAqBxR,EAEhM,MAAOpkB,QAGT,cAAgBi1B,GAAO70B,KACzB60B,EAAO70B,GAAGqa,WAAa,SAAUob,GAC7B,MAAI71B,MAAK2C,OAAS,EACVkzB,EACO71B,KAAK,GAAG2rB,YAAcvS,WAAWpZ,KAAK0V,IAAI,iBAAmB0D,WAAWpZ,KAAK0V,IAAI,gBAEjF1V,KAAK,GAAG2rB,YAEX,QAKpB7rB", "file": "../swiper.jquery.umd.min.js", "sourcesContent": ["/**\n * Swiper 3.4.0\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * \n * http://www.idangero.us/swiper/\n * \n * Copyright 2016, <PERSON>\n * The iDangero.us\n * http://www.idangero.us/\n * \n * Licensed under MIT\n * \n * Released on: October 16, 2016\n */\n(function (root, factory) {\n\t'use strict';\n\n\tif (typeof define === 'function' && define.amd) {\n\t\t// AMD. Register as an anonymous module.\n\t\tdefine(['jquery'], factory);\n\t} else if (typeof exports === 'object') {\n\t\t// Node. Does not work with strict CommonJS, but\n\t\t// only CommonJS-like environments that support module.exports,\n\t\t// like Node.\n\t\tmodule.exports = factory(require('jquery'));\n\t} else {\n\t\t// Browser globals (root is window)\n\t\troot.Swiper = factory(root.jQuery);\n\t}\n}(this, function ($) {\n\t'use strict';\n\n    /*===========================\n    Swiper\n    ===========================*/\n    var Swiper = function (container, params) {\n        if (!(this instanceof Swiper)) return new Swiper(container, params);\n\n        var defaults = {\n            direction: 'horizontal',\n            touchEventsTarget: 'container',\n            initialSlide: 0,\n            speed: 300,\n            // autoplay\n            autoplay: false,\n            autoplayDisableOnInteraction: true,\n            autoplayStopOnLast: false,\n            // To support iOS's swipe-to-go-back gesture (when being used in-app, with UIWebView).\n            iOSEdgeSwipeDetection: false,\n            iOSEdgeSwipeThreshold: 20,\n            // Free mode\n            freeMode: false,\n            freeModeMomentum: true,\n            freeModeMomentumRatio: 1,\n            freeModeMomentumBounce: true,\n            freeModeMomentumBounceRatio: 1,\n            freeModeMomentumVelocityRatio: 1,\n            freeModeSticky: false,\n            freeModeMinimumVelocity: 0.02,\n            // Autoheight\n            autoHeight: false,\n            // Set wrapper width\n            setWrapperSize: false,\n            // Virtual Translate\n            virtualTranslate: false,\n            // Effects\n            effect: 'slide', // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n            coverflow: {\n                rotate: 50,\n                stretch: 0,\n                depth: 100,\n                modifier: 1,\n                slideShadows : true\n            },\n            flip: {\n                slideShadows : true,\n                limitRotation: true\n            },\n            cube: {\n                slideShadows: true,\n                shadow: true,\n                shadowOffset: 20,\n                shadowScale: 0.94\n            },\n            fade: {\n                crossFade: false\n            },\n            // Parallax\n            parallax: false,\n            // Zoom\n            zoom: false,\n            zoomMax: 3,\n            zoomMin: 1,\n            zoomToggle: true,\n            // Scrollbar\n            scrollbar: null,\n            scrollbarHide: true,\n            scrollbarDraggable: false,\n            scrollbarSnapOnRelease: false,\n            // Keyboard Mousewheel\n            keyboardControl: false,\n            mousewheelControl: false,\n            mousewheelReleaseOnEdges: false,\n            mousewheelInvert: false,\n            mousewheelForceToAxis: false,\n            mousewheelSensitivity: 1,\n            mousewheelEventsTarged: 'container',\n            // Hash Navigation\n            hashnav: false,\n            hashnavWatchState: false,\n            // History\n            history: false,\n            // Commong Nav State\n            replaceState: false,\n            // Breakpoints\n            breakpoints: undefined,\n            // Slides grid\n            spaceBetween: 0,\n            slidesPerView: 1,\n            slidesPerColumn: 1,\n            slidesPerColumnFill: 'column',\n            slidesPerGroup: 1,\n            centeredSlides: false,\n            slidesOffsetBefore: 0, // in px\n            slidesOffsetAfter: 0, // in px\n            // Round length\n            roundLengths: false,\n            // Touches\n            touchRatio: 1,\n            touchAngle: 45,\n            simulateTouch: true,\n            shortSwipes: true,\n            longSwipes: true,\n            longSwipesRatio: 0.5,\n            longSwipesMs: 300,\n            followFinger: true,\n            onlyExternal: false,\n            threshold: 0,\n            touchMoveStopPropagation: true,\n            touchReleaseOnEdges: false,\n            // Unique Navigation Elements\n            uniqueNavElements: true,\n            // Pagination\n            pagination: null,\n            paginationElement: 'span',\n            paginationClickable: false,\n            paginationHide: false,\n            paginationBulletRender: null,\n            paginationProgressRender: null,\n            paginationFractionRender: null,\n            paginationCustomRender: null,\n            paginationType: 'bullets', // 'bullets' or 'progress' or 'fraction' or 'custom'\n            // Resistance\n            resistance: true,\n            resistanceRatio: 0.85,\n            // Next/prev buttons\n            nextButton: null,\n            prevButton: null,\n            // Progress\n            watchSlidesProgress: false,\n            watchSlidesVisibility: false,\n            // Cursor\n            grabCursor: false,\n            // Clicks\n            preventClicks: true,\n            preventClicksPropagation: true,\n            slideToClickedSlide: false,\n            // Lazy Loading\n            lazyLoading: false,\n            lazyLoadingInPrevNext: false,\n            lazyLoadingInPrevNextAmount: 1,\n            lazyLoadingOnTransitionStart: false,\n            // Images\n            preloadImages: true,\n            updateOnImagesReady: true,\n            // loop\n            loop: false,\n            loopAdditionalSlides: 0,\n            loopedSlides: null,\n            // Control\n            control: undefined,\n            controlInverse: false,\n            controlBy: 'slide', //or 'container'\n            normalizeSlideIndex: true,\n            // Swiping/no swiping\n            allowSwipeToPrev: true,\n            allowSwipeToNext: true,\n            swipeHandler: null, //'.swipe-handler',\n            noSwiping: true,\n            noSwipingClass: 'swiper-no-swiping',\n            // Passive Listeners\n            passiveListeners: true,\n            // NS\n            containerModifierClass: 'swiper-container-', // NEW\n            slideClass: 'swiper-slide',\n            slideActiveClass: 'swiper-slide-active',\n            slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n            slideVisibleClass: 'swiper-slide-visible',\n            slideDuplicateClass: 'swiper-slide-duplicate',\n            slideNextClass: 'swiper-slide-next',\n            slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n            slidePrevClass: 'swiper-slide-prev',\n            slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n            wrapperClass: 'swiper-wrapper',\n            bulletClass: 'swiper-pagination-bullet',\n            bulletActiveClass: 'swiper-pagination-bullet-active',\n            buttonDisabledClass: 'swiper-button-disabled',\n            paginationCurrentClass: 'swiper-pagination-current',\n            paginationTotalClass: 'swiper-pagination-total',\n            paginationHiddenClass: 'swiper-pagination-hidden',\n            paginationProgressbarClass: 'swiper-pagination-progressbar',\n            paginationClickableClass: 'swiper-pagination-clickable', // NEW\n            paginationModifierClass: 'swiper-pagination-', // NEW\n            lazyLoadingClass: 'swiper-lazy',\n            lazyStatusLoadingClass: 'swiper-lazy-loading',\n            lazyStatusLoadedClass: 'swiper-lazy-loaded',\n            lazyPreloaderClass: 'swiper-lazy-preloader',\n            notificationClass: 'swiper-notification',\n            preloaderClass: 'preloader',\n            zoomContainerClass: 'swiper-zoom-container',\n        \n            // Observer\n            observer: false,\n            observeParents: false,\n            // Accessibility\n            a11y: false,\n            prevSlideMessage: 'Previous slide',\n            nextSlideMessage: 'Next slide',\n            firstSlideMessage: 'This is the first slide',\n            lastSlideMessage: 'This is the last slide',\n            paginationBulletMessage: 'Go to slide {{index}}',\n            // Callbacks\n            runCallbacksOnInit: true\n            /*\n            Callbacks:\n            onInit: function (swiper)\n            onDestroy: function (swiper)\n            onClick: function (swiper, e)\n            onTap: function (swiper, e)\n            onDoubleTap: function (swiper, e)\n            onSliderMove: function (swiper, e)\n            onSlideChangeStart: function (swiper)\n            onSlideChangeEnd: function (swiper)\n            onTransitionStart: function (swiper)\n            onTransitionEnd: function (swiper)\n            onImagesReady: function (swiper)\n            onProgress: function (swiper, progress)\n            onTouchStart: function (swiper, e)\n            onTouchMove: function (swiper, e)\n            onTouchMoveOpposite: function (swiper, e)\n            onTouchEnd: function (swiper, e)\n            onReachBeginning: function (swiper)\n            onReachEnd: function (swiper)\n            onSetTransition: function (swiper, duration)\n            onSetTranslate: function (swiper, translate)\n            onAutoplayStart: function (swiper)\n            onAutoplayStop: function (swiper),\n            onLazyImageLoad: function (swiper, slide, image)\n            onLazyImageReady: function (swiper, slide, image)\n            */\n        \n        };\n        var initialVirtualTranslate = params && params.virtualTranslate;\n        \n        params = params || {};\n        var originalParams = {};\n        for (var param in params) {\n            if (typeof params[param] === 'object' && params[param] !== null && !(params[param].nodeType || params[param] === window || params[param] === document || (typeof Dom7 !== 'undefined' && params[param] instanceof Dom7) || (typeof jQuery !== 'undefined' && params[param] instanceof jQuery))) {\n                originalParams[param] = {};\n                for (var deepParam in params[param]) {\n                    originalParams[param][deepParam] = params[param][deepParam];\n                }\n            }\n            else {\n                originalParams[param] = params[param];\n            }\n        }\n        for (var def in defaults) {\n            if (typeof params[def] === 'undefined') {\n                params[def] = defaults[def];\n            }\n            else if (typeof params[def] === 'object') {\n                for (var deepDef in defaults[def]) {\n                    if (typeof params[def][deepDef] === 'undefined') {\n                        params[def][deepDef] = defaults[def][deepDef];\n                    }\n                }\n            }\n        }\n        \n        // Swiper\n        var s = this;\n        \n        // Params\n        s.params = params;\n        s.originalParams = originalParams;\n        \n        // Classname\n        s.classNames = [];\n        /*=========================\n          Dom Library and plugins\n          ===========================*/\n        if (typeof $ !== 'undefined' && typeof Dom7 !== 'undefined'){\n            $ = Dom7;\n        }\n        if (typeof $ === 'undefined') {\n            if (typeof Dom7 === 'undefined') {\n                $ = window.Dom7 || window.Zepto || window.jQuery;\n            }\n            else {\n                $ = Dom7;\n            }\n            if (!$) return;\n        }\n        // Export it to Swiper instance\n        s.$ = $;\n        \n        /*=========================\n          Breakpoints\n          ===========================*/\n        s.currentBreakpoint = undefined;\n        s.getActiveBreakpoint = function () {\n            //Get breakpoint for window width\n            if (!s.params.breakpoints) return false;\n            var breakpoint = false;\n            var points = [], point;\n            for ( point in s.params.breakpoints ) {\n                if (s.params.breakpoints.hasOwnProperty(point)) {\n                    points.push(point);\n                }\n            }\n            points.sort(function (a, b) {\n                return parseInt(a, 10) > parseInt(b, 10);\n            });\n            for (var i = 0; i < points.length; i++) {\n                point = points[i];\n                if (point >= window.innerWidth && !breakpoint) {\n                    breakpoint = point;\n                }\n            }\n            return breakpoint || 'max';\n        };\n        s.setBreakpoint = function () {\n            //Set breakpoint for window width and update parameters\n            var breakpoint = s.getActiveBreakpoint();\n            if (breakpoint && s.currentBreakpoint !== breakpoint) {\n                var breakPointsParams = breakpoint in s.params.breakpoints ? s.params.breakpoints[breakpoint] : s.originalParams;\n                var needsReLoop = s.params.loop && (breakPointsParams.slidesPerView !== s.params.slidesPerView);\n                for ( var param in breakPointsParams ) {\n                    s.params[param] = breakPointsParams[param];\n                }\n                s.currentBreakpoint = breakpoint;\n                if(needsReLoop && s.destroyLoop) {\n                    s.reLoop(true);\n                }\n            }\n        };\n        // Set breakpoint on load\n        if (s.params.breakpoints) {\n            s.setBreakpoint();\n        }\n        \n        /*=========================\n          Preparation - Define Container, Wrapper and Pagination\n          ===========================*/\n        s.container = $(container);\n        if (s.container.length === 0) return;\n        if (s.container.length > 1) {\n            var swipers = [];\n            s.container.each(function () {\n                var container = this;\n                swipers.push(new Swiper(this, params));\n            });\n            return swipers;\n        }\n        \n        // Save instance in container HTML Element and in data\n        s.container[0].swiper = s;\n        s.container.data('swiper', s);\n        \n        s.classNames.push(s.params.containerModifierClass + s.params.direction);\n        \n        if (s.params.freeMode) {\n            s.classNames.push(s.params.containerModifierClass + 'free-mode');\n        }\n        if (!s.support.flexbox) {\n            s.classNames.push(s.params.containerModifierClass + 'no-flexbox');\n            s.params.slidesPerColumn = 1;\n        }\n        if (s.params.autoHeight) {\n            s.classNames.push(s.params.containerModifierClass + 'autoheight');\n        }\n        // Enable slides progress when required\n        if (s.params.parallax || s.params.watchSlidesVisibility) {\n            s.params.watchSlidesProgress = true;\n        }\n        // Max resistance when touchReleaseOnEdges\n        if (s.params.touchReleaseOnEdges) {\n            s.params.resistanceRatio = 0;\n        }\n        // Coverflow / 3D\n        if (['cube', 'coverflow', 'flip'].indexOf(s.params.effect) >= 0) {\n            if (s.support.transforms3d) {\n                s.params.watchSlidesProgress = true;\n                s.classNames.push(s.params.containerModifierClass + '3d');\n            }\n            else {\n                s.params.effect = 'slide';\n            }\n        }\n        if (s.params.effect !== 'slide') {\n            s.classNames.push(s.params.containerModifierClass + s.params.effect);\n        }\n        if (s.params.effect === 'cube') {\n            s.params.resistanceRatio = 0;\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.centeredSlides = false;\n            s.params.spaceBetween = 0;\n            s.params.virtualTranslate = true;\n            s.params.setWrapperSize = false;\n        }\n        if (s.params.effect === 'fade' || s.params.effect === 'flip') {\n            s.params.slidesPerView = 1;\n            s.params.slidesPerColumn = 1;\n            s.params.slidesPerGroup = 1;\n            s.params.watchSlidesProgress = true;\n            s.params.spaceBetween = 0;\n            s.params.setWrapperSize = false;\n            if (typeof initialVirtualTranslate === 'undefined') {\n                s.params.virtualTranslate = true;\n            }\n        }\n        \n        // Grab Cursor\n        if (s.params.grabCursor && s.support.touch) {\n            s.params.grabCursor = false;\n        }\n        \n        // Wrapper\n        s.wrapper = s.container.children('.' + s.params.wrapperClass);\n        \n        // Pagination\n        if (s.params.pagination) {\n            s.paginationContainer = $(s.params.pagination);\n            if (s.params.uniqueNavElements && typeof s.params.pagination === 'string' && s.paginationContainer.length > 1 && s.container.find(s.params.pagination).length === 1) {\n                s.paginationContainer = s.container.find(s.params.pagination);\n            }\n        \n            if (s.params.paginationType === 'bullets' && s.params.paginationClickable) {\n                s.paginationContainer.addClass(s.params.paginationModifierClass + 'clickable');\n            }\n            else {\n                s.params.paginationClickable = false;\n            }\n            s.paginationContainer.addClass(s.params.paginationModifierClass + s.params.paginationType);\n        }\n        // Next/Prev Buttons\n        if (s.params.nextButton || s.params.prevButton) {\n            if (s.params.nextButton) {\n                s.nextButton = $(s.params.nextButton);\n                if (s.params.uniqueNavElements && typeof s.params.nextButton === 'string' && s.nextButton.length > 1 && s.container.find(s.params.nextButton).length === 1) {\n                    s.nextButton = s.container.find(s.params.nextButton);\n                }\n            }\n            if (s.params.prevButton) {\n                s.prevButton = $(s.params.prevButton);\n                if (s.params.uniqueNavElements && typeof s.params.prevButton === 'string' && s.prevButton.length > 1 && s.container.find(s.params.prevButton).length === 1) {\n                    s.prevButton = s.container.find(s.params.prevButton);\n                }\n            }\n        }\n        \n        // Is Horizontal\n        s.isHorizontal = function () {\n            return s.params.direction === 'horizontal';\n        };\n        // s.isH = isH;\n        \n        // RTL\n        s.rtl = s.isHorizontal() && (s.container[0].dir.toLowerCase() === 'rtl' || s.container.css('direction') === 'rtl');\n        if (s.rtl) {\n            s.classNames.push(s.params.containerModifierClass + 'rtl');\n        }\n        \n        // Wrong RTL support\n        if (s.rtl) {\n            s.wrongRTL = s.wrapper.css('display') === '-webkit-box';\n        }\n        \n        // Columns\n        if (s.params.slidesPerColumn > 1) {\n            s.classNames.push(s.params.containerModifierClass + 'multirow');\n        }\n        \n        // Check for Android\n        if (s.device.android) {\n            s.classNames.push(s.params.containerModifierClass + 'android');\n        }\n        \n        // Add classes\n        s.container.addClass(s.classNames.join(' '));\n        \n        // Translate\n        s.translate = 0;\n        \n        // Progress\n        s.progress = 0;\n        \n        // Velocity\n        s.velocity = 0;\n        \n        /*=========================\n          Locks, unlocks\n          ===========================*/\n        s.lockSwipeToNext = function () {\n            s.params.allowSwipeToNext = false;\n            if (s.params.allowSwipeToPrev === false && s.params.grabCursor) {\n                s.unsetGrabCursor();\n            }\n        };\n        s.lockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = false;\n            if (s.params.allowSwipeToNext === false && s.params.grabCursor) {\n                s.unsetGrabCursor();\n            }\n        };\n        s.lockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = false;\n            if (s.params.grabCursor) s.unsetGrabCursor();\n        };\n        s.unlockSwipeToNext = function () {\n            s.params.allowSwipeToNext = true;\n            if (s.params.allowSwipeToPrev === true && s.params.grabCursor) {\n                s.setGrabCursor();\n            }\n        };\n        s.unlockSwipeToPrev = function () {\n            s.params.allowSwipeToPrev = true;\n            if (s.params.allowSwipeToNext === true && s.params.grabCursor) {\n                s.setGrabCursor();\n            }\n        };\n        s.unlockSwipes = function () {\n            s.params.allowSwipeToNext = s.params.allowSwipeToPrev = true;\n            if (s.params.grabCursor) s.setGrabCursor();\n        };\n        \n        /*=========================\n          Round helper\n          ===========================*/\n        function round(a) {\n            return Math.floor(a);\n        }\n        /*=========================\n          Set grab cursor\n          ===========================*/\n        s.setGrabCursor = function(moving) {\n            s.container[0].style.cursor = 'move';\n            s.container[0].style.cursor = moving ? '-webkit-grabbing' : '-webkit-grab';\n            s.container[0].style.cursor = moving ? '-moz-grabbin' : '-moz-grab';\n            s.container[0].style.cursor = moving ? 'grabbing': 'grab';\n        };\n        s.unsetGrabCursor = function () {\n            s.container[0].style.cursor = '';\n        };\n        if (s.params.grabCursor) {\n            s.setGrabCursor();\n        }\n        /*=========================\n          Update on Images Ready\n          ===========================*/\n        s.imagesToLoad = [];\n        s.imagesLoaded = 0;\n        \n        s.loadImage = function (imgElement, src, srcset, sizes, checkForComplete, callback) {\n            var image;\n            function onReady () {\n                if (callback) callback();\n            }\n            if (!imgElement.complete || !checkForComplete) {\n                if (src) {\n                    image = new window.Image();\n                    image.onload = onReady;\n                    image.onerror = onReady;\n                    if (sizes) {\n                        image.sizes = sizes;\n                    }\n                    if (srcset) {\n                        image.srcset = srcset;\n                    }\n                    if (src) {\n                        image.src = src;\n                    }\n                } else {\n                    onReady();\n                }\n        \n            } else {//image already loaded...\n                onReady();\n            }\n        };\n        s.preloadImages = function () {\n            s.imagesToLoad = s.container.find('img');\n            function _onReady() {\n                if (typeof s === 'undefined' || s === null) return;\n                if (s.imagesLoaded !== undefined) s.imagesLoaded++;\n                if (s.imagesLoaded === s.imagesToLoad.length) {\n                    if (s.params.updateOnImagesReady) s.update();\n                    s.emit('onImagesReady', s);\n                }\n            }\n            for (var i = 0; i < s.imagesToLoad.length; i++) {\n                s.loadImage(s.imagesToLoad[i], (s.imagesToLoad[i].currentSrc || s.imagesToLoad[i].getAttribute('src')), (s.imagesToLoad[i].srcset || s.imagesToLoad[i].getAttribute('srcset')), s.imagesToLoad[i].sizes || s.imagesToLoad[i].getAttribute('sizes'), true, _onReady);\n            }\n        };\n        \n        /*=========================\n          Autoplay\n          ===========================*/\n        s.autoplayTimeoutId = undefined;\n        s.autoplaying = false;\n        s.autoplayPaused = false;\n        function autoplay() {\n            var autoplayDelay = s.params.autoplay;\n            var activeSlide = s.slides.eq(s.activeIndex);\n            if (activeSlide.attr('data-swiper-autoplay')) {\n                autoplayDelay = activeSlide.attr('data-swiper-autoplay') || s.params.autoplay;\n            }\n            s.autoplayTimeoutId = setTimeout(function () {\n                if (s.params.loop) {\n                    s.fixLoop();\n                    s._slideNext();\n                    s.emit('onAutoplay', s);\n                }\n                else {\n                    if (!s.isEnd) {\n                        s._slideNext();\n                        s.emit('onAutoplay', s);\n                    }\n                    else {\n                        if (!params.autoplayStopOnLast) {\n                            s._slideTo(0);\n                            s.emit('onAutoplay', s);\n                        }\n                        else {\n                            s.stopAutoplay();\n                        }\n                    }\n                }\n            }, autoplayDelay);\n        }\n        s.startAutoplay = function () {\n            if (typeof s.autoplayTimeoutId !== 'undefined') return false;\n            if (!s.params.autoplay) return false;\n            if (s.autoplaying) return false;\n            s.autoplaying = true;\n            s.emit('onAutoplayStart', s);\n            autoplay();\n        };\n        s.stopAutoplay = function (internal) {\n            if (!s.autoplayTimeoutId) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplaying = false;\n            s.autoplayTimeoutId = undefined;\n            s.emit('onAutoplayStop', s);\n        };\n        s.pauseAutoplay = function (speed) {\n            if (s.autoplayPaused) return;\n            if (s.autoplayTimeoutId) clearTimeout(s.autoplayTimeoutId);\n            s.autoplayPaused = true;\n            if (speed === 0) {\n                s.autoplayPaused = false;\n                autoplay();\n            }\n            else {\n                s.wrapper.transitionEnd(function () {\n                    if (!s) return;\n                    s.autoplayPaused = false;\n                    if (!s.autoplaying) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        autoplay();\n                    }\n                });\n            }\n        };\n        /*=========================\n          Min/Max Translate\n          ===========================*/\n        s.minTranslate = function () {\n            return (-s.snapGrid[0]);\n        };\n        s.maxTranslate = function () {\n            return (-s.snapGrid[s.snapGrid.length - 1]);\n        };\n        /*=========================\n          Slider/slides sizes\n          ===========================*/\n        s.updateAutoHeight = function () {\n            var activeSlides = [];\n            var newHeight = 0;\n        \n            // Find slides currently in view\n            if(s.params.slidesPerView !== 'auto' && s.params.slidesPerView > 1) {\n                for (i = 0; i < Math.ceil(s.params.slidesPerView); i++) {\n                    var index = s.activeIndex + i;\n                    if(index > s.slides.length) break;\n                    activeSlides.push(s.slides.eq(index)[0]);\n                }\n            } else {\n                activeSlides.push(s.slides.eq(s.activeIndex)[0]);\n            }\n        \n            // Find new height from heighest slide in view\n            for (i = 0; i < activeSlides.length; i++) {\n                if (typeof activeSlides[i] !== 'undefined') {\n                    var height = activeSlides[i].offsetHeight;\n                    newHeight = height > newHeight ? height : newHeight;\n                }\n            }\n        \n            // Update Height\n            if (newHeight) s.wrapper.css('height', newHeight + 'px');\n        };\n        s.updateContainerSize = function () {\n            var width, height;\n            if (typeof s.params.width !== 'undefined') {\n                width = s.params.width;\n            }\n            else {\n                width = s.container[0].clientWidth;\n            }\n            if (typeof s.params.height !== 'undefined') {\n                height = s.params.height;\n            }\n            else {\n                height = s.container[0].clientHeight;\n            }\n            if (width === 0 && s.isHorizontal() || height === 0 && !s.isHorizontal()) {\n                return;\n            }\n        \n            //Subtract paddings\n            width = width - parseInt(s.container.css('padding-left'), 10) - parseInt(s.container.css('padding-right'), 10);\n            height = height - parseInt(s.container.css('padding-top'), 10) - parseInt(s.container.css('padding-bottom'), 10);\n        \n            // Store values\n            s.width = width;\n            s.height = height;\n            s.size = s.isHorizontal() ? s.width : s.height;\n        };\n        \n        s.updateSlidesSize = function () {\n            s.slides = s.wrapper.children('.' + s.params.slideClass);\n            s.snapGrid = [];\n            s.slidesGrid = [];\n            s.slidesSizesGrid = [];\n        \n            var spaceBetween = s.params.spaceBetween,\n                slidePosition = -s.params.slidesOffsetBefore,\n                i,\n                prevSlideSize = 0,\n                index = 0;\n            if (typeof s.size === 'undefined') return;\n            if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n                spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * s.size;\n            }\n        \n            s.virtualSize = -spaceBetween;\n            // reset margins\n            if (s.rtl) s.slides.css({marginLeft: '', marginTop: ''});\n            else s.slides.css({marginRight: '', marginBottom: ''});\n        \n            var slidesNumberEvenToRows;\n            if (s.params.slidesPerColumn > 1) {\n                if (Math.floor(s.slides.length / s.params.slidesPerColumn) === s.slides.length / s.params.slidesPerColumn) {\n                    slidesNumberEvenToRows = s.slides.length;\n                }\n                else {\n                    slidesNumberEvenToRows = Math.ceil(s.slides.length / s.params.slidesPerColumn) * s.params.slidesPerColumn;\n                }\n                if (s.params.slidesPerView !== 'auto' && s.params.slidesPerColumnFill === 'row') {\n                    slidesNumberEvenToRows = Math.max(slidesNumberEvenToRows, s.params.slidesPerView * s.params.slidesPerColumn);\n                }\n            }\n        \n            // Calc slides\n            var slideSize;\n            var slidesPerColumn = s.params.slidesPerColumn;\n            var slidesPerRow = slidesNumberEvenToRows / slidesPerColumn;\n            var numFullColumns = slidesPerRow - (s.params.slidesPerColumn * slidesPerRow - s.slides.length);\n            for (i = 0; i < s.slides.length; i++) {\n                slideSize = 0;\n                var slide = s.slides.eq(i);\n                if (s.params.slidesPerColumn > 1) {\n                    // Set slides order\n                    var newSlideOrderIndex;\n                    var column, row;\n                    if (s.params.slidesPerColumnFill === 'column') {\n                        column = Math.floor(i / slidesPerColumn);\n                        row = i - column * slidesPerColumn;\n                        if (column > numFullColumns || (column === numFullColumns && row === slidesPerColumn-1)) {\n                            if (++row >= slidesPerColumn) {\n                                row = 0;\n                                column++;\n                            }\n                        }\n                        newSlideOrderIndex = column + row * slidesNumberEvenToRows / slidesPerColumn;\n                        slide\n                            .css({\n                                '-webkit-box-ordinal-group': newSlideOrderIndex,\n                                '-moz-box-ordinal-group': newSlideOrderIndex,\n                                '-ms-flex-order': newSlideOrderIndex,\n                                '-webkit-order': newSlideOrderIndex,\n                                'order': newSlideOrderIndex\n                            });\n                    }\n                    else {\n                        row = Math.floor(i / slidesPerRow);\n                        column = i - row * slidesPerRow;\n                    }\n                    slide\n                        .css(\n                            'margin-' + (s.isHorizontal() ? 'top' : 'left'),\n                            (row !== 0 && s.params.spaceBetween) && (s.params.spaceBetween + 'px')\n                        )\n                        .attr('data-swiper-column', column)\n                        .attr('data-swiper-row', row);\n        \n                }\n                if (slide.css('display') === 'none') continue;\n                if (s.params.slidesPerView === 'auto') {\n                    slideSize = s.isHorizontal() ? slide.outerWidth(true) : slide.outerHeight(true);\n                    if (s.params.roundLengths) slideSize = round(slideSize);\n                }\n                else {\n                    slideSize = (s.size - (s.params.slidesPerView - 1) * spaceBetween) / s.params.slidesPerView;\n                    if (s.params.roundLengths) slideSize = round(slideSize);\n        \n                    if (s.isHorizontal()) {\n                        s.slides[i].style.width = slideSize + 'px';\n                    }\n                    else {\n                        s.slides[i].style.height = slideSize + 'px';\n                    }\n                }\n                s.slides[i].swiperSlideSize = slideSize;\n                s.slidesSizesGrid.push(slideSize);\n        \n        \n                if (s.params.centeredSlides) {\n                    slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n                    if (i === 0) slidePosition = slidePosition - s.size / 2 - spaceBetween;\n                    if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                }\n                else {\n                    if ((index) % s.params.slidesPerGroup === 0) s.snapGrid.push(slidePosition);\n                    s.slidesGrid.push(slidePosition);\n                    slidePosition = slidePosition + slideSize + spaceBetween;\n                }\n        \n                s.virtualSize += slideSize + spaceBetween;\n        \n                prevSlideSize = slideSize;\n        \n                index ++;\n            }\n            s.virtualSize = Math.max(s.virtualSize, s.size) + s.params.slidesOffsetAfter;\n            var newSlidesGrid;\n        \n            if (\n                s.rtl && s.wrongRTL && (s.params.effect === 'slide' || s.params.effect === 'coverflow')) {\n                s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n            if (!s.support.flexbox || s.params.setWrapperSize) {\n                if (s.isHorizontal()) s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                else s.wrapper.css({height: s.virtualSize + s.params.spaceBetween + 'px'});\n            }\n        \n            if (s.params.slidesPerColumn > 1) {\n                s.virtualSize = (slideSize + s.params.spaceBetween) * slidesNumberEvenToRows;\n                s.virtualSize = Math.ceil(s.virtualSize / s.params.slidesPerColumn) - s.params.spaceBetween;\n                if (s.isHorizontal()) s.wrapper.css({width: s.virtualSize + s.params.spaceBetween + 'px'});\n                else s.wrapper.css({height: s.virtualSize + s.params.spaceBetween + 'px'});\n                if (s.params.centeredSlides) {\n                    newSlidesGrid = [];\n                    for (i = 0; i < s.snapGrid.length; i++) {\n                        if (s.snapGrid[i] < s.virtualSize + s.snapGrid[0]) newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                    s.snapGrid = newSlidesGrid;\n                }\n            }\n        \n            // Remove last grid elements depending on width\n            if (!s.params.centeredSlides) {\n                newSlidesGrid = [];\n                for (i = 0; i < s.snapGrid.length; i++) {\n                    if (s.snapGrid[i] <= s.virtualSize - s.size) {\n                        newSlidesGrid.push(s.snapGrid[i]);\n                    }\n                }\n                s.snapGrid = newSlidesGrid;\n                if (Math.floor(s.virtualSize - s.size) - Math.floor(s.snapGrid[s.snapGrid.length - 1]) > 1) {\n                    s.snapGrid.push(s.virtualSize - s.size);\n                }\n            }\n            if (s.snapGrid.length === 0) s.snapGrid = [0];\n        \n            if (s.params.spaceBetween !== 0) {\n                if (s.isHorizontal()) {\n                    if (s.rtl) s.slides.css({marginLeft: spaceBetween + 'px'});\n                    else s.slides.css({marginRight: spaceBetween + 'px'});\n                }\n                else s.slides.css({marginBottom: spaceBetween + 'px'});\n            }\n            if (s.params.watchSlidesProgress) {\n                s.updateSlidesOffset();\n            }\n        };\n        s.updateSlidesOffset = function () {\n            for (var i = 0; i < s.slides.length; i++) {\n                s.slides[i].swiperSlideOffset = s.isHorizontal() ? s.slides[i].offsetLeft : s.slides[i].offsetTop;\n            }\n        };\n        \n        /*=========================\n          Slider/slides progress\n          ===========================*/\n        s.updateSlidesProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            if (s.slides.length === 0) return;\n            if (typeof s.slides[0].swiperSlideOffset === 'undefined') s.updateSlidesOffset();\n        \n            var offsetCenter = -translate;\n            if (s.rtl) offsetCenter = translate;\n        \n            // Visible Slides\n            s.slides.removeClass(s.params.slideVisibleClass);\n            for (var i = 0; i < s.slides.length; i++) {\n                var slide = s.slides[i];\n                var slideProgress = (offsetCenter + (s.params.centeredSlides ? s.minTranslate() : 0) - slide.swiperSlideOffset) / (slide.swiperSlideSize + s.params.spaceBetween);\n                if (s.params.watchSlidesVisibility) {\n                    var slideBefore = -(offsetCenter - slide.swiperSlideOffset);\n                    var slideAfter = slideBefore + s.slidesSizesGrid[i];\n                    var isVisible =\n                        (slideBefore >= 0 && slideBefore < s.size) ||\n                        (slideAfter > 0 && slideAfter <= s.size) ||\n                        (slideBefore <= 0 && slideAfter >= s.size);\n                    if (isVisible) {\n                        s.slides.eq(i).addClass(s.params.slideVisibleClass);\n                    }\n                }\n                slide.progress = s.rtl ? -slideProgress : slideProgress;\n            }\n        };\n        s.updateProgress = function (translate) {\n            if (typeof translate === 'undefined') {\n                translate = s.translate || 0;\n            }\n            var translatesDiff = s.maxTranslate() - s.minTranslate();\n            var wasBeginning = s.isBeginning;\n            var wasEnd = s.isEnd;\n            if (translatesDiff === 0) {\n                s.progress = 0;\n                s.isBeginning = s.isEnd = true;\n            }\n            else {\n                s.progress = (translate - s.minTranslate()) / (translatesDiff);\n                s.isBeginning = s.progress <= 0;\n                s.isEnd = s.progress >= 1;\n            }\n            if (s.isBeginning && !wasBeginning) s.emit('onReachBeginning', s);\n            if (s.isEnd && !wasEnd) s.emit('onReachEnd', s);\n        \n            if (s.params.watchSlidesProgress) s.updateSlidesProgress(translate);\n            s.emit('onProgress', s, s.progress);\n        };\n        s.updateActiveIndex = function () {\n            var translate = s.rtl ? s.translate : -s.translate;\n            var newActiveIndex, i, snapIndex;\n            for (i = 0; i < s.slidesGrid.length; i ++) {\n                if (typeof s.slidesGrid[i + 1] !== 'undefined') {\n                    if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1] - (s.slidesGrid[i + 1] - s.slidesGrid[i]) / 2) {\n                        newActiveIndex = i;\n                    }\n                    else if (translate >= s.slidesGrid[i] && translate < s.slidesGrid[i + 1]) {\n                        newActiveIndex = i + 1;\n                    }\n                }\n                else {\n                    if (translate >= s.slidesGrid[i]) {\n                        newActiveIndex = i;\n                    }\n                }\n            }\n            // Normalize slideIndex\n            if(s.params.normalizeSlideIndex){\n                if (newActiveIndex < 0 || typeof newActiveIndex === 'undefined') newActiveIndex = 0;\n            }\n            // for (i = 0; i < s.slidesGrid.length; i++) {\n                // if (- translate >= s.slidesGrid[i]) {\n                    // newActiveIndex = i;\n                // }\n            // }\n            snapIndex = Math.floor(newActiveIndex / s.params.slidesPerGroup);\n            if (snapIndex >= s.snapGrid.length) snapIndex = s.snapGrid.length - 1;\n        \n            if (newActiveIndex === s.activeIndex) {\n                return;\n            }\n            s.snapIndex = snapIndex;\n            s.previousIndex = s.activeIndex;\n            s.activeIndex = newActiveIndex;\n            s.updateClasses();\n            s.updateRealIndex();\n        };\n        s.updateRealIndex = function(){\n            s.realIndex = s.slides.eq(s.activeIndex).attr('data-swiper-slide-index') || s.activeIndex;\n        };\n        \n        /*=========================\n          Classes\n          ===========================*/\n        s.updateClasses = function () {\n            s.slides.removeClass(s.params.slideActiveClass + ' ' + s.params.slideNextClass + ' ' + s.params.slidePrevClass + ' ' + s.params.slideDuplicateActiveClass + ' ' + s.params.slideDuplicateNextClass + ' ' + s.params.slideDuplicatePrevClass);\n            var activeSlide = s.slides.eq(s.activeIndex);\n            // Active classes\n            activeSlide.addClass(s.params.slideActiveClass);\n            if (params.loop) {\n                // Duplicate to all looped slides\n                if (activeSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + s.realIndex + '\"]').addClass(s.params.slideDuplicateActiveClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + s.realIndex + '\"]').addClass(s.params.slideDuplicateActiveClass);\n                }\n            }\n            // Next Slide\n            var nextSlide = activeSlide.next('.' + s.params.slideClass).addClass(s.params.slideNextClass);\n            if (s.params.loop && nextSlide.length === 0) {\n                nextSlide = s.slides.eq(0);\n                nextSlide.addClass(s.params.slideNextClass);\n            }\n            // Prev Slide\n            var prevSlide = activeSlide.prev('.' + s.params.slideClass).addClass(s.params.slidePrevClass);\n            if (s.params.loop && prevSlide.length === 0) {\n                prevSlide = s.slides.eq(-1);\n                prevSlide.addClass(s.params.slidePrevClass);\n            }\n            if (params.loop) {\n                // Duplicate to all looped slides\n                if (nextSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + nextSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicateNextClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + nextSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicateNextClass);\n                }\n                if (prevSlide.hasClass(s.params.slideDuplicateClass)) {\n                    s.wrapper.children('.' + s.params.slideClass + ':not(.' + s.params.slideDuplicateClass + ')[data-swiper-slide-index=\"' + prevSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicatePrevClass);\n                }\n                else {\n                    s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + prevSlide.attr('data-swiper-slide-index') + '\"]').addClass(s.params.slideDuplicatePrevClass);\n                }\n            }\n        \n            // Pagination\n            if (s.paginationContainer && s.paginationContainer.length > 0) {\n                // Current/Total\n                var current,\n                    total = s.params.loop ? Math.ceil((s.slides.length - s.loopedSlides * 2) / s.params.slidesPerGroup) : s.snapGrid.length;\n                if (s.params.loop) {\n                    current = Math.ceil((s.activeIndex - s.loopedSlides)/s.params.slidesPerGroup);\n                    if (current > s.slides.length - 1 - s.loopedSlides * 2) {\n                        current = current - (s.slides.length - s.loopedSlides * 2);\n                    }\n                    if (current > total - 1) current = current - total;\n                    if (current < 0 && s.params.paginationType !== 'bullets') current = total + current;\n                }\n                else {\n                    if (typeof s.snapIndex !== 'undefined') {\n                        current = s.snapIndex;\n                    }\n                    else {\n                        current = s.activeIndex || 0;\n                    }\n                }\n                // Types\n                if (s.params.paginationType === 'bullets' && s.bullets && s.bullets.length > 0) {\n                    s.bullets.removeClass(s.params.bulletActiveClass);\n                    if (s.paginationContainer.length > 1) {\n                        s.bullets.each(function () {\n                            if ($(this).index() === current) $(this).addClass(s.params.bulletActiveClass);\n                        });\n                    }\n                    else {\n                        s.bullets.eq(current).addClass(s.params.bulletActiveClass);\n                    }\n                }\n                if (s.params.paginationType === 'fraction') {\n                    s.paginationContainer.find('.' + s.params.paginationCurrentClass).text(current + 1);\n                    s.paginationContainer.find('.' + s.params.paginationTotalClass).text(total);\n                }\n                if (s.params.paginationType === 'progress') {\n                    var scale = (current + 1) / total,\n                        scaleX = scale,\n                        scaleY = 1;\n                    if (!s.isHorizontal()) {\n                        scaleY = scale;\n                        scaleX = 1;\n                    }\n                    s.paginationContainer.find('.' + s.params.paginationProgressbarClass).transform('translate3d(0,0,0) scaleX(' + scaleX + ') scaleY(' + scaleY + ')').transition(s.params.speed);\n                }\n                if (s.params.paginationType === 'custom' && s.params.paginationCustomRender) {\n                    s.paginationContainer.html(s.params.paginationCustomRender(s, current + 1, total));\n                    s.emit('onPaginationRendered', s, s.paginationContainer[0]);\n                }\n            }\n        \n            // Next/active buttons\n            if (!s.params.loop) {\n                if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                    if (s.isBeginning) {\n                        s.prevButton.addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable(s.prevButton);\n                    }\n                    else {\n                        s.prevButton.removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable(s.prevButton);\n                    }\n                }\n                if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                    if (s.isEnd) {\n                        s.nextButton.addClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.disable(s.nextButton);\n                    }\n                    else {\n                        s.nextButton.removeClass(s.params.buttonDisabledClass);\n                        if (s.params.a11y && s.a11y) s.a11y.enable(s.nextButton);\n                    }\n                }\n            }\n        };\n        \n        /*=========================\n          Pagination\n          ===========================*/\n        s.updatePagination = function () {\n            if (!s.params.pagination) return;\n            if (s.paginationContainer && s.paginationContainer.length > 0) {\n                var paginationHTML = '';\n                if (s.params.paginationType === 'bullets') {\n                    var numberOfBullets = s.params.loop ? Math.ceil((s.slides.length - s.loopedSlides * 2) / s.params.slidesPerGroup) : s.snapGrid.length;\n                    for (var i = 0; i < numberOfBullets; i++) {\n                        if (s.params.paginationBulletRender) {\n                            paginationHTML += s.params.paginationBulletRender(s, i, s.params.bulletClass);\n                        }\n                        else {\n                            paginationHTML += '<' + s.params.paginationElement+' class=\"' + s.params.bulletClass + '\"></' + s.params.paginationElement + '>';\n                        }\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                    s.bullets = s.paginationContainer.find('.' + s.params.bulletClass);\n                    if (s.params.paginationClickable && s.params.a11y && s.a11y) {\n                        s.a11y.initPagination();\n                    }\n                }\n                if (s.params.paginationType === 'fraction') {\n                    if (s.params.paginationFractionRender) {\n                        paginationHTML = s.params.paginationFractionRender(s, s.params.paginationCurrentClass, s.params.paginationTotalClass);\n                    }\n                    else {\n                        paginationHTML =\n                            '<span class=\"' + s.params.paginationCurrentClass + '\"></span>' +\n                            ' / ' +\n                            '<span class=\"' + s.params.paginationTotalClass+'\"></span>';\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                }\n                if (s.params.paginationType === 'progress') {\n                    if (s.params.paginationProgressRender) {\n                        paginationHTML = s.params.paginationProgressRender(s, s.params.paginationProgressbarClass);\n                    }\n                    else {\n                        paginationHTML = '<span class=\"' + s.params.paginationProgressbarClass + '\"></span>';\n                    }\n                    s.paginationContainer.html(paginationHTML);\n                }\n                if (s.params.paginationType !== 'custom') {\n                    s.emit('onPaginationRendered', s, s.paginationContainer[0]);\n                }\n            }\n        };\n        /*=========================\n          Common update method\n          ===========================*/\n        s.update = function (updateTranslate) {\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updateProgress();\n            s.updatePagination();\n            s.updateClasses();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            function forceSetTranslate() {\n                var translate = s.rtl ? -s.translate : s.translate;\n                newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n            }\n            if (updateTranslate) {\n                var translated, newTranslate;\n                if (s.controller && s.controller.spline) {\n                    s.controller.spline = undefined;\n                }\n                if (s.params.freeMode) {\n                    forceSetTranslate();\n                    if (s.params.autoHeight) {\n                        s.updateAutoHeight();\n                    }\n                }\n                else {\n                    if ((s.params.slidesPerView === 'auto' || s.params.slidesPerView > 1) && s.isEnd && !s.params.centeredSlides) {\n                        translated = s.slideTo(s.slides.length - 1, 0, false, true);\n                    }\n                    else {\n                        translated = s.slideTo(s.activeIndex, 0, false, true);\n                    }\n                    if (!translated) {\n                        forceSetTranslate();\n                    }\n                }\n            }\n            else if (s.params.autoHeight) {\n                s.updateAutoHeight();\n            }\n        };\n        \n        /*=========================\n          Resize Handler\n          ===========================*/\n        s.onResize = function (forceUpdatePagination) {\n            //Breakpoints\n            if (s.params.breakpoints) {\n                s.setBreakpoint();\n            }\n        \n            // Disable locks on resize\n            var allowSwipeToPrev = s.params.allowSwipeToPrev;\n            var allowSwipeToNext = s.params.allowSwipeToNext;\n            s.params.allowSwipeToPrev = s.params.allowSwipeToNext = true;\n        \n            s.updateContainerSize();\n            s.updateSlidesSize();\n            if (s.params.slidesPerView === 'auto' || s.params.freeMode || forceUpdatePagination) s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n            }\n            if (s.controller && s.controller.spline) {\n                s.controller.spline = undefined;\n            }\n            var slideChangedBySlideTo = false;\n            if (s.params.freeMode) {\n                var newTranslate = Math.min(Math.max(s.translate, s.maxTranslate()), s.minTranslate());\n                s.setWrapperTranslate(newTranslate);\n                s.updateActiveIndex();\n                s.updateClasses();\n        \n                if (s.params.autoHeight) {\n                    s.updateAutoHeight();\n                }\n            }\n            else {\n                s.updateClasses();\n                if ((s.params.slidesPerView === 'auto' || s.params.slidesPerView > 1) && s.isEnd && !s.params.centeredSlides) {\n                    slideChangedBySlideTo = s.slideTo(s.slides.length - 1, 0, false, true);\n                }\n                else {\n                    slideChangedBySlideTo = s.slideTo(s.activeIndex, 0, false, true);\n                }\n            }\n            if (s.params.lazyLoading && !slideChangedBySlideTo && s.lazy) {\n                s.lazy.load();\n            }\n            // Return locks after resize\n            s.params.allowSwipeToPrev = allowSwipeToPrev;\n            s.params.allowSwipeToNext = allowSwipeToNext;\n        };\n        \n        /*=========================\n          Events\n          ===========================*/\n        \n        //Define Touch Events\n        s.touchEventsDesktop = {start: 'mousedown', move: 'mousemove', end: 'mouseup'};\n        if (window.navigator.pointerEnabled) s.touchEventsDesktop = {start: 'pointerdown', move: 'pointermove', end: 'pointerup'};\n        else if (window.navigator.msPointerEnabled) s.touchEventsDesktop = {start: 'MSPointerDown', move: 'MSPointerMove', end: 'MSPointerUp'};\n        s.touchEvents = {\n            start : s.support.touch || !s.params.simulateTouch  ? 'touchstart' : s.touchEventsDesktop.start,\n            move : s.support.touch || !s.params.simulateTouch ? 'touchmove' : s.touchEventsDesktop.move,\n            end : s.support.touch || !s.params.simulateTouch ? 'touchend' : s.touchEventsDesktop.end\n        };\n        \n        \n        // WP8 Touch Events Fix\n        if (window.navigator.pointerEnabled || window.navigator.msPointerEnabled) {\n            (s.params.touchEventsTarget === 'container' ? s.container : s.wrapper).addClass('swiper-wp8-' + s.params.direction);\n        }\n        \n        // Attach/detach events\n        s.initEvents = function (detach) {\n            var actionDom = detach ? 'off' : 'on';\n            var action = detach ? 'removeEventListener' : 'addEventListener';\n            var touchEventsTarget = s.params.touchEventsTarget === 'container' ? s.container[0] : s.wrapper[0];\n            var target = s.support.touch ? touchEventsTarget : document;\n        \n            var moveCapture = s.params.nested ? true : false;\n        \n            //Touch Events\n            if (s.browser.ie) {\n                touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, false);\n                target[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                target[action](s.touchEvents.end, s.onTouchEnd, false);\n            }\n            else {\n                if (s.support.touch) {\n                    var passiveListener = s.touchEvents.start === 'touchstart' && s.support.passiveListener && s.params.passiveListeners ? {passive: true, capture: false} : false;\n                    touchEventsTarget[action](s.touchEvents.start, s.onTouchStart, passiveListener);\n                    touchEventsTarget[action](s.touchEvents.move, s.onTouchMove, moveCapture);\n                    touchEventsTarget[action](s.touchEvents.end, s.onTouchEnd, passiveListener);\n                }\n                if ((params.simulateTouch && !s.device.ios && !s.device.android) || (params.simulateTouch && !s.support.touch && s.device.ios)) {\n                    touchEventsTarget[action]('mousedown', s.onTouchStart, false);\n                    document[action]('mousemove', s.onTouchMove, moveCapture);\n                    document[action]('mouseup', s.onTouchEnd, false);\n                }\n            }\n            window[action]('resize', s.onResize);\n        \n            // Next, Prev, Index\n            if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                s.nextButton[actionDom]('click', s.onClickNext);\n                if (s.params.a11y && s.a11y) s.nextButton[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                s.prevButton[actionDom]('click', s.onClickPrev);\n                if (s.params.a11y && s.a11y) s.prevButton[actionDom]('keydown', s.a11y.onEnterKey);\n            }\n            if (s.params.pagination && s.params.paginationClickable) {\n                s.paginationContainer[actionDom]('click', '.' + s.params.bulletClass, s.onClickIndex);\n                if (s.params.a11y && s.a11y) s.paginationContainer[actionDom]('keydown', '.' + s.params.bulletClass, s.a11y.onEnterKey);\n            }\n        \n            // Prevent Links Clicks\n            if (s.params.preventClicks || s.params.preventClicksPropagation) touchEventsTarget[action]('click', s.preventClicks, true);\n        };\n        s.attachEvents = function () {\n            s.initEvents();\n        };\n        s.detachEvents = function () {\n            s.initEvents(true);\n        };\n        \n        /*=========================\n          Handle Clicks\n          ===========================*/\n        // Prevent Clicks\n        s.allowClick = true;\n        s.preventClicks = function (e) {\n            if (!s.allowClick) {\n                if (s.params.preventClicks) e.preventDefault();\n                if (s.params.preventClicksPropagation && s.animating) {\n                    e.stopPropagation();\n                    e.stopImmediatePropagation();\n                }\n            }\n        };\n        // Clicks\n        s.onClickNext = function (e) {\n            e.preventDefault();\n            if (s.isEnd && !s.params.loop) return;\n            s.slideNext();\n        };\n        s.onClickPrev = function (e) {\n            e.preventDefault();\n            if (s.isBeginning && !s.params.loop) return;\n            s.slidePrev();\n        };\n        s.onClickIndex = function (e) {\n            e.preventDefault();\n            var index = $(this).index() * s.params.slidesPerGroup;\n            if (s.params.loop) index = index + s.loopedSlides;\n            s.slideTo(index);\n        };\n        \n        /*=========================\n          Handle Touches\n          ===========================*/\n        function findElementInEvent(e, selector) {\n            var el = $(e.target);\n            if (!el.is(selector)) {\n                if (typeof selector === 'string') {\n                    el = el.parents(selector);\n                }\n                else if (selector.nodeType) {\n                    var found;\n                    el.parents().each(function (index, _el) {\n                        if (_el === selector) found = selector;\n                    });\n                    if (!found) return undefined;\n                    else return selector;\n                }\n            }\n            if (el.length === 0) {\n                return undefined;\n            }\n            return el[0];\n        }\n        s.updateClickedSlide = function (e) {\n            var slide = findElementInEvent(e, '.' + s.params.slideClass);\n            var slideFound = false;\n            if (slide) {\n                for (var i = 0; i < s.slides.length; i++) {\n                    if (s.slides[i] === slide) slideFound = true;\n                }\n            }\n        \n            if (slide && slideFound) {\n                s.clickedSlide = slide;\n                s.clickedIndex = $(slide).index();\n            }\n            else {\n                s.clickedSlide = undefined;\n                s.clickedIndex = undefined;\n                return;\n            }\n            if (s.params.slideToClickedSlide && s.clickedIndex !== undefined && s.clickedIndex !== s.activeIndex) {\n                var slideToIndex = s.clickedIndex,\n                    realIndex,\n                    duplicatedSlides;\n                if (s.params.loop) {\n                    if (s.animating) return;\n                    realIndex = $(s.clickedSlide).attr('data-swiper-slide-index');\n                    if (s.params.centeredSlides) {\n                        if ((slideToIndex < s.loopedSlides - s.params.slidesPerView/2) || (slideToIndex > s.slides.length - s.loopedSlides + s.params.slidesPerView/2)) {\n                            s.fixLoop();\n                            slideToIndex = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')').eq(0).index();\n                            setTimeout(function () {\n                                s.slideTo(slideToIndex);\n                            }, 0);\n                        }\n                        else {\n                            s.slideTo(slideToIndex);\n                        }\n                    }\n                    else {\n                        if (slideToIndex > s.slides.length - s.params.slidesPerView) {\n                            s.fixLoop();\n                            slideToIndex = s.wrapper.children('.' + s.params.slideClass + '[data-swiper-slide-index=\"' + realIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')').eq(0).index();\n                            setTimeout(function () {\n                                s.slideTo(slideToIndex);\n                            }, 0);\n                        }\n                        else {\n                            s.slideTo(slideToIndex);\n                        }\n                    }\n                }\n                else {\n                    s.slideTo(slideToIndex);\n                }\n            }\n        };\n        \n        var isTouched,\n            isMoved,\n            allowTouchCallbacks,\n            touchStartTime,\n            isScrolling,\n            currentTranslate,\n            startTranslate,\n            allowThresholdMove,\n            // Form elements to match\n            formElements = 'input, select, textarea, button, video',\n            // Last click time\n            lastClickTime = Date.now(), clickTimeout,\n            //Velocities\n            velocities = [],\n            allowMomentumBounce;\n        \n        // Animating Flag\n        s.animating = false;\n        \n        // Touches information\n        s.touches = {\n            startX: 0,\n            startY: 0,\n            currentX: 0,\n            currentY: 0,\n            diff: 0\n        };\n        \n        // Touch handlers\n        var isTouchEvent, startMoving;\n        s.onTouchStart = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            isTouchEvent = e.type === 'touchstart';\n            if (!isTouchEvent && 'which' in e && e.which === 3) return;\n            if (s.params.noSwiping && findElementInEvent(e, '.' + s.params.noSwipingClass)) {\n                s.allowClick = true;\n                return;\n            }\n            if (s.params.swipeHandler) {\n                if (!findElementInEvent(e, s.params.swipeHandler)) return;\n            }\n        \n            var startX = s.touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n            var startY = s.touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n        \n            // Do NOT start if iOS edge swipe is detected. Otherwise iOS app (UIWebView) cannot swipe-to-go-back anymore\n            if(s.device.ios && s.params.iOSEdgeSwipeDetection && startX <= s.params.iOSEdgeSwipeThreshold) {\n                return;\n            }\n        \n            isTouched = true;\n            isMoved = false;\n            allowTouchCallbacks = true;\n            isScrolling = undefined;\n            startMoving = undefined;\n            s.touches.startX = startX;\n            s.touches.startY = startY;\n            touchStartTime = Date.now();\n            s.allowClick = true;\n            s.updateContainerSize();\n            s.swipeDirection = undefined;\n            if (s.params.threshold > 0) allowThresholdMove = false;\n            if (e.type !== 'touchstart') {\n                var preventDefault = true;\n                if ($(e.target).is(formElements)) preventDefault = false;\n                if (document.activeElement && $(document.activeElement).is(formElements)) {\n                    document.activeElement.blur();\n                }\n                if (preventDefault) {\n                    e.preventDefault();\n                }\n            }\n            s.emit('onTouchStart', s, e);\n        };\n        \n        s.onTouchMove = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            if (isTouchEvent && e.type === 'mousemove') return;\n            if (e.preventedByNestedSwiper) {\n                s.touches.startX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                s.touches.startY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n                return;\n            }\n            if (s.params.onlyExternal) {\n                // isMoved = true;\n                s.allowClick = false;\n                if (isTouched) {\n                    s.touches.startX = s.touches.currentX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                    s.touches.startY = s.touches.currentY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n                    touchStartTime = Date.now();\n                }\n                return;\n            }\n            if (isTouchEvent && s.params.touchReleaseOnEdges && !s.params.loop) {\n                if (!s.isHorizontal()) {\n                    // Vertical\n                    if (\n                        (s.touches.currentY < s.touches.startY && s.translate <= s.maxTranslate()) ||\n                        (s.touches.currentY > s.touches.startY && s.translate >= s.minTranslate())\n                        ) {\n                        return;\n                    }\n                }\n                else {\n                    if (\n                        (s.touches.currentX < s.touches.startX && s.translate <= s.maxTranslate()) ||\n                        (s.touches.currentX > s.touches.startX && s.translate >= s.minTranslate())\n                        ) {\n                        return;\n                    }\n                }\n            }\n            if (isTouchEvent && document.activeElement) {\n                if (e.target === document.activeElement && $(e.target).is(formElements)) {\n                    isMoved = true;\n                    s.allowClick = false;\n                    return;\n                }\n            }\n            if (allowTouchCallbacks) {\n                s.emit('onTouchMove', s, e);\n            }\n            if (e.targetTouches && e.targetTouches.length > 1) return;\n        \n            s.touches.currentX = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n            s.touches.currentY = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n        \n            if (typeof isScrolling === 'undefined') {\n                var touchAngle;\n                if (s.isHorizontal() && s.touches.currentY === s.touches.startY || !s.isHorizontal() && s.touches.currentX !== s.touches.startX) {\n                    isScrolling = false;\n                }\n                else {\n                    touchAngle = Math.atan2(Math.abs(s.touches.currentY - s.touches.startY), Math.abs(s.touches.currentX - s.touches.startX)) * 180 / Math.PI;\n                    isScrolling = s.isHorizontal() ? touchAngle > s.params.touchAngle : (90 - touchAngle > s.params.touchAngle);\n                }\n            }\n            if (isScrolling) {\n                s.emit('onTouchMoveOpposite', s, e);\n            }\n            if (typeof startMoving === 'undefined' && s.browser.ieTouch) {\n                if (s.touches.currentX !== s.touches.startX || s.touches.currentY !== s.touches.startY) {\n                    startMoving = true;\n                }\n            }\n            if (!isTouched) return;\n            if (isScrolling)  {\n                isTouched = false;\n                return;\n            }\n            if (!startMoving && s.browser.ieTouch) {\n                return;\n            }\n            s.allowClick = false;\n            s.emit('onSliderMove', s, e);\n            e.preventDefault();\n            if (s.params.touchMoveStopPropagation && !s.params.nested) {\n                e.stopPropagation();\n            }\n        \n            if (!isMoved) {\n                if (params.loop) {\n                    s.fixLoop();\n                }\n                startTranslate = s.getWrapperTranslate();\n                s.setWrapperTransition(0);\n                if (s.animating) {\n                    s.wrapper.trigger('webkitTransitionEnd transitionend oTransitionEnd MSTransitionEnd msTransitionEnd');\n                }\n                if (s.params.autoplay && s.autoplaying) {\n                    if (s.params.autoplayDisableOnInteraction) {\n                        s.stopAutoplay();\n                    }\n                    else {\n                        s.pauseAutoplay();\n                    }\n                }\n                allowMomentumBounce = false;\n                //Grab Cursor\n                if (s.params.grabCursor && (s.params.allowSwipeToNext === true || s.params.allowSwipeToPrev === true)) {\n                    s.setGrabCursor(true);\n                }\n            }\n            isMoved = true;\n        \n            var diff = s.touches.diff = s.isHorizontal() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n        \n            diff = diff * s.params.touchRatio;\n            if (s.rtl) diff = -diff;\n        \n            s.swipeDirection = diff > 0 ? 'prev' : 'next';\n            currentTranslate = diff + startTranslate;\n        \n            var disableParentSwiper = true;\n            if ((diff > 0 && currentTranslate > s.minTranslate())) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.minTranslate() - 1 + Math.pow(-s.minTranslate() + startTranslate + diff, s.params.resistanceRatio);\n            }\n            else if (diff < 0 && currentTranslate < s.maxTranslate()) {\n                disableParentSwiper = false;\n                if (s.params.resistance) currentTranslate = s.maxTranslate() + 1 - Math.pow(s.maxTranslate() - startTranslate - diff, s.params.resistanceRatio);\n            }\n        \n            if (disableParentSwiper) {\n                e.preventedByNestedSwiper = true;\n            }\n        \n            // Directions locks\n            if (!s.params.allowSwipeToNext && s.swipeDirection === 'next' && currentTranslate < startTranslate) {\n                currentTranslate = startTranslate;\n            }\n            if (!s.params.allowSwipeToPrev && s.swipeDirection === 'prev' && currentTranslate > startTranslate) {\n                currentTranslate = startTranslate;\n            }\n        \n        \n            // Threshold\n            if (s.params.threshold > 0) {\n                if (Math.abs(diff) > s.params.threshold || allowThresholdMove) {\n                    if (!allowThresholdMove) {\n                        allowThresholdMove = true;\n                        s.touches.startX = s.touches.currentX;\n                        s.touches.startY = s.touches.currentY;\n                        currentTranslate = startTranslate;\n                        s.touches.diff = s.isHorizontal() ? s.touches.currentX - s.touches.startX : s.touches.currentY - s.touches.startY;\n                        return;\n                    }\n                }\n                else {\n                    currentTranslate = startTranslate;\n                    return;\n                }\n            }\n        \n            if (!s.params.followFinger) return;\n        \n            // Update active index in free mode\n            if (s.params.freeMode || s.params.watchSlidesProgress) {\n                s.updateActiveIndex();\n            }\n            if (s.params.freeMode) {\n                //Velocity\n                if (velocities.length === 0) {\n                    velocities.push({\n                        position: s.touches[s.isHorizontal() ? 'startX' : 'startY'],\n                        time: touchStartTime\n                    });\n                }\n                velocities.push({\n                    position: s.touches[s.isHorizontal() ? 'currentX' : 'currentY'],\n                    time: (new window.Date()).getTime()\n                });\n            }\n            // Update progress\n            s.updateProgress(currentTranslate);\n            // Update translate\n            s.setWrapperTranslate(currentTranslate);\n        };\n        s.onTouchEnd = function (e) {\n            if (e.originalEvent) e = e.originalEvent;\n            if (allowTouchCallbacks) {\n                s.emit('onTouchEnd', s, e);\n            }\n            allowTouchCallbacks = false;\n            if (!isTouched) return;\n            //Return Grab Cursor\n            if (s.params.grabCursor && isMoved && isTouched  && (s.params.allowSwipeToNext === true || s.params.allowSwipeToPrev === true)) {\n                s.setGrabCursor(false);\n            }\n        \n            // Time diff\n            var touchEndTime = Date.now();\n            var timeDiff = touchEndTime - touchStartTime;\n        \n            // Tap, doubleTap, Click\n            if (s.allowClick) {\n                s.updateClickedSlide(e);\n                s.emit('onTap', s, e);\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) > 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    clickTimeout = setTimeout(function () {\n                        if (!s) return;\n                        if (s.params.paginationHide && s.paginationContainer.length > 0 && !$(e.target).hasClass(s.params.bulletClass)) {\n                            s.paginationContainer.toggleClass(s.params.paginationHiddenClass);\n                        }\n                        s.emit('onClick', s, e);\n                    }, 300);\n        \n                }\n                if (timeDiff < 300 && (touchEndTime - lastClickTime) < 300) {\n                    if (clickTimeout) clearTimeout(clickTimeout);\n                    s.emit('onDoubleTap', s, e);\n                }\n            }\n        \n            lastClickTime = Date.now();\n            setTimeout(function () {\n                if (s) s.allowClick = true;\n            }, 0);\n        \n            if (!isTouched || !isMoved || !s.swipeDirection || s.touches.diff === 0 || currentTranslate === startTranslate) {\n                isTouched = isMoved = false;\n                return;\n            }\n            isTouched = isMoved = false;\n        \n            var currentPos;\n            if (s.params.followFinger) {\n                currentPos = s.rtl ? s.translate : -s.translate;\n            }\n            else {\n                currentPos = -currentTranslate;\n            }\n            if (s.params.freeMode) {\n                if (currentPos < -s.minTranslate()) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                else if (currentPos > -s.maxTranslate()) {\n                    if (s.slides.length < s.snapGrid.length) {\n                        s.slideTo(s.snapGrid.length - 1);\n                    }\n                    else {\n                        s.slideTo(s.slides.length - 1);\n                    }\n                    return;\n                }\n        \n                if (s.params.freeModeMomentum) {\n                    if (velocities.length > 1) {\n                        var lastMoveEvent = velocities.pop(), velocityEvent = velocities.pop();\n        \n                        var distance = lastMoveEvent.position - velocityEvent.position;\n                        var time = lastMoveEvent.time - velocityEvent.time;\n                        s.velocity = distance / time;\n                        s.velocity = s.velocity / 2;\n                        if (Math.abs(s.velocity) < s.params.freeModeMinimumVelocity) {\n                            s.velocity = 0;\n                        }\n                        // this implies that the user stopped moving a finger then released.\n                        // There would be no events with distance zero, so the last event is stale.\n                        if (time > 150 || (new window.Date().getTime() - lastMoveEvent.time) > 300) {\n                            s.velocity = 0;\n                        }\n                    } else {\n                        s.velocity = 0;\n                    }\n                    s.velocity = s.velocity * s.params.freeModeMomentumVelocityRatio;\n        \n                    velocities.length = 0;\n                    var momentumDuration = 1000 * s.params.freeModeMomentumRatio;\n                    var momentumDistance = s.velocity * momentumDuration;\n        \n                    var newPosition = s.translate + momentumDistance;\n                    if (s.rtl) newPosition = - newPosition;\n                    var doBounce = false;\n                    var afterBouncePosition;\n                    var bounceAmount = Math.abs(s.velocity) * 20 * s.params.freeModeMomentumBounceRatio;\n                    if (newPosition < s.maxTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition + s.maxTranslate() < -bounceAmount) {\n                                newPosition = s.maxTranslate() - bounceAmount;\n                            }\n                            afterBouncePosition = s.maxTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.maxTranslate();\n                        }\n                    }\n                    else if (newPosition > s.minTranslate()) {\n                        if (s.params.freeModeMomentumBounce) {\n                            if (newPosition - s.minTranslate() > bounceAmount) {\n                                newPosition = s.minTranslate() + bounceAmount;\n                            }\n                            afterBouncePosition = s.minTranslate();\n                            doBounce = true;\n                            allowMomentumBounce = true;\n                        }\n                        else {\n                            newPosition = s.minTranslate();\n                        }\n                    }\n                    else if (s.params.freeModeSticky) {\n                        var j = 0,\n                            nextSlide;\n                        for (j = 0; j < s.snapGrid.length; j += 1) {\n                            if (s.snapGrid[j] > -newPosition) {\n                                nextSlide = j;\n                                break;\n                            }\n        \n                        }\n                        if (Math.abs(s.snapGrid[nextSlide] - newPosition) < Math.abs(s.snapGrid[nextSlide - 1] - newPosition) || s.swipeDirection === 'next') {\n                            newPosition = s.snapGrid[nextSlide];\n                        } else {\n                            newPosition = s.snapGrid[nextSlide - 1];\n                        }\n                        if (!s.rtl) newPosition = - newPosition;\n                    }\n                    //Fix duration\n                    if (s.velocity !== 0) {\n                        if (s.rtl) {\n                            momentumDuration = Math.abs((-newPosition - s.translate) / s.velocity);\n                        }\n                        else {\n                            momentumDuration = Math.abs((newPosition - s.translate) / s.velocity);\n                        }\n                    }\n                    else if (s.params.freeModeSticky) {\n                        s.slideReset();\n                        return;\n                    }\n        \n                    if (s.params.freeModeMomentumBounce && doBounce) {\n                        s.updateProgress(afterBouncePosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        s.animating = true;\n                        s.wrapper.transitionEnd(function () {\n                            if (!s || !allowMomentumBounce) return;\n                            s.emit('onMomentumBounce', s);\n        \n                            s.setWrapperTransition(s.params.speed);\n                            s.setWrapperTranslate(afterBouncePosition);\n                            s.wrapper.transitionEnd(function () {\n                                if (!s) return;\n                                s.onTransitionEnd();\n                            });\n                        });\n                    } else if (s.velocity) {\n                        s.updateProgress(newPosition);\n                        s.setWrapperTransition(momentumDuration);\n                        s.setWrapperTranslate(newPosition);\n                        s.onTransitionStart();\n                        if (!s.animating) {\n                            s.animating = true;\n                            s.wrapper.transitionEnd(function () {\n                                if (!s) return;\n                                s.onTransitionEnd();\n                            });\n                        }\n        \n                    } else {\n                        s.updateProgress(newPosition);\n                    }\n        \n                    s.updateActiveIndex();\n                }\n                if (!s.params.freeModeMomentum || timeDiff >= s.params.longSwipesMs) {\n                    s.updateProgress();\n                    s.updateActiveIndex();\n                }\n                return;\n            }\n        \n            // Find current slide\n            var i, stopIndex = 0, groupSize = s.slidesSizesGrid[0];\n            for (i = 0; i < s.slidesGrid.length; i += s.params.slidesPerGroup) {\n                if (typeof s.slidesGrid[i + s.params.slidesPerGroup] !== 'undefined') {\n                    if (currentPos >= s.slidesGrid[i] && currentPos < s.slidesGrid[i + s.params.slidesPerGroup]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[i + s.params.slidesPerGroup] - s.slidesGrid[i];\n                    }\n                }\n                else {\n                    if (currentPos >= s.slidesGrid[i]) {\n                        stopIndex = i;\n                        groupSize = s.slidesGrid[s.slidesGrid.length - 1] - s.slidesGrid[s.slidesGrid.length - 2];\n                    }\n                }\n            }\n        \n            // Find current slide size\n            var ratio = (currentPos - s.slidesGrid[stopIndex]) / groupSize;\n        \n            if (timeDiff > s.params.longSwipesMs) {\n                // Long touches\n                if (!s.params.longSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    if (ratio >= s.params.longSwipesRatio) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    if (ratio > (1 - s.params.longSwipesRatio)) s.slideTo(stopIndex + s.params.slidesPerGroup);\n                    else s.slideTo(stopIndex);\n                }\n            }\n            else {\n                // Short swipes\n                if (!s.params.shortSwipes) {\n                    s.slideTo(s.activeIndex);\n                    return;\n                }\n                if (s.swipeDirection === 'next') {\n                    s.slideTo(stopIndex + s.params.slidesPerGroup);\n        \n                }\n                if (s.swipeDirection === 'prev') {\n                    s.slideTo(stopIndex);\n                }\n            }\n        };\n        /*=========================\n          Transitions\n          ===========================*/\n        s._slideTo = function (slideIndex, speed) {\n            return s.slideTo(slideIndex, speed, true, true);\n        };\n        s.slideTo = function (slideIndex, speed, runCallbacks, internal) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (typeof slideIndex === 'undefined') slideIndex = 0;\n            if (slideIndex < 0) slideIndex = 0;\n            s.snapIndex = Math.floor(slideIndex / s.params.slidesPerGroup);\n            if (s.snapIndex >= s.snapGrid.length) s.snapIndex = s.snapGrid.length - 1;\n        \n            var translate = - s.snapGrid[s.snapIndex];\n            // Stop autoplay\n            if (s.params.autoplay && s.autoplaying) {\n                if (internal || !s.params.autoplayDisableOnInteraction) {\n                    s.pauseAutoplay(speed);\n                }\n                else {\n                    s.stopAutoplay();\n                }\n            }\n            // Update progress\n            s.updateProgress(translate);\n        \n            // Normalize slideIndex\n            if(s.params.normalizeSlideIndex){\n                for (var i = 0; i < s.slidesGrid.length; i++) {\n                    if (- Math.floor(translate * 100) >= Math.floor(s.slidesGrid[i] * 100)) {\n                        slideIndex = i;\n                    }\n                }\n            }\n        \n            // Directions locks\n            if (!s.params.allowSwipeToNext && translate < s.translate && translate < s.minTranslate()) {\n                return false;\n            }\n            if (!s.params.allowSwipeToPrev && translate > s.translate && translate > s.maxTranslate()) {\n                if ((s.activeIndex || 0) !== slideIndex ) return false;\n            }\n        \n            // Update Index\n            if (typeof speed === 'undefined') speed = s.params.speed;\n            s.previousIndex = s.activeIndex || 0;\n            s.activeIndex = slideIndex;\n            s.updateRealIndex();\n            if ((s.rtl && -translate === s.translate) || (!s.rtl && translate === s.translate)) {\n                // Update Height\n                if (s.params.autoHeight) {\n                    s.updateAutoHeight();\n                }\n                s.updateClasses();\n                if (s.params.effect !== 'slide') {\n                    s.setWrapperTranslate(translate);\n                }\n                return false;\n            }\n            s.updateClasses();\n            s.onTransitionStart(runCallbacks);\n        \n            if (speed === 0 || s.browser.lteIE9) {\n                s.setWrapperTranslate(translate);\n                s.setWrapperTransition(0);\n                s.onTransitionEnd(runCallbacks);\n            }\n            else {\n                s.setWrapperTranslate(translate);\n                s.setWrapperTransition(speed);\n                if (!s.animating) {\n                    s.animating = true;\n                    s.wrapper.transitionEnd(function () {\n                        if (!s) return;\n                        s.onTransitionEnd(runCallbacks);\n                    });\n                }\n        \n            }\n        \n            return true;\n        };\n        \n        s.onTransitionStart = function (runCallbacks) {\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.params.autoHeight) {\n                s.updateAutoHeight();\n            }\n            if (s.lazy) s.lazy.onTransitionStart();\n            if (runCallbacks) {\n                s.emit('onTransitionStart', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeStart', s);\n                    if (s.activeIndex > s.previousIndex) {\n                        s.emit('onSlideNextStart', s);\n                    }\n                    else {\n                        s.emit('onSlidePrevStart', s);\n                    }\n                }\n        \n            }\n        };\n        s.onTransitionEnd = function (runCallbacks) {\n            s.animating = false;\n            s.setWrapperTransition(0);\n            if (typeof runCallbacks === 'undefined') runCallbacks = true;\n            if (s.lazy) s.lazy.onTransitionEnd();\n            if (runCallbacks) {\n                s.emit('onTransitionEnd', s);\n                if (s.activeIndex !== s.previousIndex) {\n                    s.emit('onSlideChangeEnd', s);\n                    if (s.activeIndex > s.previousIndex) {\n                        s.emit('onSlideNextEnd', s);\n                    }\n                    else {\n                        s.emit('onSlidePrevEnd', s);\n                    }\n                }\n            }\n            if (s.params.history && s.history) {\n                s.history.setHistory(s.params.history, s.activeIndex);\n            }\n            if (s.params.hashnav && s.hashnav) {\n                s.hashnav.setHash();\n            }\n        \n        };\n        s.slideNext = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex + s.params.slidesPerGroup, speed, runCallbacks, internal);\n        };\n        s._slideNext = function (speed) {\n            return s.slideNext(true, speed, true);\n        };\n        s.slidePrev = function (runCallbacks, speed, internal) {\n            if (s.params.loop) {\n                if (s.animating) return false;\n                s.fixLoop();\n                var clientLeft = s.container[0].clientLeft;\n                return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n            }\n            else return s.slideTo(s.activeIndex - 1, speed, runCallbacks, internal);\n        };\n        s._slidePrev = function (speed) {\n            return s.slidePrev(true, speed, true);\n        };\n        s.slideReset = function (runCallbacks, speed, internal) {\n            return s.slideTo(s.activeIndex, speed, runCallbacks);\n        };\n        \n        s.disableTouchControl = function () {\n            s.params.onlyExternal = true;\n            return true;\n        };\n        s.enableTouchControl = function () {\n            s.params.onlyExternal = false;\n            return true;\n        };\n        \n        /*=========================\n          Translate/transition helpers\n          ===========================*/\n        s.setWrapperTransition = function (duration, byController) {\n            s.wrapper.transition(duration);\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTransition(duration);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTransition(duration);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTransition(duration);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTransition(duration, byController);\n            }\n            s.emit('onSetTransition', s, duration);\n        };\n        s.setWrapperTranslate = function (translate, updateActiveIndex, byController) {\n            var x = 0, y = 0, z = 0;\n            if (s.isHorizontal()) {\n                x = s.rtl ? -translate : translate;\n            }\n            else {\n                y = translate;\n            }\n        \n            if (s.params.roundLengths) {\n                x = round(x);\n                y = round(y);\n            }\n        \n            if (!s.params.virtualTranslate) {\n                if (s.support.transforms3d) s.wrapper.transform('translate3d(' + x + 'px, ' + y + 'px, ' + z + 'px)');\n                else s.wrapper.transform('translate(' + x + 'px, ' + y + 'px)');\n            }\n        \n            s.translate = s.isHorizontal() ? x : y;\n        \n            // Check if we need to update progress\n            var progress;\n            var translatesDiff = s.maxTranslate() - s.minTranslate();\n            if (translatesDiff === 0) {\n                progress = 0;\n            }\n            else {\n                progress = (translate - s.minTranslate()) / (translatesDiff);\n            }\n            if (progress !== s.progress) {\n                s.updateProgress(translate);\n            }\n        \n            if (updateActiveIndex) s.updateActiveIndex();\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                s.effects[s.params.effect].setTranslate(s.translate);\n            }\n            if (s.params.parallax && s.parallax) {\n                s.parallax.setTranslate(s.translate);\n            }\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.setTranslate(s.translate);\n            }\n            if (s.params.control && s.controller) {\n                s.controller.setTranslate(s.translate, byController);\n            }\n            s.emit('onSetTranslate', s, s.translate);\n        };\n        \n        s.getTranslate = function (el, axis) {\n            var matrix, curTransform, curStyle, transformMatrix;\n        \n            // automatic axis detection\n            if (typeof axis === 'undefined') {\n                axis = 'x';\n            }\n        \n            if (s.params.virtualTranslate) {\n                return s.rtl ? -s.translate : s.translate;\n            }\n        \n            curStyle = window.getComputedStyle(el, null);\n            if (window.WebKitCSSMatrix) {\n                curTransform = curStyle.transform || curStyle.webkitTransform;\n                if (curTransform.split(',').length > 6) {\n                    curTransform = curTransform.split(', ').map(function(a){\n                        return a.replace(',','.');\n                    }).join(', ');\n                }\n                // Some old versions of Webkit choke when 'none' is passed; pass\n                // empty string instead in this case\n                transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n            }\n            else {\n                transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform  || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n                matrix = transformMatrix.toString().split(',');\n            }\n        \n            if (axis === 'x') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m41;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[12]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[4]);\n            }\n            if (axis === 'y') {\n                //Latest Chrome and webkits Fix\n                if (window.WebKitCSSMatrix)\n                    curTransform = transformMatrix.m42;\n                //Crazy IE10 Matrix\n                else if (matrix.length === 16)\n                    curTransform = parseFloat(matrix[13]);\n                //Normal Browsers\n                else\n                    curTransform = parseFloat(matrix[5]);\n            }\n            if (s.rtl && curTransform) curTransform = -curTransform;\n            return curTransform || 0;\n        };\n        s.getWrapperTranslate = function (axis) {\n            if (typeof axis === 'undefined') {\n                axis = s.isHorizontal() ? 'x' : 'y';\n            }\n            return s.getTranslate(s.wrapper[0], axis);\n        };\n        \n        /*=========================\n          Observer\n          ===========================*/\n        s.observers = [];\n        function initObserver(target, options) {\n            options = options || {};\n            // create an observer instance\n            var ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n            var observer = new ObserverFunc(function (mutations) {\n                mutations.forEach(function (mutation) {\n                    s.onResize(true);\n                    s.emit('onObserverUpdate', s, mutation);\n                });\n            });\n        \n            observer.observe(target, {\n                attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n                childList: typeof options.childList === 'undefined' ? true : options.childList,\n                characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n            });\n        \n            s.observers.push(observer);\n        }\n        s.initObservers = function () {\n            if (s.params.observeParents) {\n                var containerParents = s.container.parents();\n                for (var i = 0; i < containerParents.length; i++) {\n                    initObserver(containerParents[i]);\n                }\n            }\n        \n            // Observe container\n            initObserver(s.container[0], {childList: false});\n        \n            // Observe wrapper\n            initObserver(s.wrapper[0], {attributes: false});\n        };\n        s.disconnectObservers = function () {\n            for (var i = 0; i < s.observers.length; i++) {\n                s.observers[i].disconnect();\n            }\n            s.observers = [];\n        };\n        /*=========================\n          Loop\n          ===========================*/\n        // Create looped slides\n        s.createLoop = function () {\n            // Remove duplicated slides\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n        \n            var slides = s.wrapper.children('.' + s.params.slideClass);\n        \n            if(s.params.slidesPerView === 'auto' && !s.params.loopedSlides) s.params.loopedSlides = slides.length;\n        \n            s.loopedSlides = parseInt(s.params.loopedSlides || s.params.slidesPerView, 10);\n            s.loopedSlides = s.loopedSlides + s.params.loopAdditionalSlides;\n            if (s.loopedSlides > slides.length) {\n                s.loopedSlides = slides.length;\n            }\n        \n            var prependSlides = [], appendSlides = [], i;\n            slides.each(function (index, el) {\n                var slide = $(this);\n                if (index < s.loopedSlides) appendSlides.push(el);\n                if (index < slides.length && index >= slides.length - s.loopedSlides) prependSlides.push(el);\n                slide.attr('data-swiper-slide-index', index);\n            });\n            for (i = 0; i < appendSlides.length; i++) {\n                s.wrapper.append($(appendSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n            for (i = prependSlides.length - 1; i >= 0; i--) {\n                s.wrapper.prepend($(prependSlides[i].cloneNode(true)).addClass(s.params.slideDuplicateClass));\n            }\n        };\n        s.destroyLoop = function () {\n            s.wrapper.children('.' + s.params.slideClass + '.' + s.params.slideDuplicateClass).remove();\n            s.slides.removeAttr('data-swiper-slide-index');\n        };\n        s.reLoop = function (updatePosition) {\n            var oldIndex = s.activeIndex - s.loopedSlides;\n            s.destroyLoop();\n            s.createLoop();\n            s.updateSlidesSize();\n            if (updatePosition) {\n                s.slideTo(oldIndex + s.loopedSlides, 0, false);\n            }\n        \n        };\n        s.fixLoop = function () {\n            var newIndex;\n            //Fix For Negative Oversliding\n            if (s.activeIndex < s.loopedSlides) {\n                newIndex = s.slides.length - s.loopedSlides * 3 + s.activeIndex;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n            //Fix For Positive Oversliding\n            else if ((s.params.slidesPerView === 'auto' && s.activeIndex >= s.loopedSlides * 2) || (s.activeIndex > s.slides.length - s.params.slidesPerView * 2)) {\n                newIndex = -s.slides.length + s.activeIndex + s.loopedSlides;\n                newIndex = newIndex + s.loopedSlides;\n                s.slideTo(newIndex, 0, false, true);\n            }\n        };\n        /*=========================\n          Append/Prepend/Remove Slides\n          ===========================*/\n        s.appendSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.append(slides[i]);\n                }\n            }\n            else {\n                s.wrapper.append(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n        };\n        s.prependSlide = function (slides) {\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            var newActiveIndex = s.activeIndex + 1;\n            if (typeof slides === 'object' && slides.length) {\n                for (var i = 0; i < slides.length; i++) {\n                    if (slides[i]) s.wrapper.prepend(slides[i]);\n                }\n                newActiveIndex = s.activeIndex + slides.length;\n            }\n            else {\n                s.wrapper.prepend(slides);\n            }\n            if (s.params.loop) {\n                s.createLoop();\n            }\n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            s.slideTo(newActiveIndex, 0, false);\n        };\n        s.removeSlide = function (slidesIndexes) {\n            if (s.params.loop) {\n                s.destroyLoop();\n                s.slides = s.wrapper.children('.' + s.params.slideClass);\n            }\n            var newActiveIndex = s.activeIndex,\n                indexToRemove;\n            if (typeof slidesIndexes === 'object' && slidesIndexes.length) {\n                for (var i = 0; i < slidesIndexes.length; i++) {\n                    indexToRemove = slidesIndexes[i];\n                    if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                    if (indexToRemove < newActiveIndex) newActiveIndex--;\n                }\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n            else {\n                indexToRemove = slidesIndexes;\n                if (s.slides[indexToRemove]) s.slides.eq(indexToRemove).remove();\n                if (indexToRemove < newActiveIndex) newActiveIndex--;\n                newActiveIndex = Math.max(newActiveIndex, 0);\n            }\n        \n            if (s.params.loop) {\n                s.createLoop();\n            }\n        \n            if (!(s.params.observer && s.support.observer)) {\n                s.update(true);\n            }\n            if (s.params.loop) {\n                s.slideTo(newActiveIndex + s.loopedSlides, 0, false);\n            }\n            else {\n                s.slideTo(newActiveIndex, 0, false);\n            }\n        \n        };\n        s.removeAllSlides = function () {\n            var slidesIndexes = [];\n            for (var i = 0; i < s.slides.length; i++) {\n                slidesIndexes.push(i);\n            }\n            s.removeSlide(slidesIndexes);\n        };\n        \n\n        /*=========================\n          Effects\n          ===========================*/\n        s.effects = {\n            fade: {\n                setTranslate: function () {\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var offset = slide[0].swiperSlideOffset;\n                        var tx = -offset;\n                        if (!s.params.virtualTranslate) tx = tx - s.translate;\n                        var ty = 0;\n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n                        var slideOpacity = s.params.fade.crossFade ?\n                                Math.max(1 - Math.abs(slide[0].progress), 0) :\n                                1 + Math.min(Math.max(slide[0].progress, -1), 0);\n                        slide\n                            .css({\n                                opacity: slideOpacity\n                            })\n                            .transform('translate3d(' + tx + 'px, ' + ty + 'px, 0px)');\n        \n                    }\n        \n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration);\n                    if (s.params.virtualTranslate && duration !== 0) {\n                        var eventTriggered = false;\n                        s.slides.transitionEnd(function () {\n                            if (eventTriggered) return;\n                            if (!s) return;\n                            eventTriggered = true;\n                            s.animating = false;\n                            var triggerEvents = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'];\n                            for (var i = 0; i < triggerEvents.length; i++) {\n                                s.wrapper.trigger(triggerEvents[i]);\n                            }\n                        });\n                    }\n                }\n            },\n            flip: {\n                setTranslate: function () {\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var progress = slide[0].progress;\n                        if (s.params.flip.limitRotation) {\n                            progress = Math.max(Math.min(slide[0].progress, 1), -1);\n                        }\n                        var offset = slide[0].swiperSlideOffset;\n                        var rotate = -180 * progress,\n                            rotateY = rotate,\n                            rotateX = 0,\n                            tx = -offset,\n                            ty = 0;\n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                            rotateX = -rotateY;\n                            rotateY = 0;\n                        }\n                        else if (s.rtl) {\n                            rotateY = -rotateY;\n                        }\n        \n                        slide[0].style.zIndex = -Math.abs(Math.round(progress)) + s.slides.length;\n        \n                        if (s.params.flip.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n                        }\n        \n                        slide\n                            .transform('translate3d(' + tx + 'px, ' + ty + 'px, 0px) rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)');\n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                    if (s.params.virtualTranslate && duration !== 0) {\n                        var eventTriggered = false;\n                        s.slides.eq(s.activeIndex).transitionEnd(function () {\n                            if (eventTriggered) return;\n                            if (!s) return;\n                            if (!$(this).hasClass(s.params.slideActiveClass)) return;\n                            eventTriggered = true;\n                            s.animating = false;\n                            var triggerEvents = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'];\n                            for (var i = 0; i < triggerEvents.length; i++) {\n                                s.wrapper.trigger(triggerEvents[i]);\n                            }\n                        });\n                    }\n                }\n            },\n            cube: {\n                setTranslate: function () {\n                    var wrapperRotate = 0, cubeShadow;\n                    if (s.params.cube.shadow) {\n                        if (s.isHorizontal()) {\n                            cubeShadow = s.wrapper.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.wrapper.append(cubeShadow);\n                            }\n                            cubeShadow.css({height: s.width + 'px'});\n                        }\n                        else {\n                            cubeShadow = s.container.find('.swiper-cube-shadow');\n                            if (cubeShadow.length === 0) {\n                                cubeShadow = $('<div class=\"swiper-cube-shadow\"></div>');\n                                s.container.append(cubeShadow);\n                            }\n                        }\n                    }\n                    for (var i = 0; i < s.slides.length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideAngle = i * 90;\n                        var round = Math.floor(slideAngle / 360);\n                        if (s.rtl) {\n                            slideAngle = -slideAngle;\n                            round = Math.floor(-slideAngle / 360);\n                        }\n                        var progress = Math.max(Math.min(slide[0].progress, 1), -1);\n                        var tx = 0, ty = 0, tz = 0;\n                        if (i % 4 === 0) {\n                            tx = - round * 4 * s.size;\n                            tz = 0;\n                        }\n                        else if ((i - 1) % 4 === 0) {\n                            tx = 0;\n                            tz = - round * 4 * s.size;\n                        }\n                        else if ((i - 2) % 4 === 0) {\n                            tx = s.size + round * 4 * s.size;\n                            tz = s.size;\n                        }\n                        else if ((i - 3) % 4 === 0) {\n                            tx = - s.size;\n                            tz = 3 * s.size + s.size * 4 * round;\n                        }\n                        if (s.rtl) {\n                            tx = -tx;\n                        }\n        \n                        if (!s.isHorizontal()) {\n                            ty = tx;\n                            tx = 0;\n                        }\n        \n                        var transform = 'rotateX(' + (s.isHorizontal() ? 0 : -slideAngle) + 'deg) rotateY(' + (s.isHorizontal() ? slideAngle : 0) + 'deg) translate3d(' + tx + 'px, ' + ty + 'px, ' + tz + 'px)';\n                        if (progress <= 1 && progress > -1) {\n                            wrapperRotate = i * 90 + progress * 90;\n                            if (s.rtl) wrapperRotate = -i * 90 - progress * 90;\n                        }\n                        slide.transform(transform);\n                        if (s.params.cube.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = Math.max(-progress, 0);\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = Math.max(progress, 0);\n                        }\n                    }\n                    s.wrapper.css({\n                        '-webkit-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-moz-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        '-ms-transform-origin': '50% 50% -' + (s.size / 2) + 'px',\n                        'transform-origin': '50% 50% -' + (s.size / 2) + 'px'\n                    });\n        \n                    if (s.params.cube.shadow) {\n                        if (s.isHorizontal()) {\n                            cubeShadow.transform('translate3d(0px, ' + (s.width / 2 + s.params.cube.shadowOffset) + 'px, ' + (-s.width / 2) + 'px) rotateX(90deg) rotateZ(0deg) scale(' + (s.params.cube.shadowScale) + ')');\n                        }\n                        else {\n                            var shadowAngle = Math.abs(wrapperRotate) - Math.floor(Math.abs(wrapperRotate) / 90) * 90;\n                            var multiplier = 1.5 - (Math.sin(shadowAngle * 2 * Math.PI / 360) / 2 + Math.cos(shadowAngle * 2 * Math.PI / 360) / 2);\n                            var scale1 = s.params.cube.shadowScale,\n                                scale2 = s.params.cube.shadowScale / multiplier,\n                                offset = s.params.cube.shadowOffset;\n                            cubeShadow.transform('scale3d(' + scale1 + ', 1, ' + scale2 + ') translate3d(0px, ' + (s.height / 2 + offset) + 'px, ' + (-s.height / 2 / scale2) + 'px) rotateX(-90deg)');\n                        }\n                    }\n                    var zFactor = (s.isSafari || s.isUiWebView) ? (-s.size / 2) : 0;\n                    s.wrapper.transform('translate3d(0px,0,' + zFactor + 'px) rotateX(' + (s.isHorizontal() ? 0 : wrapperRotate) + 'deg) rotateY(' + (s.isHorizontal() ? -wrapperRotate : 0) + 'deg)');\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                    if (s.params.cube.shadow && !s.isHorizontal()) {\n                        s.container.find('.swiper-cube-shadow').transition(duration);\n                    }\n                }\n            },\n            coverflow: {\n                setTranslate: function () {\n                    var transform = s.translate;\n                    var center = s.isHorizontal() ? -transform + s.width / 2 : -transform + s.height / 2;\n                    var rotate = s.isHorizontal() ? s.params.coverflow.rotate: -s.params.coverflow.rotate;\n                    var translate = s.params.coverflow.depth;\n                    //Each slide offset from center\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideSize = s.slidesSizesGrid[i];\n                        var slideOffset = slide[0].swiperSlideOffset;\n                        var offsetMultiplier = (center - slideOffset - slideSize / 2) / slideSize * s.params.coverflow.modifier;\n        \n                        var rotateY = s.isHorizontal() ? rotate * offsetMultiplier : 0;\n                        var rotateX = s.isHorizontal() ? 0 : rotate * offsetMultiplier;\n                        // var rotateZ = 0\n                        var translateZ = -translate * Math.abs(offsetMultiplier);\n        \n                        var translateY = s.isHorizontal() ? 0 : s.params.coverflow.stretch * (offsetMultiplier);\n                        var translateX = s.isHorizontal() ? s.params.coverflow.stretch * (offsetMultiplier) : 0;\n        \n                        //Fix for ultra small values\n                        if (Math.abs(translateX) < 0.001) translateX = 0;\n                        if (Math.abs(translateY) < 0.001) translateY = 0;\n                        if (Math.abs(translateZ) < 0.001) translateZ = 0;\n                        if (Math.abs(rotateY) < 0.001) rotateY = 0;\n                        if (Math.abs(rotateX) < 0.001) rotateX = 0;\n        \n                        var slideTransform = 'translate3d(' + translateX + 'px,' + translateY + 'px,' + translateZ + 'px)  rotateX(' + rotateX + 'deg) rotateY(' + rotateY + 'deg)';\n        \n                        slide.transform(slideTransform);\n                        slide[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n                        if (s.params.coverflow.slideShadows) {\n                            //Set shadows\n                            var shadowBefore = s.isHorizontal() ? slide.find('.swiper-slide-shadow-left') : slide.find('.swiper-slide-shadow-top');\n                            var shadowAfter = s.isHorizontal() ? slide.find('.swiper-slide-shadow-right') : slide.find('.swiper-slide-shadow-bottom');\n                            if (shadowBefore.length === 0) {\n                                shadowBefore = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'left' : 'top') + '\"></div>');\n                                slide.append(shadowBefore);\n                            }\n                            if (shadowAfter.length === 0) {\n                                shadowAfter = $('<div class=\"swiper-slide-shadow-' + (s.isHorizontal() ? 'right' : 'bottom') + '\"></div>');\n                                slide.append(shadowAfter);\n                            }\n                            if (shadowBefore.length) shadowBefore[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n                            if (shadowAfter.length) shadowAfter[0].style.opacity = (-offsetMultiplier) > 0 ? -offsetMultiplier : 0;\n                        }\n                    }\n        \n                    //Set correct perspective for IE10\n                    if (s.browser.ie) {\n                        var ws = s.wrapper[0].style;\n                        ws.perspectiveOrigin = center + 'px 50%';\n                    }\n                },\n                setTransition: function (duration) {\n                    s.slides.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n                }\n            }\n        };\n\n        /*=========================\n          Images Lazy Loading\n          ===========================*/\n        s.lazy = {\n            initialImageLoaded: false,\n            loadImageInSlide: function (index, loadInDuplicate) {\n                if (typeof index === 'undefined') return;\n                if (typeof loadInDuplicate === 'undefined') loadInDuplicate = true;\n                if (s.slides.length === 0) return;\n        \n                var slide = s.slides.eq(index);\n                var img = slide.find('.' + s.params.lazyLoadingClass + ':not(.' + s.params.lazyStatusLoadedClass + '):not(.' + s.params.lazyStatusLoadingClass + ')');\n                if (slide.hasClass(s.params.lazyLoadingClass) && !slide.hasClass(s.params.lazyStatusLoadedClass) && !slide.hasClass(s.params.lazyStatusLoadingClass)) {\n                    img = img.add(slide[0]);\n                }\n                if (img.length === 0) return;\n        \n                img.each(function () {\n                    var _img = $(this);\n                    _img.addClass(s.params.lazyStatusLoadingClass);\n                    var background = _img.attr('data-background');\n                    var src = _img.attr('data-src'),\n                        srcset = _img.attr('data-srcset'),\n                        sizes = _img.attr('data-sizes');\n                    s.loadImage(_img[0], (src || background), srcset, sizes, false, function () {\n                        if (background) {\n                            _img.css('background-image', 'url(\"' + background + '\")');\n                            _img.removeAttr('data-background');\n                        }\n                        else {\n                            if (srcset) {\n                                _img.attr('srcset', srcset);\n                                _img.removeAttr('data-srcset');\n                            }\n                            if (sizes) {\n                                _img.attr('sizes', sizes);\n                                _img.removeAttr('data-sizes');\n                            }\n                            if (src) {\n                                _img.attr('src', src);\n                                _img.removeAttr('data-src');\n                            }\n        \n                        }\n        \n                        _img.addClass(s.params.lazyStatusLoadedClass).removeClass(s.params.lazyStatusLoadingClass);\n                        slide.find('.' + s.params.lazyPreloaderClass + ', .' + s.params.preloaderClass).remove();\n                        if (s.params.loop && loadInDuplicate) {\n                            var slideOriginalIndex = slide.attr('data-swiper-slide-index');\n                            if (slide.hasClass(s.params.slideDuplicateClass)) {\n                                var originalSlide = s.wrapper.children('[data-swiper-slide-index=\"' + slideOriginalIndex + '\"]:not(.' + s.params.slideDuplicateClass + ')');\n                                s.lazy.loadImageInSlide(originalSlide.index(), false);\n                            }\n                            else {\n                                var duplicatedSlide = s.wrapper.children('.' + s.params.slideDuplicateClass + '[data-swiper-slide-index=\"' + slideOriginalIndex + '\"]');\n                                s.lazy.loadImageInSlide(duplicatedSlide.index(), false);\n                            }\n                        }\n                        s.emit('onLazyImageReady', s, slide[0], _img[0]);\n                    });\n        \n                    s.emit('onLazyImageLoad', s, slide[0], _img[0]);\n                });\n        \n            },\n            load: function () {\n                var i;\n                var slidesPerView = s.params.slidesPerView;\n                if (slidesPerView === 'auto') {\n                    slidesPerView = 0;\n                }\n                if (!s.lazy.initialImageLoaded) s.lazy.initialImageLoaded = true;\n                if (s.params.watchSlidesVisibility) {\n                    s.wrapper.children('.' + s.params.slideVisibleClass).each(function () {\n                        s.lazy.loadImageInSlide($(this).index());\n                    });\n                }\n                else {\n                    if (slidesPerView > 1) {\n                        for (i = s.activeIndex; i < s.activeIndex + slidesPerView ; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                    }\n                    else {\n                        s.lazy.loadImageInSlide(s.activeIndex);\n                    }\n                }\n                if (s.params.lazyLoadingInPrevNext) {\n                    if (slidesPerView > 1 || (s.params.lazyLoadingInPrevNextAmount && s.params.lazyLoadingInPrevNextAmount > 1)) {\n                        var amount = s.params.lazyLoadingInPrevNextAmount;\n                        var spv = slidesPerView;\n                        var maxIndex = Math.min(s.activeIndex + spv + Math.max(amount, spv), s.slides.length);\n                        var minIndex = Math.max(s.activeIndex - Math.max(spv, amount), 0);\n                        // Next Slides\n                        for (i = s.activeIndex + slidesPerView; i < maxIndex; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                        // Prev Slides\n                        for (i = minIndex; i < s.activeIndex ; i++) {\n                            if (s.slides[i]) s.lazy.loadImageInSlide(i);\n                        }\n                    }\n                    else {\n                        var nextSlide = s.wrapper.children('.' + s.params.slideNextClass);\n                        if (nextSlide.length > 0) s.lazy.loadImageInSlide(nextSlide.index());\n        \n                        var prevSlide = s.wrapper.children('.' + s.params.slidePrevClass);\n                        if (prevSlide.length > 0) s.lazy.loadImageInSlide(prevSlide.index());\n                    }\n                }\n            },\n            onTransitionStart: function () {\n                if (s.params.lazyLoading) {\n                    if (s.params.lazyLoadingOnTransitionStart || (!s.params.lazyLoadingOnTransitionStart && !s.lazy.initialImageLoaded)) {\n                        s.lazy.load();\n                    }\n                }\n            },\n            onTransitionEnd: function () {\n                if (s.params.lazyLoading && !s.params.lazyLoadingOnTransitionStart) {\n                    s.lazy.load();\n                }\n            }\n        };\n        \n\n        /*=========================\n          Scrollbar\n          ===========================*/\n        s.scrollbar = {\n            isTouched: false,\n            setDragPosition: function (e) {\n                var sb = s.scrollbar;\n                var x = 0, y = 0;\n                var translate;\n                var pointerPosition = s.isHorizontal() ?\n                    ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageX : e.pageX || e.clientX) :\n                    ((e.type === 'touchstart' || e.type === 'touchmove') ? e.targetTouches[0].pageY : e.pageY || e.clientY) ;\n                var position = (pointerPosition) - sb.track.offset()[s.isHorizontal() ? 'left' : 'top'] - sb.dragSize / 2;\n                var positionMin = -s.minTranslate() * sb.moveDivider;\n                var positionMax = -s.maxTranslate() * sb.moveDivider;\n                if (position < positionMin) {\n                    position = positionMin;\n                }\n                else if (position > positionMax) {\n                    position = positionMax;\n                }\n                position = -position / sb.moveDivider;\n                s.updateProgress(position);\n                s.setWrapperTranslate(position, true);\n            },\n            dragStart: function (e) {\n                var sb = s.scrollbar;\n                sb.isTouched = true;\n                e.preventDefault();\n                e.stopPropagation();\n        \n                sb.setDragPosition(e);\n                clearTimeout(sb.dragTimeout);\n        \n                sb.track.transition(0);\n                if (s.params.scrollbarHide) {\n                    sb.track.css('opacity', 1);\n                }\n                s.wrapper.transition(100);\n                sb.drag.transition(100);\n                s.emit('onScrollbarDragStart', s);\n            },\n            dragMove: function (e) {\n                var sb = s.scrollbar;\n                if (!sb.isTouched) return;\n                if (e.preventDefault) e.preventDefault();\n                else e.returnValue = false;\n                sb.setDragPosition(e);\n                s.wrapper.transition(0);\n                sb.track.transition(0);\n                sb.drag.transition(0);\n                s.emit('onScrollbarDragMove', s);\n            },\n            dragEnd: function (e) {\n                var sb = s.scrollbar;\n                if (!sb.isTouched) return;\n                sb.isTouched = false;\n                if (s.params.scrollbarHide) {\n                    clearTimeout(sb.dragTimeout);\n                    sb.dragTimeout = setTimeout(function () {\n                        sb.track.css('opacity', 0);\n                        sb.track.transition(400);\n                    }, 1000);\n        \n                }\n                s.emit('onScrollbarDragEnd', s);\n                if (s.params.scrollbarSnapOnRelease) {\n                    s.slideReset();\n                }\n            },\n            draggableEvents: (function () {\n                if ((s.params.simulateTouch === false && !s.support.touch)) return s.touchEventsDesktop;\n                else return s.touchEvents;\n            })(),\n            enableDraggable: function () {\n                var sb = s.scrollbar;\n                var target = s.support.touch ? sb.track : document;\n                $(sb.track).on(sb.draggableEvents.start, sb.dragStart);\n                $(target).on(sb.draggableEvents.move, sb.dragMove);\n                $(target).on(sb.draggableEvents.end, sb.dragEnd);\n            },\n            disableDraggable: function () {\n                var sb = s.scrollbar;\n                var target = s.support.touch ? sb.track : document;\n                $(sb.track).off(s.draggableEvents.start, sb.dragStart);\n                $(target).off(s.draggableEvents.move, sb.dragMove);\n                $(target).off(s.draggableEvents.end, sb.dragEnd);\n            },\n            set: function () {\n                if (!s.params.scrollbar) return;\n                var sb = s.scrollbar;\n                sb.track = $(s.params.scrollbar);\n                if (s.params.uniqueNavElements && typeof s.params.scrollbar === 'string' && sb.track.length > 1 && s.container.find(s.params.scrollbar).length === 1) {\n                    sb.track = s.container.find(s.params.scrollbar);\n                }\n                sb.drag = sb.track.find('.swiper-scrollbar-drag');\n                if (sb.drag.length === 0) {\n                    sb.drag = $('<div class=\"swiper-scrollbar-drag\"></div>');\n                    sb.track.append(sb.drag);\n                }\n                sb.drag[0].style.width = '';\n                sb.drag[0].style.height = '';\n                sb.trackSize = s.isHorizontal() ? sb.track[0].offsetWidth : sb.track[0].offsetHeight;\n        \n                sb.divider = s.size / s.virtualSize;\n                sb.moveDivider = sb.divider * (sb.trackSize / s.size);\n                sb.dragSize = sb.trackSize * sb.divider;\n        \n                if (s.isHorizontal()) {\n                    sb.drag[0].style.width = sb.dragSize + 'px';\n                }\n                else {\n                    sb.drag[0].style.height = sb.dragSize + 'px';\n                }\n        \n                if (sb.divider >= 1) {\n                    sb.track[0].style.display = 'none';\n                }\n                else {\n                    sb.track[0].style.display = '';\n                }\n                if (s.params.scrollbarHide) {\n                    sb.track[0].style.opacity = 0;\n                }\n            },\n            setTranslate: function () {\n                if (!s.params.scrollbar) return;\n                var diff;\n                var sb = s.scrollbar;\n                var translate = s.translate || 0;\n                var newPos;\n        \n                var newSize = sb.dragSize;\n                newPos = (sb.trackSize - sb.dragSize) * s.progress;\n                if (s.rtl && s.isHorizontal()) {\n                    newPos = -newPos;\n                    if (newPos > 0) {\n                        newSize = sb.dragSize - newPos;\n                        newPos = 0;\n                    }\n                    else if (-newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize + newPos;\n                    }\n                }\n                else {\n                    if (newPos < 0) {\n                        newSize = sb.dragSize + newPos;\n                        newPos = 0;\n                    }\n                    else if (newPos + sb.dragSize > sb.trackSize) {\n                        newSize = sb.trackSize - newPos;\n                    }\n                }\n                if (s.isHorizontal()) {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(' + (newPos) + 'px, 0, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateX(' + (newPos) + 'px)');\n                    }\n                    sb.drag[0].style.width = newSize + 'px';\n                }\n                else {\n                    if (s.support.transforms3d) {\n                        sb.drag.transform('translate3d(0px, ' + (newPos) + 'px, 0)');\n                    }\n                    else {\n                        sb.drag.transform('translateY(' + (newPos) + 'px)');\n                    }\n                    sb.drag[0].style.height = newSize + 'px';\n                }\n                if (s.params.scrollbarHide) {\n                    clearTimeout(sb.timeout);\n                    sb.track[0].style.opacity = 1;\n                    sb.timeout = setTimeout(function () {\n                        sb.track[0].style.opacity = 0;\n                        sb.track.transition(400);\n                    }, 1000);\n                }\n            },\n            setTransition: function (duration) {\n                if (!s.params.scrollbar) return;\n                s.scrollbar.drag.transition(duration);\n            }\n        };\n\n        /*=========================\n          Controller\n          ===========================*/\n        s.controller = {\n            LinearSpline: function (x, y) {\n                this.x = x;\n                this.y = y;\n                this.lastIndex = x.length - 1;\n                // Given an x value (x2), return the expected y2 value:\n                // (x1,y1) is the known point before given value,\n                // (x3,y3) is the known point after given value.\n                var i1, i3;\n                var l = this.x.length;\n        \n                this.interpolate = function (x2) {\n                    if (!x2) return 0;\n        \n                    // Get the indexes of x1 and x3 (the array indexes before and after given x2):\n                    i3 = binarySearch(this.x, x2);\n                    i1 = i3 - 1;\n        \n                    // We have our indexes i1 & i3, so we can calculate already:\n                    // y2 := ((x2−x1) × (y3−y1)) ÷ (x3−x1) + y1\n                    return ((x2 - this.x[i1]) * (this.y[i3] - this.y[i1])) / (this.x[i3] - this.x[i1]) + this.y[i1];\n                };\n        \n                var binarySearch = (function() {\n                    var maxIndex, minIndex, guess;\n                    return function(array, val) {\n                        minIndex = -1;\n                        maxIndex = array.length;\n                        while (maxIndex - minIndex > 1)\n                            if (array[guess = maxIndex + minIndex >> 1] <= val) {\n                                minIndex = guess;\n                            } else {\n                                maxIndex = guess;\n                            }\n                        return maxIndex;\n                    };\n                })();\n            },\n            //xxx: for now i will just save one spline function to to\n            getInterpolateFunction: function(c){\n                if(!s.controller.spline) s.controller.spline = s.params.loop ?\n                    new s.controller.LinearSpline(s.slidesGrid, c.slidesGrid) :\n                    new s.controller.LinearSpline(s.snapGrid, c.snapGrid);\n            },\n            setTranslate: function (translate, byController) {\n               var controlled = s.params.control;\n               var multiplier, controlledTranslate;\n               function setControlledTranslate(c) {\n                    // this will create an Interpolate function based on the snapGrids\n                    // x is the Grid of the scrolled scroller and y will be the controlled scroller\n                    // it makes sense to create this only once and recall it for the interpolation\n                    // the function does a lot of value caching for performance\n                    translate = c.rtl && c.params.direction === 'horizontal' ? -s.translate : s.translate;\n                    if (s.params.controlBy === 'slide') {\n                        s.controller.getInterpolateFunction(c);\n                        // i am not sure why the values have to be multiplicated this way, tried to invert the snapGrid\n                        // but it did not work out\n                        controlledTranslate = -s.controller.spline.interpolate(-translate);\n                    }\n        \n                    if(!controlledTranslate || s.params.controlBy === 'container'){\n                        multiplier = (c.maxTranslate() - c.minTranslate()) / (s.maxTranslate() - s.minTranslate());\n                        controlledTranslate = (translate - s.minTranslate()) * multiplier + c.minTranslate();\n                    }\n        \n                    if (s.params.controlInverse) {\n                        controlledTranslate = c.maxTranslate() - controlledTranslate;\n                    }\n                    c.updateProgress(controlledTranslate);\n                    c.setWrapperTranslate(controlledTranslate, false, s);\n                    c.updateActiveIndex();\n               }\n               if (s.isArray(controlled)) {\n                   for (var i = 0; i < controlled.length; i++) {\n                       if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                           setControlledTranslate(controlled[i]);\n                       }\n                   }\n               }\n               else if (controlled instanceof Swiper && byController !== controlled) {\n        \n                   setControlledTranslate(controlled);\n               }\n            },\n            setTransition: function (duration, byController) {\n                var controlled = s.params.control;\n                var i;\n                function setControlledTransition(c) {\n                    c.setWrapperTransition(duration, s);\n                    if (duration !== 0) {\n                        c.onTransitionStart();\n                        c.wrapper.transitionEnd(function(){\n                            if (!controlled) return;\n                            if (c.params.loop && s.params.controlBy === 'slide') {\n                                c.fixLoop();\n                            }\n                            c.onTransitionEnd();\n        \n                        });\n                    }\n                }\n                if (s.isArray(controlled)) {\n                    for (i = 0; i < controlled.length; i++) {\n                        if (controlled[i] !== byController && controlled[i] instanceof Swiper) {\n                            setControlledTransition(controlled[i]);\n                        }\n                    }\n                }\n                else if (controlled instanceof Swiper && byController !== controlled) {\n                    setControlledTransition(controlled);\n                }\n            }\n        };\n\n        /*=========================\n          Hash Navigation\n          ===========================*/\n        s.hashnav = {\n            onHashCange: function (e, a) {\n                var newHash = document.location.hash.replace('#', '');\n                var activeSlideHash = s.slides.eq(s.activeIndex).attr('data-hash');\n                if (newHash !== activeSlideHash) {\n                    s.slideTo(s.wrapper.children('.' + s.params.slideClass + '[data-hash=\"' + (newHash) + '\"]').index());\n                }\n            },\n            attachEvents: function (detach) {\n                var action = detach ? 'off' : 'on';\n                $(window)[action]('hashchange', s.hashnav.onHashCange);\n            },\n            setHash: function () {\n                if (!s.hashnav.initialized || !s.params.hashnav) return;\n                if (s.params.replaceState && window.history && window.history.replaceState) {\n                    window.history.replaceState(null, null, ('#' + s.slides.eq(s.activeIndex).attr('data-hash') || ''));\n                } else {\n                    var slide = s.slides.eq(s.activeIndex);\n                    var hash = slide.attr('data-hash') || slide.attr('data-history');\n                    document.location.hash = hash || '';\n                }\n            },\n            init: function () {\n                if (!s.params.hashnav || s.params.history) return;\n                s.hashnav.initialized = true;\n                var hash = document.location.hash.replace('#', '');\n                if (!hash) return;\n                var speed = 0;\n                for (var i = 0, length = s.slides.length; i < length; i++) {\n                    var slide = s.slides.eq(i);\n                    var slideHash = slide.attr('data-hash') || slide.attr('data-history');\n                    if (slideHash === hash && !slide.hasClass(s.params.slideDuplicateClass)) {\n                        var index = slide.index();\n                        s.slideTo(index, speed, s.params.runCallbacksOnInit, true);\n                    }\n                }\n                if (s.params.hashnavWatchState) s.hashnav.attachEvents();\n            },\n            destroy: function () {\n                if (s.params.hashnavWatchState) s.hashnav.attachEvents(true);\n            }\n        };\n\n        /*=========================\n          History Api with fallback to Hashnav\n          ===========================*/\n        s.history = {\n            init: function () {\n                if (!s.params.history) return;\n                if (!window.history || !window.history.pushState) {\n                    s.params.history = false;\n                    s.params.hashnav = true;\n                    return;\n                }\n                s.history.initialized = true;\n                this.paths = this.getPathValues();\n                if (!this.paths.key && !this.paths.value) return;\n                this.scrollToSlide(0, this.paths.value, s.params.runCallbacksOnInit);\n                if (!s.params.replaceState) {\n                    window.addEventListener('popstate', this.setHistoryPopState);\n                }\n            },\n            setHistoryPopState: function() {\n                s.history.paths = s.history.getPathValues();\n                s.history.scrollToSlide(s.params.speed, s.history.paths.value, false);\n            },\n            getPathValues: function() {\n                var pathArray = window.location.pathname.slice(1).split('/');\n                var total = pathArray.length;\n                var key = pathArray[total - 2];\n                var value = pathArray[total - 1];\n                return { key: key, value: value };\n            },\n            setHistory: function (key, index) {\n                if (!s.history.initialized || !s.params.history) return;\n                var slide = s.slides.eq(index);\n                var value = this.slugify(slide.attr('data-history'));\n                if (!window.location.pathname.includes(key)) {\n                    value = key + '/' + value;\n                }\n                if (s.params.replaceState) {\n                    window.history.replaceState(null, null, value);\n                } else {\n                    window.history.pushState(null, null, value);\n                }\n            },\n            slugify: function(text) {\n                return text.toString().toLowerCase()\n                    .replace(/\\s+/g, '-')\n                    .replace(/[^\\w\\-]+/g, '')\n                    .replace(/\\-\\-+/g, '-')\n                    .replace(/^-+/, '')\n                    .replace(/-+$/, '');\n            },\n            scrollToSlide: function(speed, value, runCallbacks) {\n                if (value) {\n                    for (var i = 0, length = s.slides.length; i < length; i++) {\n                        var slide = s.slides.eq(i);\n                        var slideHistory = this.slugify(slide.attr('data-history'));\n                        if (slideHistory === value && !slide.hasClass(s.params.slideDuplicateClass)) {\n                            var index = slide.index();\n                            s.slideTo(index, speed, runCallbacks);\n                        }\n                    }\n                } else {\n                    s.slideTo(0, speed, runCallbacks);\n                }\n            }\n        };\n\n        /*=========================\n          Keyboard Control\n          ===========================*/\n        function handleKeyboard(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var kc = e.keyCode || e.charCode;\n            // Directions locks\n            if (!s.params.allowSwipeToNext && (s.isHorizontal() && kc === 39 || !s.isHorizontal() && kc === 40)) {\n                return false;\n            }\n            if (!s.params.allowSwipeToPrev && (s.isHorizontal() && kc === 37 || !s.isHorizontal() && kc === 38)) {\n                return false;\n            }\n            if (e.shiftKey || e.altKey || e.ctrlKey || e.metaKey) {\n                return;\n            }\n            if (document.activeElement && document.activeElement.nodeName && (document.activeElement.nodeName.toLowerCase() === 'input' || document.activeElement.nodeName.toLowerCase() === 'textarea')) {\n                return;\n            }\n            if (kc === 37 || kc === 39 || kc === 38 || kc === 40) {\n                var inView = false;\n                //Check that swiper should be inside of visible area of window\n                if (s.container.parents('.' + s.params.slideClass).length > 0 && s.container.parents('.' + s.params.slideActiveClass).length === 0) {\n                    return;\n                }\n                var windowScroll = {\n                    left: window.pageXOffset,\n                    top: window.pageYOffset\n                };\n                var windowWidth = window.innerWidth;\n                var windowHeight = window.innerHeight;\n                var swiperOffset = s.container.offset();\n                if (s.rtl) swiperOffset.left = swiperOffset.left - s.container[0].scrollLeft;\n                var swiperCoord = [\n                    [swiperOffset.left, swiperOffset.top],\n                    [swiperOffset.left + s.width, swiperOffset.top],\n                    [swiperOffset.left, swiperOffset.top + s.height],\n                    [swiperOffset.left + s.width, swiperOffset.top + s.height]\n                ];\n                for (var i = 0; i < swiperCoord.length; i++) {\n                    var point = swiperCoord[i];\n                    if (\n                        point[0] >= windowScroll.left && point[0] <= windowScroll.left + windowWidth &&\n                        point[1] >= windowScroll.top && point[1] <= windowScroll.top + windowHeight\n                    ) {\n                        inView = true;\n                    }\n        \n                }\n                if (!inView) return;\n            }\n            if (s.isHorizontal()) {\n                if (kc === 37 || kc === 39) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if ((kc === 39 && !s.rtl) || (kc === 37 && s.rtl)) s.slideNext();\n                if ((kc === 37 && !s.rtl) || (kc === 39 && s.rtl)) s.slidePrev();\n            }\n            else {\n                if (kc === 38 || kc === 40) {\n                    if (e.preventDefault) e.preventDefault();\n                    else e.returnValue = false;\n                }\n                if (kc === 40) s.slideNext();\n                if (kc === 38) s.slidePrev();\n            }\n        }\n        s.disableKeyboardControl = function () {\n            s.params.keyboardControl = false;\n            $(document).off('keydown', handleKeyboard);\n        };\n        s.enableKeyboardControl = function () {\n            s.params.keyboardControl = true;\n            $(document).on('keydown', handleKeyboard);\n        };\n        \n\n        /*=========================\n          Mousewheel Control\n          ===========================*/\n        s.mousewheel = {\n            event: false,\n            lastScrollTime: (new window.Date()).getTime()\n        };\n        if (s.params.mousewheelControl) {\n            /**\n             * The best combination if you prefer spinX + spinY normalization.  It favors\n             * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n             * 'wheel' event, making spin speed determination impossible.\n             */\n            s.mousewheel.event = (navigator.userAgent.indexOf('firefox') > -1) ?\n                'DOMMouseScroll' :\n                isEventSupported() ?\n                    'wheel' : 'mousewheel';\n        }\n        \n        function isEventSupported() {\n            var eventName = 'onwheel';\n            var isSupported = eventName in document;\n        \n            if (!isSupported) {\n                var element = document.createElement('div');\n                element.setAttribute(eventName, 'return;');\n                isSupported = typeof element[eventName] === 'function';\n            }\n        \n            if (!isSupported &&\n                document.implementation &&\n                document.implementation.hasFeature &&\n                    // always returns true in newer browsers as per the standard.\n                    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n                document.implementation.hasFeature('', '') !== true ) {\n                // This is the only way to test support for the `wheel` event in IE9+.\n                isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n            }\n        \n            return isSupported;\n        }\n        \n        function handleMousewheel(e) {\n            if (e.originalEvent) e = e.originalEvent; //jquery fix\n            var delta = 0;\n            var rtlFactor = s.rtl ? -1 : 1;\n        \n            var data = normalizeWheel( e );\n        \n            if (s.params.mousewheelForceToAxis) {\n                if (s.isHorizontal()) {\n                    if (Math.abs(data.pixelX) > Math.abs(data.pixelY)) delta = data.pixelX * rtlFactor;\n                    else return;\n                }\n                else {\n                    if (Math.abs(data.pixelY) > Math.abs(data.pixelX)) delta = data.pixelY;\n                    else return;\n                }\n            }\n            else {\n                delta = Math.abs(data.pixelX) > Math.abs(data.pixelY) ? - data.pixelX * rtlFactor : - data.pixelY;\n            }\n        \n            if (delta === 0) return;\n        \n            if (s.params.mousewheelInvert) delta = -delta;\n        \n            if (!s.params.freeMode) {\n                if ((new window.Date()).getTime() - s.mousewheel.lastScrollTime > 60) {\n                    if (delta < 0) {\n                        if ((!s.isEnd || s.params.loop) && !s.animating) {\n                            s.slideNext();\n                            s.emit('onScroll', s, e);\n                        }\n                        else if (s.params.mousewheelReleaseOnEdges) return true;\n                    }\n                    else {\n                        if ((!s.isBeginning || s.params.loop) && !s.animating) {\n                            s.slidePrev();\n                            s.emit('onScroll', s, e);\n                        }\n                        else if (s.params.mousewheelReleaseOnEdges) return true;\n                    }\n                }\n                s.mousewheel.lastScrollTime = (new window.Date()).getTime();\n        \n            }\n            else {\n                //Freemode or scrollContainer:\n                var position = s.getWrapperTranslate() + delta * s.params.mousewheelSensitivity;\n                var wasBeginning = s.isBeginning,\n                    wasEnd = s.isEnd;\n        \n                if (position >= s.minTranslate()) position = s.minTranslate();\n                if (position <= s.maxTranslate()) position = s.maxTranslate();\n        \n                s.setWrapperTransition(0);\n                s.setWrapperTranslate(position);\n                s.updateProgress();\n                s.updateActiveIndex();\n        \n                if (!wasBeginning && s.isBeginning || !wasEnd && s.isEnd) {\n                    s.updateClasses();\n                }\n        \n                if (s.params.freeModeSticky) {\n                    clearTimeout(s.mousewheel.timeout);\n                    s.mousewheel.timeout = setTimeout(function () {\n                        s.slideReset();\n                    }, 300);\n                }\n                else {\n                    if (s.params.lazyLoading && s.lazy) {\n                        s.lazy.load();\n                    }\n                }\n                // Emit event\n                s.emit('onScroll', s, e);\n        \n                // Stop autoplay\n                if (s.params.autoplay && s.params.autoplayDisableOnInteraction) s.stopAutoplay();\n        \n                // Return page scroll on edge positions\n                if (position === 0 || position === s.maxTranslate()) return;\n            }\n        \n            if (e.preventDefault) e.preventDefault();\n            else e.returnValue = false;\n            return false;\n        }\n        s.disableMousewheelControl = function () {\n            if (!s.mousewheel.event) return false;\n            var target = s.container;\n            if (s.params.mousewheelEventsTarged !== 'container') {\n                target = $(s.params.mousewheelEventsTarged);\n            }\n            target.off(s.mousewheel.event, handleMousewheel);\n            return true;\n        };\n        \n        s.enableMousewheelControl = function () {\n            if (!s.mousewheel.event) return false;\n            var target = s.container;\n            if (s.params.mousewheelEventsTarged !== 'container') {\n                target = $(s.params.mousewheelEventsTarged);\n            }\n            target.on(s.mousewheel.event, handleMousewheel);\n            return true;\n        };\n        \n        /**\n         * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n         * complicated, thus this doc is long and (hopefully) detailed enough to answer\n         * your questions.\n         *\n         * If you need to react to the mouse wheel in a predictable way, this code is\n         * like your bestest friend. * hugs *\n         *\n         * As of today, there are 4 DOM event types you can listen to:\n         *\n         *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n         *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n         *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n         *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n         *\n         * So what to do?  The is the best:\n         *\n         *   normalizeWheel.getEventType();\n         *\n         * In your event callback, use this code to get sane interpretation of the\n         * deltas.  This code will return an object with properties:\n         *\n         *   spinX   -- normalized spin speed (use for zoom) - x plane\n         *   spinY   -- \" - y plane\n         *   pixelX  -- normalized distance (to pixels) - x plane\n         *   pixelY  -- \" - y plane\n         *\n         * Wheel values are provided by the browser assuming you are using the wheel to\n         * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n         * significantly on different platforms and browsers, forgetting that you can\n         * scroll at different speeds.  Some devices (like trackpads) emit more events\n         * at smaller increments with fine granularity, and some emit massive jumps with\n         * linear speed or acceleration.\n         *\n         * This code does its best to normalize the deltas for you:\n         *\n         *   - spin is trying to normalize how far the wheel was spun (or trackpad\n         *     dragged).  This is super useful for zoom support where you want to\n         *     throw away the chunky scroll steps on the PC and make those equal to\n         *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n         *     resolve a single slow step on a wheel to 1.\n         *\n         *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n         *     get the crazy differences between browsers, but at least it'll be in\n         *     pixels!\n         *\n         *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n         *     should translate to positive value zooming IN, negative zooming OUT.\n         *     This matches the newer 'wheel' event.\n         *\n         * Why are there spinX, spinY (or pixels)?\n         *\n         *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n         *     with a mouse.  It results in side-scrolling in the browser by default.\n         *\n         *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n         *\n         *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n         *     probably is by browsers in conjunction with fancy 3D controllers .. but\n         *     you know.\n         *\n         * Implementation info:\n         *\n         * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n         * average mouse:\n         *\n         *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n         *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n         *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n         *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n         *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n         *\n         * On the trackpad:\n         *\n         *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n         *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n         *\n         * On other/older browsers.. it's more complicated as there can be multiple and\n         * also missing delta values.\n         *\n         * The 'wheel' event is more standard:\n         *\n         * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n         *\n         * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n         * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n         * backward compatibility with older events.  Those other values help us\n         * better normalize spin speed.  Example of what the browsers provide:\n         *\n         *                          | event.wheelDelta | event.detail\n         *        ------------------+------------------+--------------\n         *          Safari v5/OS X  |       -120       |       0\n         *          Safari v5/Win7  |       -120       |       0\n         *         Chrome v17/OS X  |       -120       |       0\n         *         Chrome v17/Win7  |       -120       |       0\n         *                IE9/Win7  |       -120       |   undefined\n         *         Firefox v4/OS X  |     undefined    |       1\n         *         Firefox v4/Win7  |     undefined    |       3\n         *\n         */\n        function normalizeWheel( /*object*/ event ) /*object*/ {\n            // Reasonable defaults\n            var PIXEL_STEP = 10;\n            var LINE_HEIGHT = 40;\n            var PAGE_HEIGHT = 800;\n        \n            var sX = 0, sY = 0,       // spinX, spinY\n                pX = 0, pY = 0;       // pixelX, pixelY\n        \n            // Legacy\n            if( 'detail' in event ) {\n                sY = event.detail;\n            }\n            if( 'wheelDelta' in event ) {\n                sY = -event.wheelDelta / 120;\n            }\n            if( 'wheelDeltaY' in event ) {\n                sY = -event.wheelDeltaY / 120;\n            }\n            if( 'wheelDeltaX' in event ) {\n                sX = -event.wheelDeltaX / 120;\n            }\n        \n            // side scrolling on FF with DOMMouseScroll\n            if( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {\n                sX = sY;\n                sY = 0;\n            }\n        \n            pX = sX * PIXEL_STEP;\n            pY = sY * PIXEL_STEP;\n        \n            if( 'deltaY' in event ) {\n                pY = event.deltaY;\n            }\n            if( 'deltaX' in event ) {\n                pX = event.deltaX;\n            }\n        \n            if( (pX || pY) && event.deltaMode ) {\n                if( event.deltaMode === 1 ) {          // delta in LINE units\n                    pX *= LINE_HEIGHT;\n                    pY *= LINE_HEIGHT;\n                } else {                             // delta in PAGE units\n                    pX *= PAGE_HEIGHT;\n                    pY *= PAGE_HEIGHT;\n                }\n            }\n        \n            // Fall-back if spin cannot be determined\n            if( pX && !sX ) {\n                sX = (pX < 1) ? -1 : 1;\n            }\n            if( pY && !sY ) {\n                sY = (pY < 1) ? -1 : 1;\n            }\n        \n            return {\n                spinX: sX,\n                spinY: sY,\n                pixelX: pX,\n                pixelY: pY\n            };\n        }\n\n        /*=========================\n          Parallax\n          ===========================*/\n        function setParallaxTransform(el, progress) {\n            el = $(el);\n            var p, pX, pY;\n            var rtlFactor = s.rtl ? -1 : 1;\n        \n            p = el.attr('data-swiper-parallax') || '0';\n            pX = el.attr('data-swiper-parallax-x');\n            pY = el.attr('data-swiper-parallax-y');\n            if (pX || pY) {\n                pX = pX || '0';\n                pY = pY || '0';\n            }\n            else {\n                if (s.isHorizontal()) {\n                    pX = p;\n                    pY = '0';\n                }\n                else {\n                    pY = p;\n                    pX = '0';\n                }\n            }\n        \n            if ((pX).indexOf('%') >= 0) {\n                pX = parseInt(pX, 10) * progress * rtlFactor + '%';\n            }\n            else {\n                pX = pX * progress * rtlFactor + 'px' ;\n            }\n            if ((pY).indexOf('%') >= 0) {\n                pY = parseInt(pY, 10) * progress + '%';\n            }\n            else {\n                pY = pY * progress + 'px' ;\n            }\n        \n            el.transform('translate3d(' + pX + ', ' + pY + ',0px)');\n        }\n        s.parallax = {\n            setTranslate: function () {\n                s.container.children('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    setParallaxTransform(this, s.progress);\n        \n                });\n                s.slides.each(function () {\n                    var slide = $(this);\n                    slide.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function () {\n                        var progress = Math.min(Math.max(slide[0].progress, -1), 1);\n                        setParallaxTransform(this, progress);\n                    });\n                });\n            },\n            setTransition: function (duration) {\n                if (typeof duration === 'undefined') duration = s.params.speed;\n                s.container.find('[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y]').each(function(){\n                    var el = $(this);\n                    var parallaxDuration = parseInt(el.attr('data-swiper-parallax-duration'), 10) || duration;\n                    if (duration === 0) parallaxDuration = 0;\n                    el.transition(parallaxDuration);\n                });\n            }\n        };\n        \n\n        /*=========================\n          Zoom\n          ===========================*/\n        s.zoom = {\n            // \"Global\" Props\n            scale: 1,\n            currentScale: 1,\n            isScaling: false,\n            gesture: {\n                slide: undefined,\n                slideWidth: undefined,\n                slideHeight: undefined,\n                image: undefined,\n                imageWrap: undefined,\n                zoomMax: s.params.zoomMax\n            },\n            image: {\n                isTouched: undefined,\n                isMoved: undefined,\n                currentX: undefined,\n                currentY: undefined,\n                minX: undefined,\n                minY: undefined,\n                maxX: undefined,\n                maxY: undefined,\n                width: undefined,\n                height: undefined,\n                startX: undefined,\n                startY: undefined,\n                touchesStart: {},\n                touchesCurrent: {}\n            },\n            velocity: {\n                x: undefined,\n                y: undefined,\n                prevPositionX: undefined,\n                prevPositionY: undefined,\n                prevTime: undefined\n            },\n            // Calc Scale From Multi-touches\n            getDistanceBetweenTouches: function (e) {\n                if (e.targetTouches.length < 2) return 1;\n                var x1 = e.targetTouches[0].pageX,\n                    y1 = e.targetTouches[0].pageY,\n                    x2 = e.targetTouches[1].pageX,\n                    y2 = e.targetTouches[1].pageY;\n                var distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));\n                return distance;\n            },\n            // Events\n            onGestureStart: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchstart' || e.type === 'touchstart' && e.targetTouches.length < 2) {\n                        return;\n                    }\n                    z.gesture.scaleStart = z.getDistanceBetweenTouches(e);\n                }\n                if (!z.gesture.slide || !z.gesture.slide.length) {\n                    z.gesture.slide = $(this);\n                    if (z.gesture.slide.length === 0) z.gesture.slide = s.slides.eq(s.activeIndex);\n                    z.gesture.image = z.gesture.slide.find('img, svg, canvas');\n                    z.gesture.imageWrap = z.gesture.image.parent('.' + s.params.zoomContainerClass);\n                    z.gesture.zoomMax = z.gesture.imageWrap.attr('data-swiper-zoom') || s.params.zoomMax ;\n                    if (z.gesture.imageWrap.length === 0) {\n                        z.gesture.image = undefined;\n                        return;\n                    }\n                }\n                z.gesture.image.transition(0);\n                z.isScaling = true;\n            },\n            onGestureChange: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchmove' || e.type === 'touchmove' && e.targetTouches.length < 2) {\n                        return;\n                    }\n                    z.gesture.scaleMove = z.getDistanceBetweenTouches(e);\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (s.support.gestures) {\n                    z.scale = e.scale * z.currentScale;\n                }\n                else {\n                    z.scale = (z.gesture.scaleMove / z.gesture.scaleStart) * z.currentScale;\n                }\n                if (z.scale > z.gesture.zoomMax) {\n                    z.scale = z.gesture.zoomMax - 1 + Math.pow((z.scale - z.gesture.zoomMax + 1), 0.5);\n                }\n                if (z.scale < s.params.zoomMin) {\n                    z.scale =  s.params.zoomMin + 1 - Math.pow((s.params.zoomMin - z.scale + 1), 0.5);\n                }\n                z.gesture.image.transform('translate3d(0,0,0) scale(' + z.scale + ')');\n            },\n            onGestureEnd: function (e) {\n                var z = s.zoom;\n                if (!s.support.gestures) {\n                    if (e.type !== 'touchend' || e.type === 'touchend' && e.changedTouches.length < 2) {\n                        return;\n                    }\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                z.scale = Math.max(Math.min(z.scale, z.gesture.zoomMax), s.params.zoomMin);\n                z.gesture.image.transition(s.params.speed).transform('translate3d(0,0,0) scale(' + z.scale + ')');\n                z.currentScale = z.scale;\n                z.isScaling = false;\n                if (z.scale === 1) z.gesture.slide = undefined;\n            },\n            onTouchStart: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (z.image.isTouched) return;\n                if (s.device.os === 'android') e.preventDefault();\n                z.image.isTouched = true;\n                z.image.touchesStart.x = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n                z.image.touchesStart.y = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n            },\n            onTouchMove: function (e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                s.allowClick = false;\n                if (!z.image.isTouched || !z.gesture.slide) return;\n        \n                if (!z.image.isMoved) {\n                    z.image.width = z.gesture.image[0].offsetWidth;\n                    z.image.height = z.gesture.image[0].offsetHeight;\n                    z.image.startX = s.getTranslate(z.gesture.imageWrap[0], 'x') || 0;\n                    z.image.startY = s.getTranslate(z.gesture.imageWrap[0], 'y') || 0;\n                    z.gesture.slideWidth = z.gesture.slide[0].offsetWidth;\n                    z.gesture.slideHeight = z.gesture.slide[0].offsetHeight;\n                    z.gesture.imageWrap.transition(0);\n                }\n                // Define if we need image drag\n                var scaledWidth = z.image.width * z.scale;\n                var scaledHeight = z.image.height * z.scale;\n        \n                if (scaledWidth < z.gesture.slideWidth && scaledHeight < z.gesture.slideHeight) return;\n        \n                z.image.minX = Math.min((z.gesture.slideWidth / 2 - scaledWidth / 2), 0);\n                z.image.maxX = -z.image.minX;\n                z.image.minY = Math.min((z.gesture.slideHeight / 2 - scaledHeight / 2), 0);\n                z.image.maxY = -z.image.minY;\n        \n                z.image.touchesCurrent.x = e.type === 'touchmove' ? e.targetTouches[0].pageX : e.pageX;\n                z.image.touchesCurrent.y = e.type === 'touchmove' ? e.targetTouches[0].pageY : e.pageY;\n        \n                if (!z.image.isMoved && !z.isScaling) {\n                    if (s.isHorizontal() &&\n                        (Math.floor(z.image.minX) === Math.floor(z.image.startX) && z.image.touchesCurrent.x < z.image.touchesStart.x) ||\n                        (Math.floor(z.image.maxX) === Math.floor(z.image.startX) && z.image.touchesCurrent.x > z.image.touchesStart.x)\n                        ) {\n                        z.image.isTouched = false;\n                        return;\n                    }\n                    else if (!s.isHorizontal() &&\n                        (Math.floor(z.image.minY) === Math.floor(z.image.startY) && z.image.touchesCurrent.y < z.image.touchesStart.y) ||\n                        (Math.floor(z.image.maxY) === Math.floor(z.image.startY) && z.image.touchesCurrent.y > z.image.touchesStart.y)\n                        ) {\n                        z.image.isTouched = false;\n                        return;\n                    }\n                }\n                e.preventDefault();\n                e.stopPropagation();\n        \n                z.image.isMoved = true;\n                z.image.currentX = z.image.touchesCurrent.x - z.image.touchesStart.x + z.image.startX;\n                z.image.currentY = z.image.touchesCurrent.y - z.image.touchesStart.y + z.image.startY;\n        \n                if (z.image.currentX < z.image.minX) {\n                    z.image.currentX =  z.image.minX + 1 - Math.pow((z.image.minX - z.image.currentX + 1), 0.8);\n                }\n                if (z.image.currentX > z.image.maxX) {\n                    z.image.currentX = z.image.maxX - 1 + Math.pow((z.image.currentX - z.image.maxX + 1), 0.8);\n                }\n        \n                if (z.image.currentY < z.image.minY) {\n                    z.image.currentY =  z.image.minY + 1 - Math.pow((z.image.minY - z.image.currentY + 1), 0.8);\n                }\n                if (z.image.currentY > z.image.maxY) {\n                    z.image.currentY = z.image.maxY - 1 + Math.pow((z.image.currentY - z.image.maxY + 1), 0.8);\n                }\n        \n                //Velocity\n                if (!z.velocity.prevPositionX) z.velocity.prevPositionX = z.image.touchesCurrent.x;\n                if (!z.velocity.prevPositionY) z.velocity.prevPositionY = z.image.touchesCurrent.y;\n                if (!z.velocity.prevTime) z.velocity.prevTime = Date.now();\n                z.velocity.x = (z.image.touchesCurrent.x - z.velocity.prevPositionX) / (Date.now() - z.velocity.prevTime) / 2;\n                z.velocity.y = (z.image.touchesCurrent.y - z.velocity.prevPositionY) / (Date.now() - z.velocity.prevTime) / 2;\n                if (Math.abs(z.image.touchesCurrent.x - z.velocity.prevPositionX) < 2) z.velocity.x = 0;\n                if (Math.abs(z.image.touchesCurrent.y - z.velocity.prevPositionY) < 2) z.velocity.y = 0;\n                z.velocity.prevPositionX = z.image.touchesCurrent.x;\n                z.velocity.prevPositionY = z.image.touchesCurrent.y;\n                z.velocity.prevTime = Date.now();\n        \n                z.gesture.imageWrap.transform('translate3d(' + z.image.currentX + 'px, ' + z.image.currentY + 'px,0)');\n            },\n            onTouchEnd: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n                if (!z.image.isTouched || !z.image.isMoved) {\n                    z.image.isTouched = false;\n                    z.image.isMoved = false;\n                    return;\n                }\n                z.image.isTouched = false;\n                z.image.isMoved = false;\n                var momentumDurationX = 300;\n                var momentumDurationY = 300;\n                var momentumDistanceX = z.velocity.x * momentumDurationX;\n                var newPositionX = z.image.currentX + momentumDistanceX;\n                var momentumDistanceY = z.velocity.y * momentumDurationY;\n                var newPositionY = z.image.currentY + momentumDistanceY;\n        \n                //Fix duration\n                if (z.velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - z.image.currentX) / z.velocity.x);\n                if (z.velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - z.image.currentY) / z.velocity.y);\n                var momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n        \n                z.image.currentX = newPositionX;\n                z.image.currentY = newPositionY;\n        \n                // Define if we need image drag\n                var scaledWidth = z.image.width * z.scale;\n                var scaledHeight = z.image.height * z.scale;\n                z.image.minX = Math.min((z.gesture.slideWidth / 2 - scaledWidth / 2), 0);\n                z.image.maxX = -z.image.minX;\n                z.image.minY = Math.min((z.gesture.slideHeight / 2 - scaledHeight / 2), 0);\n                z.image.maxY = -z.image.minY;\n                z.image.currentX = Math.max(Math.min(z.image.currentX, z.image.maxX), z.image.minX);\n                z.image.currentY = Math.max(Math.min(z.image.currentY, z.image.maxY), z.image.minY);\n        \n                z.gesture.imageWrap.transition(momentumDuration).transform('translate3d(' + z.image.currentX + 'px, ' + z.image.currentY + 'px,0)');\n            },\n            onTransitionEnd: function (s) {\n                var z = s.zoom;\n                if (z.gesture.slide && s.previousIndex !== s.activeIndex) {\n                    z.gesture.image.transform('translate3d(0,0,0) scale(1)');\n                    z.gesture.imageWrap.transform('translate3d(0,0,0)');\n                    z.gesture.slide = z.gesture.image = z.gesture.imageWrap = undefined;\n                    z.scale = z.currentScale = 1;\n                }\n            },\n            // Toggle Zoom\n            toggleZoom: function (s, e) {\n                var z = s.zoom;\n                if (!z.gesture.slide) {\n                    z.gesture.slide = s.clickedSlide ? $(s.clickedSlide) : s.slides.eq(s.activeIndex);\n                    z.gesture.image = z.gesture.slide.find('img, svg, canvas');\n                    z.gesture.imageWrap = z.gesture.image.parent('.' + s.params.zoomContainerClass);\n                }\n                if (!z.gesture.image || z.gesture.image.length === 0) return;\n        \n                var touchX, touchY, offsetX, offsetY, diffX, diffY, translateX, translateY, imageWidth, imageHeight, scaledWidth, scaledHeight, translateMinX, translateMinY, translateMaxX, translateMaxY, slideWidth, slideHeight;\n        \n                if (typeof z.image.touchesStart.x === 'undefined' && e) {\n                    touchX = e.type === 'touchend' ? e.changedTouches[0].pageX : e.pageX;\n                    touchY = e.type === 'touchend' ? e.changedTouches[0].pageY : e.pageY;\n                }\n                else {\n                    touchX = z.image.touchesStart.x;\n                    touchY = z.image.touchesStart.y;\n                }\n        \n                if (z.scale && z.scale !== 1) {\n                    // Zoom Out\n                    z.scale = z.currentScale = 1;\n                    z.gesture.imageWrap.transition(300).transform('translate3d(0,0,0)');\n                    z.gesture.image.transition(300).transform('translate3d(0,0,0) scale(1)');\n                    z.gesture.slide = undefined;\n                }\n                else {\n                    // Zoom In\n                    z.scale = z.currentScale = z.gesture.imageWrap.attr('data-swiper-zoom') || s.params.zoomMax;\n                    if (e) {\n                        slideWidth = z.gesture.slide[0].offsetWidth;\n                        slideHeight = z.gesture.slide[0].offsetHeight;\n                        offsetX = z.gesture.slide.offset().left;\n                        offsetY = z.gesture.slide.offset().top;\n                        diffX = offsetX + slideWidth/2 - touchX;\n                        diffY = offsetY + slideHeight/2 - touchY;\n        \n                        imageWidth = z.gesture.image[0].offsetWidth;\n                        imageHeight = z.gesture.image[0].offsetHeight;\n                        scaledWidth = imageWidth * z.scale;\n                        scaledHeight = imageHeight * z.scale;\n        \n                        translateMinX = Math.min((slideWidth / 2 - scaledWidth / 2), 0);\n                        translateMinY = Math.min((slideHeight / 2 - scaledHeight / 2), 0);\n                        translateMaxX = -translateMinX;\n                        translateMaxY = -translateMinY;\n        \n                        translateX = diffX * z.scale;\n                        translateY = diffY * z.scale;\n        \n                        if (translateX < translateMinX) {\n                            translateX =  translateMinX;\n                        }\n                        if (translateX > translateMaxX) {\n                            translateX = translateMaxX;\n                        }\n        \n                        if (translateY < translateMinY) {\n                            translateY =  translateMinY;\n                        }\n                        if (translateY > translateMaxY) {\n                            translateY = translateMaxY;\n                        }\n                    }\n                    else {\n                        translateX = 0;\n                        translateY = 0;\n                    }\n                    z.gesture.imageWrap.transition(300).transform('translate3d(' + translateX + 'px, ' + translateY + 'px,0)');\n                    z.gesture.image.transition(300).transform('translate3d(0,0,0) scale(' + z.scale + ')');\n                }\n            },\n            // Attach/Detach Events\n            attachEvents: function (detach) {\n                var action = detach ? 'off' : 'on';\n        \n                if (s.params.zoom) {\n                    var target = s.slides;\n                    var passiveListener = s.touchEvents.start === 'touchstart' && s.support.passiveListener && s.params.passiveListeners ? {passive: true, capture: false} : false;\n                    // Scale image\n                    if (s.support.gestures) {\n                        s.slides[action]('gesturestart', s.zoom.onGestureStart, passiveListener);\n                        s.slides[action]('gesturechange', s.zoom.onGestureChange, passiveListener);\n                        s.slides[action]('gestureend', s.zoom.onGestureEnd, passiveListener);\n                    }\n                    else if (s.touchEvents.start === 'touchstart') {\n                        s.slides[action](s.touchEvents.start, s.zoom.onGestureStart, passiveListener);\n                        s.slides[action](s.touchEvents.move, s.zoom.onGestureChange, passiveListener);\n                        s.slides[action](s.touchEvents.end, s.zoom.onGestureEnd, passiveListener);\n                    }\n        \n                    // Move image\n                    s[action]('touchStart', s.zoom.onTouchStart);\n                    s.slides.each(function (index, slide){\n                        if ($(slide).find('.' + s.params.zoomContainerClass).length > 0) {\n                            $(slide)[action](s.touchEvents.move, s.zoom.onTouchMove);\n                        }\n                    });\n                    s[action]('touchEnd', s.zoom.onTouchEnd);\n        \n                    // Scale Out\n                    s[action]('transitionEnd', s.zoom.onTransitionEnd);\n                    if (s.params.zoomToggle) {\n                        s.on('doubleTap', s.zoom.toggleZoom);\n                    }\n                }\n            },\n            init: function () {\n                s.zoom.attachEvents();\n            },\n            destroy: function () {\n                s.zoom.attachEvents(true);\n            }\n        };\n\n        /*=========================\n          Plugins API. Collect all and init all plugins\n          ===========================*/\n        s._plugins = [];\n        for (var plugin in s.plugins) {\n            var p = s.plugins[plugin](s, s.params[plugin]);\n            if (p) s._plugins.push(p);\n        }\n        // Method to call all plugins event/method\n        s.callPlugins = function (eventName) {\n            for (var i = 0; i < s._plugins.length; i++) {\n                if (eventName in s._plugins[i]) {\n                    s._plugins[i][eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n        };\n\n        /*=========================\n          Events/Callbacks/Plugins Emitter\n          ===========================*/\n        function normalizeEventName (eventName) {\n            if (eventName.indexOf('on') !== 0) {\n                if (eventName[0] !== eventName[0].toUpperCase()) {\n                    eventName = 'on' + eventName[0].toUpperCase() + eventName.substring(1);\n                }\n                else {\n                    eventName = 'on' + eventName;\n                }\n            }\n            return eventName;\n        }\n        s.emitterEventListeners = {\n        \n        };\n        s.emit = function (eventName) {\n            // Trigger callbacks\n            if (s.params[eventName]) {\n                s.params[eventName](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n            }\n            var i;\n            // Trigger events\n            if (s.emitterEventListeners[eventName]) {\n                for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                    s.emitterEventListeners[eventName][i](arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n                }\n            }\n            // Trigger plugins\n            if (s.callPlugins) s.callPlugins(eventName, arguments[1], arguments[2], arguments[3], arguments[4], arguments[5]);\n        };\n        s.on = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            if (!s.emitterEventListeners[eventName]) s.emitterEventListeners[eventName] = [];\n            s.emitterEventListeners[eventName].push(handler);\n            return s;\n        };\n        s.off = function (eventName, handler) {\n            var i;\n            eventName = normalizeEventName(eventName);\n            if (typeof handler === 'undefined') {\n                // Remove all handlers for such event\n                s.emitterEventListeners[eventName] = [];\n                return s;\n            }\n            if (!s.emitterEventListeners[eventName] || s.emitterEventListeners[eventName].length === 0) return;\n            for (i = 0; i < s.emitterEventListeners[eventName].length; i++) {\n                if(s.emitterEventListeners[eventName][i] === handler) s.emitterEventListeners[eventName].splice(i, 1);\n            }\n            return s;\n        };\n        s.once = function (eventName, handler) {\n            eventName = normalizeEventName(eventName);\n            var _handler = function () {\n                handler(arguments[0], arguments[1], arguments[2], arguments[3], arguments[4]);\n                s.off(eventName, _handler);\n            };\n            s.on(eventName, _handler);\n            return s;\n        };\n\n        // Accessibility tools\n        s.a11y = {\n            makeFocusable: function ($el) {\n                $el.attr('tabIndex', '0');\n                return $el;\n            },\n            addRole: function ($el, role) {\n                $el.attr('role', role);\n                return $el;\n            },\n        \n            addLabel: function ($el, label) {\n                $el.attr('aria-label', label);\n                return $el;\n            },\n        \n            disable: function ($el) {\n                $el.attr('aria-disabled', true);\n                return $el;\n            },\n        \n            enable: function ($el) {\n                $el.attr('aria-disabled', false);\n                return $el;\n            },\n        \n            onEnterKey: function (event) {\n                if (event.keyCode !== 13) return;\n                if ($(event.target).is(s.params.nextButton)) {\n                    s.onClickNext(event);\n                    if (s.isEnd) {\n                        s.a11y.notify(s.params.lastSlideMessage);\n                    }\n                    else {\n                        s.a11y.notify(s.params.nextSlideMessage);\n                    }\n                }\n                else if ($(event.target).is(s.params.prevButton)) {\n                    s.onClickPrev(event);\n                    if (s.isBeginning) {\n                        s.a11y.notify(s.params.firstSlideMessage);\n                    }\n                    else {\n                        s.a11y.notify(s.params.prevSlideMessage);\n                    }\n                }\n                if ($(event.target).is('.' + s.params.bulletClass)) {\n                    $(event.target)[0].click();\n                }\n            },\n        \n            liveRegion: $('<span class=\"' + s.params.notificationClass + '\" aria-live=\"assertive\" aria-atomic=\"true\"></span>'),\n        \n            notify: function (message) {\n                var notification = s.a11y.liveRegion;\n                if (notification.length === 0) return;\n                notification.html('');\n                notification.html(message);\n            },\n            init: function () {\n                // Setup accessibility\n                if (s.params.nextButton && s.nextButton && s.nextButton.length > 0) {\n                    s.a11y.makeFocusable(s.nextButton);\n                    s.a11y.addRole(s.nextButton, 'button');\n                    s.a11y.addLabel(s.nextButton, s.params.nextSlideMessage);\n                }\n                if (s.params.prevButton && s.prevButton && s.prevButton.length > 0) {\n                    s.a11y.makeFocusable(s.prevButton);\n                    s.a11y.addRole(s.prevButton, 'button');\n                    s.a11y.addLabel(s.prevButton, s.params.prevSlideMessage);\n                }\n        \n                $(s.container).append(s.a11y.liveRegion);\n            },\n            initPagination: function () {\n                if (s.params.pagination && s.params.paginationClickable && s.bullets && s.bullets.length) {\n                    s.bullets.each(function () {\n                        var bullet = $(this);\n                        s.a11y.makeFocusable(bullet);\n                        s.a11y.addRole(bullet, 'button');\n                        s.a11y.addLabel(bullet, s.params.paginationBulletMessage.replace(/{{index}}/, bullet.index() + 1));\n                    });\n                }\n            },\n            destroy: function () {\n                if (s.a11y.liveRegion && s.a11y.liveRegion.length > 0) s.a11y.liveRegion.remove();\n            }\n        };\n        \n\n        /*=========================\n          Init/Destroy\n          ===========================*/\n        s.init = function () {\n            if (s.params.loop) s.createLoop();\n            s.updateContainerSize();\n            s.updateSlidesSize();\n            s.updatePagination();\n            if (s.params.scrollbar && s.scrollbar) {\n                s.scrollbar.set();\n                if (s.params.scrollbarDraggable) {\n                    s.scrollbar.enableDraggable();\n                }\n            }\n            if (s.params.effect !== 'slide' && s.effects[s.params.effect]) {\n                if (!s.params.loop) s.updateProgress();\n                s.effects[s.params.effect].setTranslate();\n            }\n            if (s.params.loop) {\n                s.slideTo(s.params.initialSlide + s.loopedSlides, 0, s.params.runCallbacksOnInit);\n            }\n            else {\n                s.slideTo(s.params.initialSlide, 0, s.params.runCallbacksOnInit);\n                if (s.params.initialSlide === 0) {\n                    if (s.parallax && s.params.parallax) s.parallax.setTranslate();\n                    if (s.lazy && s.params.lazyLoading) {\n                        s.lazy.load();\n                        s.lazy.initialImageLoaded = true;\n                    }\n                }\n            }\n            s.attachEvents();\n            if (s.params.observer && s.support.observer) {\n                s.initObservers();\n            }\n            if (s.params.preloadImages && !s.params.lazyLoading) {\n                s.preloadImages();\n            }\n            if (s.params.zoom && s.zoom) {\n                s.zoom.init();\n            }\n            if (s.params.autoplay) {\n                s.startAutoplay();\n            }\n            if (s.params.keyboardControl) {\n                if (s.enableKeyboardControl) s.enableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.enableMousewheelControl) s.enableMousewheelControl();\n            }\n            // Deprecated hashnavReplaceState changed to replaceState for use in hashnav and history\n            if (s.params.hashnavReplaceState) {\n                s.params.replaceState = s.params.hashnavReplaceState;\n            }\n            if (s.params.history) {\n                if (s.history) s.history.init();\n            }\n            if (s.params.hashnav) {\n                if (s.hashnav) s.hashnav.init();\n            }\n            if (s.params.a11y && s.a11y) s.a11y.init();\n            s.emit('onInit', s);\n        };\n        \n        // Cleanup dynamic styles\n        s.cleanupStyles = function () {\n            // Container\n            s.container.removeClass(s.classNames.join(' ')).removeAttr('style');\n        \n            // Wrapper\n            s.wrapper.removeAttr('style');\n        \n            // Slides\n            if (s.slides && s.slides.length) {\n                s.slides\n                    .removeClass([\n                      s.params.slideVisibleClass,\n                      s.params.slideActiveClass,\n                      s.params.slideNextClass,\n                      s.params.slidePrevClass\n                    ].join(' '))\n                    .removeAttr('style')\n                    .removeAttr('data-swiper-column')\n                    .removeAttr('data-swiper-row');\n            }\n        \n            // Pagination/Bullets\n            if (s.paginationContainer && s.paginationContainer.length) {\n                s.paginationContainer.removeClass(s.params.paginationHiddenClass);\n            }\n            if (s.bullets && s.bullets.length) {\n                s.bullets.removeClass(s.params.bulletActiveClass);\n            }\n        \n            // Buttons\n            if (s.params.prevButton) $(s.params.prevButton).removeClass(s.params.buttonDisabledClass);\n            if (s.params.nextButton) $(s.params.nextButton).removeClass(s.params.buttonDisabledClass);\n        \n            // Scrollbar\n            if (s.params.scrollbar && s.scrollbar) {\n                if (s.scrollbar.track && s.scrollbar.track.length) s.scrollbar.track.removeAttr('style');\n                if (s.scrollbar.drag && s.scrollbar.drag.length) s.scrollbar.drag.removeAttr('style');\n            }\n        };\n        \n        // Destroy\n        s.destroy = function (deleteInstance, cleanupStyles) {\n            // Detach evebts\n            s.detachEvents();\n            // Stop autoplay\n            s.stopAutoplay();\n            // Disable draggable\n            if (s.params.scrollbar && s.scrollbar) {\n                if (s.params.scrollbarDraggable) {\n                    s.scrollbar.disableDraggable();\n                }\n            }\n            // Destroy loop\n            if (s.params.loop) {\n                s.destroyLoop();\n            }\n            // Cleanup styles\n            if (cleanupStyles) {\n                s.cleanupStyles();\n            }\n            // Disconnect observer\n            s.disconnectObservers();\n        \n            // Destroy zoom\n            if (s.params.zoom && s.zoom) {\n                s.zoom.destroy();\n            }\n            // Disable keyboard/mousewheel\n            if (s.params.keyboardControl) {\n                if (s.disableKeyboardControl) s.disableKeyboardControl();\n            }\n            if (s.params.mousewheelControl) {\n                if (s.disableMousewheelControl) s.disableMousewheelControl();\n            }\n            // Disable a11y\n            if (s.params.a11y && s.a11y) s.a11y.destroy();\n            // Delete history popstate\n            if (s.params.history && !s.params.replaceState) {\n                window.removeEventListener('popstate', s.history.setHistoryPopState);\n            }\n            if (s.params.hashnav && s.hashnav)  {\n                s.hashnav.destroy();\n            }\n            // Destroy callback\n            s.emit('onDestroy');\n            // Delete instance\n            if (deleteInstance !== false) s = null;\n        };\n        \n        s.init();\n        \n\n    \n        // Return swiper instance\n        return s;\n    };\n    \n\n    /*==================================================\n        Prototype\n    ====================================================*/\n    Swiper.prototype = {\n        isSafari: (function () {\n            var ua = navigator.userAgent.toLowerCase();\n            return (ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0);\n        })(),\n        isUiWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent),\n        isArray: function (arr) {\n            return Object.prototype.toString.apply(arr) === '[object Array]';\n        },\n        /*==================================================\n        Browser\n        ====================================================*/\n        browser: {\n            ie: window.navigator.pointerEnabled || window.navigator.msPointerEnabled,\n            ieTouch: (window.navigator.msPointerEnabled && window.navigator.msMaxTouchPoints > 1) || (window.navigator.pointerEnabled && window.navigator.maxTouchPoints > 1),\n            lteIE9: (function() {\n                // create temporary DIV\n                var div = document.createElement('div');\n                // add content to tmp DIV which is wrapped into the IE HTML conditional statement\n                div.innerHTML = '<!--[if lte IE 9]><i></i><![endif]-->';\n                // return true / false value based on what will browser render\n                return div.getElementsByTagName('i').length === 1;\n            })()\n        },\n        /*==================================================\n        Devices\n        ====================================================*/\n        device: (function () {\n            var ua = navigator.userAgent;\n            var android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/);\n            var ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n            var ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n            var iphone = !ipad && ua.match(/(iPhone\\sOS)\\s([\\d_]+)/);\n            return {\n                ios: ipad || iphone || ipod,\n                android: android\n            };\n        })(),\n        /*==================================================\n        Feature Detection\n        ====================================================*/\n        support: {\n            touch : (window.Modernizr && Modernizr.touch === true) || (function () {\n                return !!(('ontouchstart' in window) || window.DocumentTouch && document instanceof DocumentTouch);\n            })(),\n    \n            transforms3d : (window.Modernizr && Modernizr.csstransforms3d === true) || (function () {\n                var div = document.createElement('div').style;\n                return ('webkitPerspective' in div || 'MozPerspective' in div || 'OPerspective' in div || 'MsPerspective' in div || 'perspective' in div);\n            })(),\n    \n            flexbox: (function () {\n                var div = document.createElement('div').style;\n                var styles = ('alignItems webkitAlignItems webkitBoxAlign msFlexAlign mozBoxAlign webkitFlexDirection msFlexDirection mozBoxDirection mozBoxOrient webkitBoxDirection webkitBoxOrient').split(' ');\n                for (var i = 0; i < styles.length; i++) {\n                    if (styles[i] in div) return true;\n                }\n            })(),\n    \n            observer: (function () {\n                return ('MutationObserver' in window || 'WebkitMutationObserver' in window);\n            })(),\n    \n            passiveListener: (function () {\n                var supportsPassive = false;\n                try {\n                    var opts = Object.defineProperty({}, 'passive', {\n                        get: function() {\n                            supportsPassive = true;\n                        }\n                    });\n                    window.addEventListener('testPassiveListener', null, opts);\n                } catch (e) {}\n                return supportsPassive;\n            })(),\n    \n            gestures: (function () {\n                return 'ongesturestart' in window;\n            })()\n        },\n        /*==================================================\n        Plugins\n        ====================================================*/\n        plugins: {}\n    };\n    \n\n    /*===========================\n     Get jQuery\n     ===========================*/\n    \n    addLibraryPlugin($);\n    \n    var domLib = $;\n\n    /*===========================\n    Add .swiper plugin from Dom libraries\n    ===========================*/\n    function addLibraryPlugin(lib) {\n        lib.fn.swiper = function (params) {\n            var firstInstance;\n            lib(this).each(function () {\n                var s = new Swiper(this, params);\n                if (!firstInstance) firstInstance = s;\n            });\n            return firstInstance;\n        };\n    }\n    \n    if (domLib) {\n        if (!('transitionEnd' in domLib.fn)) {\n            domLib.fn.transitionEnd = function (callback) {\n                var events = ['webkitTransitionEnd', 'transitionend', 'oTransitionEnd', 'MSTransitionEnd', 'msTransitionEnd'],\n                    i, j, dom = this;\n                function fireCallBack(e) {\n                    /*jshint validthis:true */\n                    if (e.target !== this) return;\n                    callback.call(this, e);\n                    for (i = 0; i < events.length; i++) {\n                        dom.off(events[i], fireCallBack);\n                    }\n                }\n                if (callback) {\n                    for (i = 0; i < events.length; i++) {\n                        dom.on(events[i], fireCallBack);\n                    }\n                }\n                return this;\n            };\n        }\n        if (!('transform' in domLib.fn)) {\n            domLib.fn.transform = function (transform) {\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransform = elStyle.MsTransform = elStyle.msTransform = elStyle.MozTransform = elStyle.OTransform = elStyle.transform = transform;\n                }\n                return this;\n            };\n        }\n        if (!('transition' in domLib.fn)) {\n            domLib.fn.transition = function (duration) {\n                if (typeof duration !== 'string') {\n                    duration = duration + 'ms';\n                }\n                for (var i = 0; i < this.length; i++) {\n                    var elStyle = this[i].style;\n                    elStyle.webkitTransitionDuration = elStyle.MsTransitionDuration = elStyle.msTransitionDuration = elStyle.MozTransitionDuration = elStyle.OTransitionDuration = elStyle.transitionDuration = duration;\n                }\n                return this;\n            };\n        }\n        if (!('outerWidth' in domLib.fn)) {\n            domLib.fn.outerWidth = function (includeMargins) {\n                if (this.length > 0) {\n                    if (includeMargins)\n                        return this[0].offsetWidth + parseFloat(this.css('margin-right')) + parseFloat(this.css('margin-left'));\n                    else\n                        return this[0].offsetWidth;\n                }\n                else return null;\n            };\n        }\n    }\n\n\treturn Swiper;\n}));\n//# sourceMappingURL=maps/swiper.jquery.umd.js.map\n"]}