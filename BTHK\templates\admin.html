<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BTHK Admin - لوحة التحكم</title>
    <link rel="icon" href="/interface/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #4CAF50;
        }
        
        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .nav-tab {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .nav-tab.active {
            background: #4CAF50;
        }
        
        .nav-tab:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .nav-tab.active:hover {
            background: #45a049;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .tab-content {
            display: none;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 2rem;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            margin-right: 0.5rem;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn-danger {
            background: #f44336;
        }
        
        .btn-danger:hover {
            background: #da190b;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table th,
        .table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .table th {
            background: rgba(0, 0, 0, 0.3);
            font-weight: bold;
        }
        
        .table tr:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background: #4CAF50;
            color: white;
        }
        
        .status-inactive {
            background: #f44336;
            color: white;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .alert-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .stat-label {
            margin-top: 0.5rem;
            color: #ccc;
        }
        
        .upload-area {
            border: 2px dashed #4CAF50;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            background: rgba(76, 175, 80, 0.1);
        }
        
        .upload-area.dragover {
            background: rgba(76, 175, 80, 0.2);
            border-color: #45a049;
        }
        
        @media (max-width: 768px) {
            .nav-tabs {
                flex-wrap: wrap;
            }
            
            .container {
                padding: 0 0.5rem;
            }
            
            .table {
                font-size: 0.8rem;
            }
            
            .table th,
            .table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚙️ BTHK Admin Panel</h1>
        <p>لوحة التحكم الرئيسية</p>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">📊 الرئيسية</button>
            <button class="nav-tab" onclick="showTab('channels')">📺 القنوات</button>
            <button class="nav-tab" onclick="showTab('files')">📁 الملفات</button>
            <button class="nav-tab" onclick="showTab('settings')">⚙️ الإعدادات</button>
        </div>
    </div>
    
    <div class="container">
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <h2>📊 لوحة المعلومات</h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalChannels">0</div>
                    <div class="stat-label">إجمالي القنوات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="activeChannels">0</div>
                    <div class="stat-label">القنوات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalFiles">0</div>
                    <div class="stat-label">إجمالي الملفات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="currentViewers">0</div>
                    <div class="stat-label">المشاهدون الحاليون</div>
                </div>
            </div>
            
            <h3>📈 إحصائيات النظام</h3>
            <p>النظام يعمل بشكل طبيعي على البورت 3333</p>
            <p>تم تشغيل النظام في: <span id="serverStartTime">جاري التحميل...</span></p>
        </div>
        
        <!-- Channels Tab -->
        <div id="channels" class="tab-content">
            <h2>📺 إدارة القنوات</h2>
            
            <div id="channelAlert"></div>
            
            <h3>إضافة قناة جديدة</h3>
            <form id="channelForm">
                <div class="form-group">
                    <label for="channelName">اسم القناة:</label>
                    <input type="text" id="channelName" required>
                </div>
                <div class="form-group">
                    <label for="channelUrl">رابط البث:</label>
                    <input type="url" id="channelUrl" required placeholder="http://example.com/stream.m3u8">
                </div>
                <div class="form-group">
                    <label for="channelType">نوع البث:</label>
                    <select id="channelType">
                        <option value="live">بث مباشر</option>
                        <option value="file">ملف</option>
                        <option value="url">رابط خارجي</option>
                    </select>
                </div>
                <button type="submit" class="btn">➕ إضافة القناة</button>
            </form>
            
            <h3>القنوات الموجودة</h3>
            <table class="table" id="channelsTable">
                <thead>
                    <tr>
                        <th>الإجراءات</th>
                        <th>الحالة</th>
                        <th>النوع</th>
                        <th>الرابط</th>
                        <th>اسم القناة</th>
                        <th>الرقم</th>
                    </tr>
                </thead>
                <tbody id="channelsTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center;">جاري تحميل القنوات...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Files Tab -->
        <div id="files" class="tab-content">
            <h2>📁 إدارة الملفات</h2>
            
            <div id="fileAlert"></div>
            
            <h3>رفع ملف جديد</h3>
            <div class="upload-area" id="uploadArea">
                <p>📤 اسحب الملفات هنا أو انقر للاختيار</p>
                <p style="font-size: 0.8rem; color: #ccc;">الحد الأقصى: 500 ميجابايت</p>
                <input type="file" id="fileInput" style="display: none;" accept="video/*,audio/*">
            </div>
            
            <h3>الملفات المرفوعة</h3>
            <table class="table" id="filesTable">
                <thead>
                    <tr>
                        <th>الإجراءات</th>
                        <th>تاريخ الرفع</th>
                        <th>الحجم</th>
                        <th>النوع</th>
                        <th>اسم الملف</th>
                        <th>الرقم</th>
                    </tr>
                </thead>
                <tbody id="filesTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center;">جاري تحميل الملفات...</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <h2>⚙️ إعدادات النظام</h2>
            
            <div id="settingsAlert"></div>
            
            <form id="settingsForm">
                <div class="form-group">
                    <label for="systemName">اسم النظام:</label>
                    <input type="text" id="systemName" value="BTHK Live System">
                </div>
                <div class="form-group">
                    <label for="serverIp">عنوان الخادم:</label>
                    <input type="text" id="serverIp" value="localhost">
                </div>
                <div class="form-group">
                    <label for="serverPort">منفذ الخادم:</label>
                    <input type="number" id="serverPort" value="3333" readonly>
                </div>
                <button type="submit" class="btn">💾 حفظ الإعدادات</button>
            </form>
            
            <h3>معلومات النظام</h3>
            <table class="table">
                <tr>
                    <td>إصدار Node.js</td>
                    <td id="nodeVersion">جاري التحميل...</td>
                </tr>
                <tr>
                    <td>نظام التشغيل</td>
                    <td id="osInfo">جاري التحميل...</td>
                </tr>
                <tr>
                    <td>وقت تشغيل النظام</td>
                    <td id="uptime">جاري التحميل...</td>
                </tr>
            </table>
        </div>
    </div>
    
    <script src="/socket.io/socket.io.js"></script>
    <script>
        const socket = io();
        
        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked nav tab
            event.target.classList.add('active');
            
            // Load data for the selected tab
            switch(tabName) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'channels':
                    loadChannels();
                    break;
                case 'files':
                    loadFiles();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }
        
        // Socket events
        socket.on('connect', () => {
            console.log('Connected to admin panel');
            loadDashboard();
        });
        
        socket.on('viewerCount', (count) => {
            document.getElementById('currentViewers').textContent = count;
        });
        
        // Load dashboard data
        async function loadDashboard() {
            try {
                const [channelsRes, filesRes] = await Promise.all([
                    fetch('/api/channels'),
                    fetch('/api/files')
                ]);
                
                const channelsData = await channelsRes.json();
                const filesData = await filesRes.json();
                
                const totalChannels = channelsData.channels?.length || 0;
                const activeChannels = channelsData.channels?.filter(c => c.status === 'active').length || 0;
                const totalFiles = filesData.files?.length || 0;
                
                document.getElementById('totalChannels').textContent = totalChannels;
                document.getElementById('activeChannels').textContent = activeChannels;
                document.getElementById('totalFiles').textContent = totalFiles;
                
                document.getElementById('serverStartTime').textContent = new Date().toLocaleString('ar-SA');
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }
        
        // Load channels
        async function loadChannels() {
            try {
                const response = await fetch('/api/channels');
                const data = await response.json();
                
                const tbody = document.getElementById('channelsTableBody');
                
                if (data.channels && data.channels.length > 0) {
                    tbody.innerHTML = data.channels.map(channel => `
                        <tr>
                            <td>
                                <button class="btn btn-danger" onclick="deleteChannel(${channel.id})">🗑️ حذف</button>
                            </td>
                            <td>
                                <span class="status-badge ${channel.status === 'active' ? 'status-active' : 'status-inactive'}">
                                    ${channel.status === 'active' ? 'نشط' : 'غير نشط'}
                                </span>
                            </td>
                            <td>${channel.type}</td>
                            <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">${channel.url}</td>
                            <td>${channel.name}</td>
                            <td>${channel.id}</td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">لا توجد قنوات</td></tr>';
                }
            } catch (error) {
                console.error('Error loading channels:', error);
                showAlert('channelAlert', 'خطأ في تحميل القنوات', 'error');
            }
        }
        
        // Add channel
        document.getElementById('channelForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const name = document.getElementById('channelName').value;
            const url = document.getElementById('channelUrl').value;
            const type = document.getElementById('channelType').value;
            
            try {
                const response = await fetch('/api/channels', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, url, type })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('channelAlert', 'تم إضافة القناة بنجاح', 'success');
                    document.getElementById('channelForm').reset();
                    loadChannels();
                    loadDashboard();
                } else {
                    showAlert('channelAlert', data.error || 'خطأ في إضافة القناة', 'error');
                }
            } catch (error) {
                console.error('Error adding channel:', error);
                showAlert('channelAlert', 'خطأ في الاتصال بالخادم', 'error');
            }
        });
        
        // Delete channel
        async function deleteChannel(id) {
            if (!confirm('هل أنت متأكد من حذف هذه القناة؟')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/channels/${id}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('channelAlert', 'تم حذف القناة بنجاح', 'success');
                    loadChannels();
                    loadDashboard();
                } else {
                    showAlert('channelAlert', data.error || 'خطأ في حذف القناة', 'error');
                }
            } catch (error) {
                console.error('Error deleting channel:', error);
                showAlert('channelAlert', 'خطأ في الاتصال بالخادم', 'error');
            }
        }
        
        // Load files
        async function loadFiles() {
            try {
                const response = await fetch('/api/files');
                const data = await response.json();
                
                const tbody = document.getElementById('filesTableBody');
                
                if (data.files && data.files.length > 0) {
                    tbody.innerHTML = data.files.map(file => `
                        <tr>
                            <td>
                                <button class="btn btn-secondary" onclick="useAsChannel('${file.name}', '/assets/uploads/${file.path}')">📺 استخدام كقناة</button>
                            </td>
                            <td>${new Date(file.created_at).toLocaleString('ar-SA')}</td>
                            <td>${formatFileSize(file.size)}</td>
                            <td>${file.type}</td>
                            <td>${file.name}</td>
                            <td>${file.id}</td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">لا توجد ملفات</td></tr>';
                }
            } catch (error) {
                console.error('Error loading files:', error);
                showAlert('fileAlert', 'خطأ في تحميل الملفات', 'error');
            }
        }
        
        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                uploadFile(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                uploadFile(e.target.files[0]);
            }
        });
        
        // Upload file
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showAlert('fileAlert', 'جاري رفع الملف...', 'success');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('fileAlert', 'تم رفع الملف بنجاح', 'success');
                    loadFiles();
                    loadDashboard();
                } else {
                    showAlert('fileAlert', data.error || 'خطأ في رفع الملف', 'error');
                }
            } catch (error) {
                console.error('Error uploading file:', error);
                showAlert('fileAlert', 'خطأ في رفع الملف', 'error');
            }
        }
        
        // Use file as channel
        function useAsChannel(fileName, filePath) {
            document.getElementById('channelName').value = fileName;
            document.getElementById('channelUrl').value = window.location.origin + filePath;
            document.getElementById('channelType').value = 'file';
            showTab('channels');
        }
        
        // Load settings
        async function loadSettings() {
            try {
                const response = await fetch('/api/settings');
                const data = await response.json();
                
                if (data.settings) {
                    document.getElementById('systemName').value = data.settings.system_name || 'BTHK Live System';
                    document.getElementById('serverIp').value = data.settings.server_ip || 'localhost';
                    document.getElementById('serverPort').value = data.settings.server_port || '3333';
                }
                
                // Load system info
                document.getElementById('nodeVersion').textContent = 'Node.js (جاري التحميل...)';
                document.getElementById('osInfo').textContent = navigator.platform;
                document.getElementById('uptime').textContent = 'جاري التحميل...';
            } catch (error) {
                console.error('Error loading settings:', error);
                showAlert('settingsAlert', 'خطأ في تحميل الإعدادات', 'error');
            }
        }
        
        // Save settings
        document.getElementById('settingsForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const settings = {
                system_name: document.getElementById('systemName').value,
                server_ip: document.getElementById('serverIp').value,
                server_port: document.getElementById('serverPort').value
            };
            
            try {
                const response = await fetch('/api/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settings)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('settingsAlert', 'تم حفظ الإعدادات بنجاح', 'success');
                } else {
                    showAlert('settingsAlert', data.error || 'خطأ في حفظ الإعدادات', 'error');
                }
            } catch (error) {
                console.error('Error saving settings:', error);
                showAlert('settingsAlert', 'خطأ في الاتصال بالخادم', 'error');
            }
        });
        
        // Utility functions
        function showAlert(containerId, message, type) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
