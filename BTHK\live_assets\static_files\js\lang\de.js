'use strict';/*!
 * This is a `i18n` language object.
 *
 * German
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.de === undefined) {
		exports.de = {
			'mejs.plural-form': 1,			
			'mejs.download-file': 'Datei herunterladen',			
			'mejs.install-flash': 'Ihr Browser unterstützt kein Flash. Bitte aktivieren Sie Flash bzw. laden Sie die aktuellste Flash-Version herunter unter https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Vollbild',			
			'mejs.play': 'Abspielen',
			'mejs.pause': 'Pause',			
			'mejs.time-slider': 'Zeitschieberegler',
			'mejs.time-help-text': 'Verwende die Pfeiltaste nach links/rechts, um eine Sekunde zu spulen, hoch/runter um zehn Sekunden zu spulen.',
			'mejs.live-broadcast' : 'Live-Übertragung',			
			'mejs.volume-help-text': 'Verwende die Pfeiltaste nach oben/nach unten um die Lautstärke zu erhöhen oder zu verringern.',
			'mejs.unmute': 'Stummschaltung aufheben',
			'mejs.mute': 'Stummschalten',
			'mejs.volume-slider': 'Lautstärkeregler',			
			'mejs.video-player': 'Video-Player',
			'mejs.audio-player': 'Audio-Player',			
			'mejs.captions-subtitles': 'Überschriften/Untertitel',
			'mejs.captions-chapters': 'Kapitel',
			'mejs.none': 'Keine',
			'mejs.afrikaans': 'Afrikanisch',
			'mejs.albanian': 'Albanisch',
			'mejs.arabic': 'Arabisch',
			'mejs.belarusian': 'Weißrussisch',
			'mejs.bulgarian': 'Bulgarisch',
			'mejs.catalan': 'Katalanisch',
			'mejs.chinese': 'Chinesisch',
			'mejs.chinese-simplified': 'Chinesisch (Vereinfacht)',
			'mejs.chinese-traditional': 'Chinesisch (Traditionell)',
			'mejs.croatian': 'Kroatisch',
			'mejs.czech': 'Tschechisch',
			'mejs.danish': 'Dänisch',
			'mejs.dutch': 'Niederländisch',
			'mejs.english': 'Englisch',
			'mejs.estonian': 'Estnisch',
			'mejs.filipino': 'Filipino',
			'mejs.finnish': 'Finnisch',
			'mejs.french': 'Französisch',
			'mejs.galician': 'Galicisch',
			'mejs.german': 'Deutsch',
			'mejs.greek': 'Griechisch',
			'mejs.haitian-creole': 'Haitianisch',
			'mejs.hebrew': 'Hebräisch',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Ungarisch',
			'mejs.icelandic': 'Isländisch',
			'mejs.indonesian': 'Indonesisch',
			'mejs.irish': 'Irisch',
			'mejs.italian': 'Italienisch',
			'mejs.japanese': 'Japanisch',
			'mejs.korean': 'Koreanisch',
			'mejs.latvian': 'Lettisch',
			'mejs.lithuanian': 'Litauisch',
			'mejs.macedonian': 'Mazedonisch',
			'mejs.malay': 'Malaysisch',
			'mejs.maltese': 'Maltesisch',
			'mejs.norwegian': 'Norwegisch',
			'mejs.persian': 'Persisch',
			'mejs.polish': 'Polnisch',
			'mejs.portuguese': 'Portugiesisch',
			'mejs.romanian': 'Rumänisch',
			'mejs.russian': 'Russisch',
			'mejs.serbian': 'Serbisch',
			'mejs.slovak': 'Slovakisch',
			'mejs.slovenian': 'Slovenisch',
			'mejs.spanish': 'Spanisch',
			'mejs.swahili': 'Swahili',
			'mejs.swedish': 'Schwedisch',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thailändisch',
			'mejs.turkish': 'Türkisch',
			'mejs.ukrainian': 'Ukrainisch',
			'mejs.vietnamese': 'Vietnamnesisch',
			'mejs.welsh': 'Walisisch',
			'mejs.yiddish': 'Jiddisch'
		};
	}
})(mejs.i18n);
