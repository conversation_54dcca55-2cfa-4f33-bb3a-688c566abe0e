'use strict';/*!
 * This is a `i18n` language object.
 *
 * Italian
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   Sascha 'SoftCreatR' Greuel
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.it === undefined) {
		exports.it = {
			'mejs.plural-form': 1,			
			'mejs.download-file': 'Scaricare il file',			
			'mejs.install-flash': 'Stai utilizzando un browser che non dispone di Flash Player abilitato o installato. Accenda il tuo plug-in Flash Player o scarica la versione più recente da https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Schermo intero',			
			'mejs.play': 'Eseguire',
			'mejs.pause': 'Pausa',			
			'mejs.time-slider': 'Barra di scorrimento',
			'mejs.time-help-text': 'Utilizzare i tasti Freccia sinistra/Freccia destra per avanzare di un secondo, Freccia Su/Giù per avanzare dieci secondi.',
			'mejs.live-broadcast' : 'Trasmissione in diretta',			
			'mejs.volume-help-text': 'Utilizzare i tasti Freccia Su/Giù per aumentare o diminuire il volume.',
			'mejs.unmute': 'Disattivare muto',
			'mejs.mute': 'Muto',
			'mejs.volume-slider': 'Barra del volume',			
			'mejs.video-player': 'Lettore Video',
			'mejs.audio-player': 'Lettore Audio',			
			'mejs.captions-subtitles': 'Acquisizioni/sottotitoli',
			'mejs.captions-chapters': 'Capitoli',
			'mejs.none': 'Nessuno',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albanese',
			'mejs.arabic': 'Arabo',
			'mejs.belarusian': 'Bielorusso',
			'mejs.bulgarian': 'Bulgaro',
			'mejs.catalan': 'Catalano',
			'mejs.chinese': 'Cinese',
			'mejs.chinese-semplificato': 'Cinese (Semplificato)',
			'mejs.chinese-traditional': 'Cinese (Tradizionale)',
			'mejs.croatian': 'Croato',
			'mejs.czech': 'Ceco',
			'mejs.danish': 'Danese',
			'mejs.dutch': 'Olandese',
			'mejs.english': 'Inglese',
			'mejs.estonian': 'Estone',
			'mejs.filipino': 'Filippino',
			'mejs.finnish': 'Finlandese',
			'mejs.french': 'Francese',
			'mejs.galician': 'Galiziano',
			'mejs.german': 'Tedesco',
			'mejs.greek': 'Greco',
			'mejs.haitian-creole': 'Creolo Haitiano',
			'mejs.hebrew': 'Ebraico',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Ungherese',
			'mejs.icelandic': 'Islandese',
			'mejs.indonesian': 'Indonesiano',
			'mejs.irish': 'Irlandese',
			'mejs.italian': 'Italiano',
			'mejs.japanese': 'Giapponese',
			'mejs.korean': 'Coreano',
			'mejs.latvian': 'Lettone',
			'mejs.lithuanian': 'Lituano',
			'mejs.macedonian': 'Macedone',
			'mejs.malay': 'Malay',
			'mejs.maltese': 'Maltese',
			'mejs.norwegian': 'Norvegese',
			'mejs.persian': 'Persiano',
			'mejs.polish': 'Polacco',
			'mejs.portuguese': 'Portoghese',
			'mejs.romanian': 'Rumeno',
			'mejs.russian': 'Russo',
			'mejs.serbian': 'Serbo',
			'mejs.slovak': 'Slovacco',
			'mejs.slovenian': 'Sloveno',
			'mejs.spanish': 'Spagnolo',
			'mejs.swahili': 'Swahili',
			'mejs.swedish': 'Svedese',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thai',
			'mejs.turkish': 'Turco',
			'mejs.ukrainian': 'Ucraino',
			'mejs.vietnamese': 'Vietnamita',
			'mejs.welsh': 'Gallese',
			'mejs.yiddish': 'Yiddish'
		};
	}
})(mejs.i18n);