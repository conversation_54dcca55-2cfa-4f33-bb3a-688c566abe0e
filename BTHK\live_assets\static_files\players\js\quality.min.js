/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,i,s){function a(n,o){if(!i[n]){if(!t[n]){var r="function"==typeof require&&require;if(!o&&r)return r(n,!0);if(l)return l(n,!0);var u=new Error("Cannot find module '"+n+"'");throw u.code="MODULE_NOT_FOUND",u}var c=i[n]={exports:{}};t[n][0].call(c.exports,function(e){var i=t[n][1][e];return a(i||e)},c,c.exports,e,t,i,s)}return i[n].exports}for(var l="function"==typeof require&&require,n=0;n<s.length;n++)a(s[n]);return a}({1:[function(e,t,i){"use strict";mejs.i18n.en["mejs.quality-chooser"]="Quality Chooser",Object.assign(mejs.MepDefaults,{defaultQuality:"auto",qualityText:null}),Object.assign(MediaElementPlayer.prototype,{buildquality:function(e,t,i,s){for(var a=this,l=a.mediaFiles?a.mediaFiles:a.node.children,n=new Map,o=0,r=l.length;o<r;o++){var u=l[o],c=u instanceof HTMLElement?u.getAttribute("data-quality"):u["data-quality"];if(a.mediaFiles){var d=document.createElement("source");d.src=u.src,d.type=u.type,a.addValueToKey(n,c,d)}else"SOURCE"===u.nodeName&&a.addValueToKey(n,c,u)}if(!(n.size<=1)){a.cleanquality(e);var f=mejs.Utils.isString(a.options.qualityText)?a.options.qualityText:mejs.i18n.t("mejs.quality-quality"),p=function(e){return"auto"===e?a.keyExist(n,e)?e:a.getMapIndex(n,0).key:e}(a.options.defaultQuality);e.qualitiesButton=document.createElement("div"),e.qualitiesButton.className=a.options.classPrefix+"button "+a.options.classPrefix+"qualities-button",e.qualitiesButton.innerHTML='<button type="button" aria-controls="'+a.id+'" title="'+f+'" aria-label="'+f+'" tabindex="0">'+p+'</button><div class="'+a.options.classPrefix+"qualities-selector "+a.options.classPrefix+'offscreen"><ul class="'+a.options.classPrefix+'qualities-selector-list"></ul></div>',a.addControlElement(e.qualitiesButton,"qualities"),s.setSrc(n.get(p)[0].src),s.load(),n.forEach(function(t,i){if("map_keys_1"!==i){var s=t[0],l=i,n=a.id+"-qualities-"+l;e.qualitiesButton.querySelector("ul").innerHTML+='<li class="'+a.options.classPrefix+'qualities-selector-list-item"><input class="'+a.options.classPrefix+'qualities-selector-input" type="radio" name="'+a.id+'_qualities"disabled="disabled" value="'+l+'" id="'+n+'"  '+(l===p?' checked="checked"':"")+'/><label for="'+n+'" class="'+a.options.classPrefix+"qualities-selector-label"+(l===p?" "+a.options.classPrefix+"qualities-selected":"")+'">'+(s.title||l)+"</label></li>"}});for(var q=["mouseenter","focusin"],y=["mouseleave","focusout"],m=e.qualitiesButton.querySelectorAll('input[type="radio"]'),v=e.qualitiesButton.querySelectorAll("."+a.options.classPrefix+"qualities-selector-label"),h=e.qualitiesButton.querySelector("."+a.options.classPrefix+"qualities-selector"),g=0,x=q.length;g<x;g++)e.qualitiesButton.addEventListener(q[g],function(){mejs.Utils.removeClass(h,a.options.classPrefix+"offscreen"),h.style.height=h.querySelector("ul").offsetHeight+"px",h.style.top=-1*parseFloat(h.offsetHeight)+"px"});for(var E=0,b=y.length;E<b;E++)e.qualitiesButton.addEventListener(y[E],function(){mejs.Utils.addClass(h,a.options.classPrefix+"offscreen")});for(var P=0,j=m.length;P<j;P++){var k=m[P];k.disabled=!1,k.addEventListener("change",function(){for(var t=this,i=t.value,l=e.qualitiesButton.querySelectorAll("."+a.options.classPrefix+"qualities-selected"),o=0,r=l.length;o<r;o++)mejs.Utils.removeClass(l[o],a.options.classPrefix+"qualities-selected");t.checked=!0;for(var u=mejs.Utils.siblings(t,function(e){return mejs.Utils.hasClass(e,a.options.classPrefix+"qualities-selector-label")}),c=0,d=u.length;c<d;c++)mejs.Utils.addClass(u[c],a.options.classPrefix+"qualities-selected");var f=s.currentTime,p=s.paused;e.qualitiesButton.querySelector("button").innerHTML=i,p||s.pause(),a.updateVideoSource(s,n,i),s.setSrc(n.get(i)[0].src),s.load(),s.dispatchEvent(mejs.Utils.createEvent("seeking",s)),p||s.play(),s.addEventListener("canplay",function e(){s.setCurrentTime(f),s.removeEventListener("canplay",e)})})}for(var C=0,S=v.length;C<S;C++)v[C].addEventListener("click",function(){var e=mejs.Utils.siblings(this,function(e){return"INPUT"===e.tagName})[0],t=mejs.Utils.createEvent("click",e);e.dispatchEvent(t)});h.addEventListener("keydown",function(e){e.stopPropagation()}),s.setSrc(n.get(p)[0].src)}},cleanquality:function(e){e&&e.qualitiesButton&&e.qualitiesButton.remove()},addValueToKey:function(e,t,i){e.has("map_keys_1")?e.get("map_keys_1").push(t.toLowerCase()):e.set("map_keys_1",[]),e.has(t)?e.get(t).push(i):(e.set(t,[]),e.get(t).push(i))},updateVideoSource:function(e,t,i){this.cleanMediaSource(e);for(var s=t.get(i),a=0;a<e.children.length;a++)!function(t){var i=e.children[t];"VIDEO"===i.tagName&&s.forEach(function(e){i.appendChild(e)})}(a)},cleanMediaSource:function(e){for(var t=0;t<e.children.length;t++){var i=e.children[t];if("VIDEO"===i.tagName)for(;i.firstChild;)i.removeChild(i.firstChild)}},getMapIndex:function(e,t){var i=-1,s={};return e.forEach(function(e,a){i===t&&(s.key=a,s.value=e),i++}),s},keyExist:function(e,t){return-1<e.get("map_keys_1").indexOf(t.toLowerCase())}})},{}]},{},[1]);