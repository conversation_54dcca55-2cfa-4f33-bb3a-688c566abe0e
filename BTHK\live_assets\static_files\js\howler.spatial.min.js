/*! howler.js v2.0.3 | Spatial Plugin | (c) 2013-2017, <PERSON> of GoldFire Studios | MIT License | howlerjs.com */
!function(){"use strict";HowlerGlobal.prototype._pos=[0,0,0],HowlerGlobal.prototype._orientation=[0,0,-1,0,1,0],HowlerGlobal.prototype.stereo=function(n){var e=this;if(!e.ctx||!e.ctx.listener)return e;for(var o=e._howls.length-1;o>=0;o--)e._howls[o].stereo(n);return e},HowlerGlobal.prototype.pos=function(n,e,o){var t=this;return t.ctx&&t.ctx.listener?(e="number"!=typeof e?t._pos[1]:e,o="number"!=typeof o?t._pos[2]:o,"number"!=typeof n?t._pos:(t._pos=[n,e,o],t.ctx.listener.setPosition(t._pos[0],t._pos[1],t._pos[2]),t)):t},HowlerGlobal.prototype.orientation=function(n,e,o,t,r,i){var a=this;if(!a.ctx||!a.ctx.listener)return a;var p=a._orientation;return e="number"!=typeof e?p[1]:e,o="number"!=typeof o?p[2]:o,t="number"!=typeof t?p[3]:t,r="number"!=typeof r?p[4]:r,i="number"!=typeof i?p[5]:i,"number"!=typeof n?p:(a._orientation=[n,e,o,t,r,i],a.ctx.listener.setOrientation(n,e,o,t,r,i),a)},Howl.prototype.init=function(n){return function(e){var o=this;return o._orientation=e.orientation||[1,0,0],o._stereo=e.stereo||null,o._pos=e.pos||null,o._pannerAttr={coneInnerAngle:void 0!==e.coneInnerAngle?e.coneInnerAngle:360,coneOuterAngle:void 0!==e.coneOuterAngle?e.coneOuterAngle:360,coneOuterGain:void 0!==e.coneOuterGain?e.coneOuterGain:0,distanceModel:void 0!==e.distanceModel?e.distanceModel:"inverse",maxDistance:void 0!==e.maxDistance?e.maxDistance:1e4,panningModel:void 0!==e.panningModel?e.panningModel:"HRTF",refDistance:void 0!==e.refDistance?e.refDistance:1,rolloffFactor:void 0!==e.rolloffFactor?e.rolloffFactor:1},o._onstereo=e.onstereo?[{fn:e.onstereo}]:[],o._onpos=e.onpos?[{fn:e.onpos}]:[],o._onorientation=e.onorientation?[{fn:e.onorientation}]:[],n.call(this,e)}}(Howl.prototype.init),Howl.prototype.stereo=function(e,o){var t=this;if(!t._webAudio)return t;if("loaded"!==t._state)return t._queue.push({event:"stereo",action:function(){t.stereo(e,o)}}),t;var r=void 0===Howler.ctx.createStereoPanner?"spatial":"stereo";if(void 0===o){if("number"!=typeof e)return t._stereo;t._stereo=e,t._pos=[e,0,0]}for(var i=t._getSoundIds(o),a=0;a<i.length;a++){var p=t._soundById(i[a]);if(p){if("number"!=typeof e)return p._stereo;p._stereo=e,p._pos=[e,0,0],p._node&&(p._pannerAttr.panningModel="equalpower",p._panner&&p._panner.pan||n(p,r),"spatial"===r?p._panner.setPosition(e,0,0):p._panner.pan.value=e),t._emit("stereo",p._id)}}return t},Howl.prototype.pos=function(e,o,t,r){var i=this;if(!i._webAudio)return i;if("loaded"!==i._state)return i._queue.push({event:"pos",action:function(){i.pos(e,o,t,r)}}),i;if(o="number"!=typeof o?0:o,t="number"!=typeof t?-.5:t,void 0===r){if("number"!=typeof e)return i._pos;i._pos=[e,o,t]}for(var a=i._getSoundIds(r),p=0;p<a.length;p++){var s=i._soundById(a[p]);if(s){if("number"!=typeof e)return s._pos;s._pos=[e,o,t],s._node&&(s._panner&&!s._panner.pan||n(s,"spatial"),s._panner.setPosition(e,o,t)),i._emit("pos",s._id)}}return i},Howl.prototype.orientation=function(e,o,t,r){var i=this;if(!i._webAudio)return i;if("loaded"!==i._state)return i._queue.push({event:"orientation",action:function(){i.orientation(e,o,t,r)}}),i;if(o="number"!=typeof o?i._orientation[1]:o,t="number"!=typeof t?i._orientation[2]:t,void 0===r){if("number"!=typeof e)return i._orientation;i._orientation=[e,o,t]}for(var a=i._getSoundIds(r),p=0;p<a.length;p++){var s=i._soundById(a[p]);if(s){if("number"!=typeof e)return s._orientation;s._orientation=[e,o,t],s._node&&(s._panner||(s._pos||(s._pos=i._pos||[0,0,-.5]),n(s,"spatial")),s._panner.setOrientation(e,o,t)),i._emit("orientation",s._id)}}return i},Howl.prototype.pannerAttr=function(){var e,o,t,r=this,i=arguments;if(!r._webAudio)return r;if(0===i.length)return r._pannerAttr;if(1===i.length){if("object"!=typeof i[0])return t=r._soundById(parseInt(i[0],10)),t?t._pannerAttr:r._pannerAttr;e=i[0],void 0===o&&(r._pannerAttr={coneInnerAngle:void 0!==e.coneInnerAngle?e.coneInnerAngle:r._coneInnerAngle,coneOuterAngle:void 0!==e.coneOuterAngle?e.coneOuterAngle:r._coneOuterAngle,coneOuterGain:void 0!==e.coneOuterGain?e.coneOuterGain:r._coneOuterGain,distanceModel:void 0!==e.distanceModel?e.distanceModel:r._distanceModel,maxDistance:void 0!==e.maxDistance?e.maxDistance:r._maxDistance,panningModel:void 0!==e.panningModel?e.panningModel:r._panningModel,refDistance:void 0!==e.refDistance?e.refDistance:r._refDistance,rolloffFactor:void 0!==e.rolloffFactor?e.rolloffFactor:r._rolloffFactor})}else 2===i.length&&(e=i[0],o=parseInt(i[1],10));for(var a=r._getSoundIds(o),p=0;p<a.length;p++)if(t=r._soundById(a[p])){var s=t._pannerAttr;s={coneInnerAngle:void 0!==e.coneInnerAngle?e.coneInnerAngle:s.coneInnerAngle,coneOuterAngle:void 0!==e.coneOuterAngle?e.coneOuterAngle:s.coneOuterAngle,coneOuterGain:void 0!==e.coneOuterGain?e.coneOuterGain:s.coneOuterGain,distanceModel:void 0!==e.distanceModel?e.distanceModel:s.distanceModel,maxDistance:void 0!==e.maxDistance?e.maxDistance:s.maxDistance,panningModel:void 0!==e.panningModel?e.panningModel:s.panningModel,refDistance:void 0!==e.refDistance?e.refDistance:s.refDistance,rolloffFactor:void 0!==e.rolloffFactor?e.rolloffFactor:s.rolloffFactor};var l=t._panner;l?(l.coneInnerAngle=s.coneInnerAngle,l.coneOuterAngle=s.coneOuterAngle,l.coneOuterGain=s.coneOuterGain,l.distanceModel=s.distanceModel,l.maxDistance=s.maxDistance,l.panningModel=s.panningModel,l.refDistance=s.refDistance,l.rolloffFactor=s.rolloffFactor):(t._pos||(t._pos=r._pos||[0,0,-.5]),n(t,"spatial"))}return r},Sound.prototype.init=function(n){return function(){var e=this,o=e._parent;e._orientation=o._orientation,e._stereo=o._stereo,e._pos=o._pos,e._pannerAttr=o._pannerAttr,n.call(this),e._stereo?o.stereo(e._stereo):e._pos&&o.pos(e._pos[0],e._pos[1],e._pos[2],e._id)}}(Sound.prototype.init),Sound.prototype.reset=function(n){return function(){var e=this,o=e._parent;return e._orientation=o._orientation,e._pos=o._pos,e._pannerAttr=o._pannerAttr,n.call(this)}}(Sound.prototype.reset);var n=function(n,e){e=e||"spatial","spatial"===e?(n._panner=Howler.ctx.createPanner(),n._panner.coneInnerAngle=n._pannerAttr.coneInnerAngle,n._panner.coneOuterAngle=n._pannerAttr.coneOuterAngle,n._panner.coneOuterGain=n._pannerAttr.coneOuterGain,n._panner.distanceModel=n._pannerAttr.distanceModel,n._panner.maxDistance=n._pannerAttr.maxDistance,n._panner.panningModel=n._pannerAttr.panningModel,n._panner.refDistance=n._pannerAttr.refDistance,n._panner.rolloffFactor=n._pannerAttr.rolloffFactor,n._panner.setPosition(n._pos[0],n._pos[1],n._pos[2]),n._panner.setOrientation(n._orientation[0],n._orientation[1],n._orientation[2])):(n._panner=Howler.ctx.createStereoPanner(),n._panner.pan.value=n._stereo),n._panner.connect(n._node),n._paused||n._parent.pause(n._id,!0).play(n._id)}}();