'use strict';/*!
 * This is a `i18n` language object.
 *
 * Russian
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.ru === undefined) {
		exports.ru = {
			'mejs.plural-form': 7,			
			'mejs.download-file': 'Скачать файл',			
			'mejs.install-flash': 'Flash player в вашем браузере не установлен или отключен. Пожалуйста включите ваш Flash player или скачайте последнюю версию с https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Полноэкранный режим',			
			'mejs.play': 'Воспроизвести',
			'mejs.pause': 'Пауза',			
			'mejs.time-slider': 'Слайдер времени',
			'mejs.time-help-text': 'Используйте Левую/Правую клавиши со стрелками, чтобы продвинуться на одну секунду, клавиши со стрелками Вверх/Вниз, чтобы продвинуться на десять секунд.',
			'mejs.live-broadcast' : 'Прямая трансляция',			
			'mejs.volume-help-text': 'Используйте клавиши со стрелками Вверх/Вниз, чтобы увеличить или уменьшить громкость.',
			'mejs.unmute': 'Включить звук',
			'mejs.mute': 'Отключить звук',
			'mejs.volume-slider': 'Слайдер громкости',			
			'mejs.video-player': 'Видеоплеер',
			'mejs.audio-player': 'Аудиоплеер',			
			'mejs.captions-subtitles': 'Титры/Субтитры',
			'mejs.captions-chapters': 'Главы',
			'mejs.none': 'Нет',
			'mejs.afrikaans': 'Африканский',
			'mejs.albanian': 'Албанский',
			'mejs.arabic': 'Арабский',
			'mejs.belarusian': 'Белорусский',
			'mejs.bulgarian': 'Болгарский',
			'mejs.catalan': 'Каталонский',
			'mejs.chinese': 'Китайский',
			'mejs.chinese-simplified': 'Китайский (упрощенный)',
			'mejs.chinese-traditional': 'Chinese (традиционный)',
			'mejs.croatian': 'Хорватский',
			'mejs.czech': 'Чешский',
			'mejs.danish': 'Датский',
			'mejs.dutch': 'Голландский',
			'mejs.english': 'Английский',
			'mejs.estonian': 'Эстонский',
			'mejs.filipino': 'Филиппинский',
			'mejs.finnish': 'Финский',
			'mejs.french': 'Французский',
			'mejs.galician': 'Галисийский',
			'mejs.german': 'Немецкий',
			'mejs.greek': 'Греческий',
			'mejs.haitian-creole': 'Гаитянский креольский',
			'mejs.hebrew': 'Иврит',
			'mejs.hindi': 'Хинди',
			'mejs.hungarian': 'Венгерский',
			'mejs.icelandic': 'Исландский',
			'mejs.indonesian': 'Индонезийский',
			'mejs.irish': 'Ирландский',
			'mejs.italian': 'Итальянский',
			'mejs.japanese': 'Японский',
			'mejs.korean': 'Корейский',
			'mejs.latvian': 'Латышский',
			'mejs.lithuanian': 'Литовский',
			'mejs.macedonian': 'Македонский',
			'mejs.malay': 'Малайский',
			'mejs.maltese': 'Мальтийский',
			'mejs.norwegian': 'Норвежский',
			'mejs.persian': 'Персидский',
			'mejs.polish': 'Польский',
			'mejs.portuguese': 'Португальский',
			'mejs.romanian': 'Румынский',
			'mejs.russian': 'Русский',
			'mejs.serbian': 'Сербский',
			'mejs.slovak': 'Словацкий',
			'mejs.slovenian': 'Словенский',
			'mejs.spanish': 'Испанский',
			'mejs.swahili': 'Суахили',
			'mejs.swedish': 'Шведский',
			'mejs.tagalog': 'Тагальский',
			'mejs.thai': 'Тайский',
			'mejs.turkish': 'Турецкий',
			'mejs.ukrainian': 'Украинский',
			'mejs.vietnamese': 'Вьетнамский',
			'mejs.welsh': 'Валлийский',
			'mejs.yiddish': 'Идиш'
		};
	}
})(mejs.i18n);
