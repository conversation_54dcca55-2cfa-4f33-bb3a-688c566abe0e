<!DOCTYPE html>
<html>
    {% include "admin/head.html" %}
  <body dir="rtl" class="noselect" >
        {% include "admin/mnu.html" %}
    
    <div class="nopadding">
        <div class="page">
            <div class="msg info">من هنا تستطيع تنزيل واستعادة النسخه الاحتياطية في اي وقت .</div>
            <h4>تنزيل واستعادة النسخه الاحتياطية</h4>
            <hr>
            <form action="/{{admin_panel_path}}/restoreBackup" id="saveForm" method="POST" enctype="multipart/form-data">
                <div class="input">
                    <div class="row nopadding">
                        <div class="text col-sm-2">
                            ملف النسخه الاحتياطية
                        </div>
                        <div class="in-e col-sm-10">
                            <input  name="backup_file" id="backup_file" type="file"  >
                            
                        </div>
                    </div>
                </div>
                <div class="input">
                    <div class="row nopadding">
                        <div class="sbmt-btn" id="uploadfile" style="width:200px">
                            <div class=" reg-icon icon  col-xs-4   fa  fa-upload " style="width:40px"></div>
                            <div  class=" text   col-xs-8 " style="width:79%">رفع</div>
                        </div>
                    </div>
                </div>
            </form>
            <hr class="hard">
            <div class="input">
                <div class="row nopadding">
                    <div class="sbmt-btn" id="backupnow" style="width:200px">
                        <div class=" reg-icon icon  col-xs-4   fa  fa-save " style="width:40px"></div>
                        <div  class=" text   col-xs-8 " style="width:79%">تنزيل نسخه احتياطية</div>
                    </div>
                </div>
            </div>
            <iframe id="my_downloadiframe" style="display:none;"></iframe>
            <script>
                $("#backupnow").click(function(){
                    var url = "/{{admin_panel_path}}/backupNOW";
                    document.getElementById('my_downloadiframe').src = url;
                });
                 $("#uploadfile").click(function(){
                    var formData = new FormData($("#saveForm")[0]);

                    $.ajax({
                        url: $("#saveForm").attr("action"),
                        type: 'POST',
                        data: formData,
                        success: function (data) {
                            
                            if(data.msg=="ok"){
                                setTimeout(function(){
                                    swal("تم الاسترجاع بنجاح", "تم استرجاع النسخه الاحتياطية بنجاح", "success");
                                },500);
                                
                                table.ajax.reload();
                            }else{
                                swal("خطاء", "حدث خطاء غير معروف!", "error");
                            }
                        },
                        error:function(){
                        
                        },
                        cache: false,
                        contentType: false,
                        processData: false
                    });
                })
            </script>
        </div>
    </div>
    
    {% include "admin/footer.html" %}
  </body>
</html>
