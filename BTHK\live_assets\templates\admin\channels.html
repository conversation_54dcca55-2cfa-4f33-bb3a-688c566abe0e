<!DOCTYPE html>
<html>
  {% include "admin/head.html" %}
  <body dir="rtl" class="noselect">
    {% include "admin/mnu.html" %}

    <div class="nopadding">
      <div class="page">
        <div class="msg info">من هنا تستطيع اضافة عدد لانهائي من القنوات الخاصه بالبث لكي تستخدمها لاحقاً .</div>
        <h4>اضافة قناة جديدة</h4>
        <form action="/{{admin_panel_path}}/saveChannel" id="saveForm" method="POST" enctype="multipart/form-data">
          <div class="content">
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اسم القناة</div>
                <div class="in-e col-sm-10">
                  <input name="cha_name" id="cha_name" placeholder="اسم القناة" />
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">صورة القناة</div>
                <div class="in-e col-sm-10">
                  <input name="cha_img" id="cha_img" type="file" placeholder="صورة القناة" />
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">مشغل البث المباشر عبر المتصفح</div>
                <div class="in-e col-sm-10">
                  <select name="cha_player" id="cha_player">
                    <option value="MediaElementJS">MediaElementJS بدون فلاش HTML5</option>
                    <option value="Flowplayer">Flowplayer فلاشي + HTML5</option>
                    <option value="hdwplayer">HDW Player</option>
                    <option value="ALL" selected>الكل (اختيار المستخدم)</option>
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اختر سيرفرات البث</div>
                <div class="in-e col-sm-10">
                  <select name="cha_server_id" id="cha_server_id">
                    {% for dev in servers %}
                    <option value="{{dev.id}}">{{dev.server_name}}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">مصدر البث</div>
                <div class="in-e col-sm-10">
                  <select name="cha_src" id="cha_src">
                    <option value="easycap1">ايزيكاب راسبيري باي</option>
                    <option value="easycap2">ايزيكاب اورنج باي</option>
                    <option value="easycap3">HDMI Video Capture</option>
                    {% for dev in devs %}
                    <option value="devs__{{dev.id}}">{{dev.e_name}}</option>
                    {% endfor %} {% for dev in linuxDevs %}
                    <option value="linuxDevs__{{dev.id}}">{{dev.e_name}}</option>
                    {% endfor %} {% for dev in dvr %}
                    <option value="dvr__{{dev.id}}">{{dev.dvr_name}}</option>
                    {% endfor %} {% for dev in iptv %}
                    <option value="iptv__{{dev.id}}">{{dev.iptv_name}}</option>
                    {% endfor %} {% for dev in file %}
                    <option value="file__{{dev.id}}">{{dev.file_name}}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اختر جدول البث</div>
                <div class="in-e col-sm-10">
                  <select name="cha_type" id="cha_type">
                    <option id="no">بدون جدولة</option>
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">تشغيل البث عند بدء التشغيل</div>
                <div class="in-e col-sm-10">
                  <select name="cha_boot" id="cha_boot">
                    <option value="yes">نعم</option>
                    <option value="no">لا</option>
                  </select>
                </div>
              </div>
            </div>

            <hr class="hard" />
            <div class="input">
              <div class="row nopadding">
                <div class="sbmt-btn" id="saveServer">
                  <div class="reg-icon icon col-xs-4 fa fa-plus"></div>
                  <div class="text col-xs-8">اضافة</div>
                </div>
                <div class="colasbmbtn" id="testDVRCurrent" style="margin-left: 10px">
                  <div class="reg-icon icon col-xs-4 fa fa-play"></div>
                  <div class="text col-xs-8">اختبار البث</div>
                </div>
              </div>
            </div>
          </div>
        </form>

        <div class="warppingTable">
          <table id="example" class="display" width="100%"></table>
          <script>
            function addToolTip(msg) {
              return 'data-placement="bottom" data-toggle="tooltip" title="' + msg + '"';
            }
            var IntervalTocheck = null;
            $(document).ready(function () {
              const TABLE = "{{admin_page}}";

              var table = $("#example").DataTable({
                processing: true,
                serverSide: true,
                ajax: "/{{admin_panel_path}}/get/" + TABLE,

                language: {
                  decimal: "",
                  emptyTable: "لايوجد اي بيانات في الجدول",
                  info: "عرض _START_ الى _END_ من _TOTAL_ عناصر",
                  infoEmpty: "عرض 0 الى 0 من 0 عناصر",
                  infoFiltered: "(فلترة من _MAX_ مجموعة عناصر)",
                  infoPostFix: "",
                  thousands: ",",
                  lengthMenu: "عرض _MENU_ عناصر",
                  loadingRecords: "تحميل...",
                  processing: "معالجة...",
                  search: "بحث:",
                  zeroRecords: "لايوجد سجلات بحسب بحثك",
                  paginate: {
                    first: "الاول",
                    last: "الاخر",
                    next: "التالي",
                    previous: "السابق",
                  },
                  aria: {
                    sortAscending: ": تفعيل ترتيب العمود من الاصغر للاكبر",
                    sortDescending: ": تفعيل ترتيب العمود من الاكبر للاصغر",
                  },
                },
                columns: [
                  {
                    title: "id",
                    render: function (data, type, row, meta) {
                      return row.id + "	&nbsp; <i class='is_run fa ' dataid='" + row.id + "' ></i>	&nbsp;";
                    },
                  },
                  {
                    title: "اسم القناة",
                    render: function (data, type, row, meta) {
                      return row.cha_name;
                    },
                  },
                  {
                    title: "صورة القناة",
                    render: function (data, type, row, meta) {
                      var imgHTML = "";
                      if (row.cha_img) {
                        imgHTML = "<img width='60' height='50' src='/getChannelImg/" + row.id + "'>";
                      } else {
                        imgHTML = "لايوجد صورة";
                      }
                      return imgHTML;
                    },
                  },
                  {
                    title: "مشغل البث",
                    render: function (data, type, row, meta) {
                      return row.cha_player;
                    },
                  },
                  {
                    title: "سيرفر البث",
                    render: function (data, type, row, meta) {
                      return row.cha_server_id;
                    },
                  },
                  {
                    title: "مصدر البث",
                    render: function (data, type, row, meta) {
                      return row.cha_src;
                    },
                  },
                  {
                    title: "تشغيل عند بدء التشغيل",
                    render: function (data, type, row, meta) {
                      return row.cha_boot || "لا";
                    },
                  },
                  {
                    title: "تحكم",
                    render: function (data, type, row, meta) {
                      return (
                        "<span dataid='" +
                        row.id +
                        "' ></span>  <i " +
                        addToolTip("حذف القناة") +
                        " class='deleteROW fa fa-times' dataid='" +
                        row.id +
                        "' ></i>"
                      );
                    },
                  },
                ],
                columnDefs: [
                  {
                    width: "2%",
                    targets: 0,
                    width: "1%",
                    targets: 6,
                    width: "10%",
                    targets: 7,
                  },
                ],
              });
              $("body").delegate(".deleteROW", "click", function () {
                var id = $(this).attr("dataid");

                swal(
                  {
                    title: "حذف هذا السجل : " + id,
                    text: "هل انت متاكد من حذف هذا السجل  : " + id,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "نعم",
                    cancelButtonText: "لا",
                    closeOnConfirm: true,
                    html: true,
                  },
                  function (isConfirm) {
                    if (!isConfirm) return;
                    $.ajax({
                      url: "/{{admin_panel_path}}/delete/" + TABLE + "/" + id,
                      success: function () {
                        clearInterval(IntervalTocheck);
                        table.ajax.reload();

                        setupinterval();
                        setTimeout(function () {
                          swal("تم الحذف بنجاح", "تم حذف السجل بنجاح", "success");
                        }, 300);
                      },
                    });
                  }
                );
              });

              $("#example tbody").on("click", "tr", function () {
                // var id = $(this)[0].childNodes[0].innerHTML;
                //$.ajax({url:"/delete/"+id,success:function(){
                //    table.ajax.reload();
                //}});
              });

              $("#button").click(function () {
                table.row(".selected").remove().draw(false);
              });

              $("#saveServer").click(function () {
                var formData = new FormData($("#saveForm")[0]);

                $.ajax({
                  url: $("#saveForm").attr("action"),
                  type: "POST",
                  data: formData,
                  success: function (data) {
                    if (data.msg == "ok") {
                      setTimeout(function () {
                        swal("تم الحفظ بنجاح", "تم حفظ بياناتك بنجاح", "success");
                      }, 500);

                      table.ajax.reload();
                    } else {
                      swal("خطاء", "حدث خطاء غير معروف!", "error");
                    }
                  },
                  error: function () {},
                  cache: false,
                  contentType: false,
                  processData: false,
                });
              });
            });
            // calc data
            $("input[name=server_hls_time]").change(function () {
              var valt = $(this).val();
              var valn = $("input[name=server_hls_num]").val();
              $("#setSUg").text(valt * valn);
            });
            $("input[name=server_hls_num]").change(function () {
              var valn = $(this).val();
              var valt = $("input[name=server_hls_time]").val();
              $("#setSUg").text(valt * valn);
            });
            $("#testDVRCurrent").click(function () {
              var cha_server_id = $("#cha_server_id").val();
              var src = $("#cha_src").val().split("__");
              var table = src[0];
              var cha_src = src[1];

              $(".fa-play").addClass("fa-spinner");
              $(".fa-play").addClass("fa-spin");
              $(".fa-play").removeClass("fa-play");
              swal({
                title: "يتم الاختبار انتظر قليلاً",
                text: "يتم اختبار البث",
                icon: "info",
                buttons: false,
                timer: 30000,
              });
              $.ajax({
                url: "/{{admin_panel_path}}/checkChannels/" + cha_server_id + "/" + table + "/" + cha_src,
                success: function (data) {
                  if (data.is_ok) {
                    swal("الاعدادات صحيحة", "تم تخطي الاختبار بنجاح", "success");
                  } else {
                    swal("يوجد خطاء في الاعدادات", "خطاء في تخطي الاختبار", "error");
                  }
                  $(".fa-spinner").addClass("fa-play");
                  $(".fa-play").removeClass("fa-spinner");
                  $(".fa-play").removeClass("fa-spin");
                },
              });
            });
            var is_run = false;
            function checkChannelActive() {
              $.ajax({
                url: "/{{admin_panel_path}}/getRunningTABLE/",
                success: function (data) {
                  $(".is_run").each(function () {
                    var id = $(this).attr("dataid");
                    var isok = false;
                    for (var i = 0; i < data.length; i++) {
                      if (id == data[i]) {
                        isok = true;
                        $(this).removeClass("closed");
                        $(this).removeClass("fa-eye-slash");
                        $(this).addClass("running");
                        $(this).addClass("fa-eye");
                        var t =
                          "<i " +
                          addToolTip("ايقاف تشغيل البث") +
                          " class='stopChannel fa fa-stop' style='background: #fff;color:#000;' dataid='" +
                          id +
                          "' ></i>";
                        $("span[dataid='" + id + "']").html(t);
                      }
                    }
                    if (!isok) {
                      $(this).removeClass("running");
                      $(this).removeClass("fa-eye");
                      $(this).addClass("closed");
                      $(this).addClass("fa-eye-slash");
                      var t =
                        "<i " +
                        addToolTip(" تشغيل البث") +
                        " class='startChannel fa fa-play' style='background: #fff;color:#000;' dataid='" +
                        id +
                        "' ></i>";
                      $("span[dataid='" + id + "']").html(t);
                    }
                  });
                },
              });
            }
            $("body").delegate(".startChannel", "click", function () {
              swal({
                title: "انتظر قليلاً يتم تشغيل القناة",
                text: "يتم التشغيل حالياً انتظر قليلاً",
                icon: "info",
                buttons: false,
                timer: 10000,
              });
              $.ajax({
                url: "/{{admin_panel_path}}/start_channel/" + $(this).attr("dataid"),
                success: function (data) {
                  swal.close();
                  if (data.is_ok == "error_server_not_found") {
                    swal("لا يمكن تشغيل القناة ", " تم حذف سيرفر البث !", "error");
                  }
                  if (data.is_ok == "error_src_not_found") {
                    swal("لا يمكن تشغيل القناة ", " تم حذف مصدر البث !", "error");
                  }
                },
              });
            });
            $("body").delegate(".stopChannel", "click", function () {
              $.ajax({
                url: "/{{admin_panel_path}}/stop_channel/" + $(this).attr("dataid"),
                success: function (data) {},
              });
            });
            setTimeout(function () {
              checkChannelActive();
            }, 600);
            function setupinterval() {
              IntervalTocheck = setInterval(function () {
                checkChannelActive();
              }, 3000);
            }
            setupinterval();
          </script>
        </div>
      </div>
    </div>

    {% include "admin/footer.html" %}
  </body>
</html>
