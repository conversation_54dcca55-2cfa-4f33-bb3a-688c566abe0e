'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.quality-chooser']= 'Selector de qualitat';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.quality-chooser']= 'Kvalitní výběr';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.quality-chooser']= 'Qualitätswähler';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.quality-chooser']= 'Selector de calidad';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.quality-chooser']= 'انتخاب کننده کیفیت';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.quality-chooser']= 'Sélecteur de qualité';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.quality-chooser']= 'Kvalitetni birač';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.quality-chooser']= 'Minőségi választó';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.quality-chooser']= 'Qualità scelto';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.quality-chooser']= '品質チューザー';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.quality-chooser']= '품질 선택자';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.quality-chooser']= 'Kwaliteit kiezer';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.quality-chooser']= 'Chooser jakości';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.quality-chooser']= 'Escolha de qualidade';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.quality-chooser']= 'Alegere de calitate';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.quality-chooser']= 'Выбор качества';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.quality-chooser']= 'Kvalitný výber';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.quality-chooser']= 'Kvalitetsvalare';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.quality-chooser']= 'Якісний вибір';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.quality-chooser']= '質量選擇';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.quality-chooser']= '质量选择';
}