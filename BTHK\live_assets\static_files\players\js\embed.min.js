!function n(e,t,r){function o(u,c){if(!t[u]){if(!e[u]){var f="function"==typeof require&&require;if(!c&&f)return f(u,!0);if(i)return i(u,!0);var s=new Error("Cannot find module '"+u+"'");throw s.code="MODULE_NOT_FOUND",s}var a=t[u]={exports:{}};e[u][0].call(a.exports,function(n){var t=e[u][1][n];return o(t?t:n)},a,a.exports,n,e,t,r)}return t[u].exports}for(var i="function"==typeof require&&require,u=0;u<r.length;u++)o(r[u]);return o}({1:[function(n,e,t){function r(n,e){"use strict";var t=n.common.createElement("div");t.setAttribute("data-origin",e.e.href),e.e.parentNode.replaceChild(t,e.e),e.c.e=1,e.c.swf=e.c.swf||p,e.c.swfHls=e.c.swfHls||m,n(t,e.c)}function o(n){"use strict";return"function"==typeof window.requirejs?n():void a("https://cdnjs.cloudflare.com/ajax/libs/require.js/2.1.16/require.min.js",n)}function i(n,e){"use strict";o(function(){requirejs([n],e)})}function u(n){"use strict";var e=n.common.createElement("div",{className:"flowplayer"});return e.style.display="none",document.body.appendChild(e),e}function c(n){"use strict";var e=u(n),t=0===window.getComputedStyle(e).getPropertyValue("counter-increment").indexOf("flowplayer");return n.common.removeNode(e),t}function f(n,e){"use strict";return c(n)?e(n):(document.head.appendChild(n.common.createElement("link",{rel:"stylesheet",href:h})),void e(n))}var s=window._fpes,a=n("scriptjs"),l="//releases.flowplayer.org/6.0.4/commercial",d=l+"/flowplayer.min.js",p=l+"/flowplayer.swf",m=l+"/flowplayerhls.swf",h=l+"/skin/functional.css";s.forEach(function(n){"use strict";i(n.l||d,function(e){f(e,function(e){r(e,n)})})})},{scriptjs:2}],2:[function(n,e,t){!function(n,t){"undefined"!=typeof e&&e.exports?e.exports=t():"function"==typeof define&&define.amd?define(t):this[n]=t()}("$script",function(){function n(n,e){for(var t=0,r=n.length;r>t;++t)if(!e(n[t]))return f;return 1}function e(e,t){n(e,function(n){return!t(n)})}function t(i,u,c){function f(n){return n.call?n():d[n]}function a(){if(!--w){d[y]=1,v&&v();for(var t in m)n(t.split("|"),f)&&!e(m[t],f)&&(m[t]=[])}}i=i[s]?i:[i];var l=u&&u.call,v=l?u:c,y=l?i.join(""):u,w=i.length;return setTimeout(function(){e(i,function n(e,t){return null===e?a():(e=t||-1!==e.indexOf(".js")||/^https?:\/\//.test(e)||!o?e:o+e+".js",h[e]?(y&&(p[y]=1),2==h[e]?a():setTimeout(function(){n(e,!0)},0)):(h[e]=1,y&&(p[y]=1),void r(e,a)))})},0),t}function r(n,e){var t,r=u.createElement("script");r.onload=r.onerror=r[l]=function(){r[a]&&!/^c|loade/.test(r[a])||t||(r.onload=r[l]=null,t=1,h[n]=2,e())},r.async=1,r.src=i?n+(-1===n.indexOf("?")?"?":"&")+i:n,c.insertBefore(r,c.lastChild)}var o,i,u=document,c=u.getElementsByTagName("head")[0],f=!1,s="push",a="readyState",l="onreadystatechange",d={},p={},m={},h={};return t.get=r,t.order=function(n,e,r){!function o(i){i=n.shift(),n.length?t(i,o):t(i,e,r)}()},t.path=function(n){o=n},t.urlArgs=function(n){i=n},t.ready=function(r,o,i){r=r[s]?r:[r];var u=[];return!e(r,function(n){d[n]||u[s](n)})&&n(r,function(n){return d[n]})?o():!function(n){m[n]=m[n]||[],m[n][s](o),i&&i(u)}(r.join("|")),t},t.done=function(n){t([null],n)},t})},{}]},{},[1]);
