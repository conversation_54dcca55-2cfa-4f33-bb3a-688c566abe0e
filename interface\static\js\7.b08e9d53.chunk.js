(this.webpackJsonpestra7ah5=this.webpackJsonpestra7ah5||[]).push([[7],{362:function(e,t,c){"use strict";var i=c(0),s=c(13),n=c(1),a=(c(367),c(365)),l=c(12),r=c(2),o=c(374),j=(c(368),c(369),c(48));var d=function(e){Object(n.useEffect)((function(){}),[]);var t="/itemView/"+e.item.item.type+"/"+e.item.item.id;if(e.item.item.content&&null!=e.item.item.content.contentJSON)try{JSON.parse(e.item.item.content.contentJSON)}catch(a){}function c(e){var t=parseInt(e/3600);t<10&&(t="0"+t);var c=parseInt(e%3600/60);c<10&&(c="0"+c);var i=Math.ceil(e%60);return i<10&&(i="0"+i),t+":"+c+":"+i}function s(){return Object(i.jsxs)(i.Fragment,{children:[Object(i.jsx)("div",{className:"over",title:e.item.item.name,children:Object(i.jsx)("div",{className:"inner",children:Object(i.jsx)("div",{children:Object(i.jsx)("i",{onClick:e.onClickIcon,className:"lni lni-trash"})})})}),Object(i.jsx)(j.a,{src:r.a.host+"ItemImage/"+e.item.item.id,alt:e.item.item.name,type:"movie"}),Object(i.jsx)("div",{className:"title",children:e.item.item.name&&e.item.item.name.length>20?"..."+e.item.item.name.substr(0,20):e.item.item.name}),Object(i.jsx)("div",{className:"det",children:Object(i.jsx)("div",{className:"time-line",style:{width:e.item.time/e.item.duration*100+"%"},children:Object(i.jsx)("span",{children:c(e.item.time)})})})]})}return"episod"==e.item.item.type||"vidsSameFolder"==e.item.item.type?Object(i.jsx)("a",{href:"/player/"+e.item.item.id,className:"movie-box-continue",item:e.item.item.id,children:s()},e.item.item.id):Object(i.jsx)(l.b,{to:t,className:"movie-box-continue",item:e.item.item.id,children:s()},e.item.item.id)};c(370);var m=function(e){if(Object(n.useEffect)((function(){}),[]),e.skeleton)return Object(i.jsx)("div",{children:"test"});if(!e.section)return Object(i.jsx)("div",{});var t=parseInt(e.section.createdAt)+259200>parseInt(+new Date/1e3);return Object(i.jsxs)(l.b,{to:"/sections/"+("linked"==e.section.type?e.section.linkedId:e.section.id),className:"section-box "+(e.isFloat&&"float"),onClick:e.onClick,children:[Object(i.jsx)("div",{className:"over",title:e.section.name,children:Object(i.jsx)("div",{children:Object(i.jsx)("i",{onClick:e.onClickIcon,className:"lni lni-"+e.centerIcon})})}),Object(i.jsx)(j.a,{src:r.a.host+"sectionImage/"+e.section.id,alt:e.section.name,type:"section"}),t&&Object(i.jsx)("div",{className:"new-text",children:"\u062c\u062f\u064a\u062f"}),"yes"===e.section.isVIP&&Object(i.jsx)("div",{title:"\u0641\u0642\u0637 \u0644\u0645\u0634\u062a\u0631\u0643\u064a\u0646 VIP",className:"vip-text",children:"VIP"}),Object(i.jsx)("div",{className:"title",children:e.section.name&&e.section.name.length>30?"..."+e.section.name.substr(0,30):e.section.name})]},e.section.id)},b=c(373),h=c(375),O=c(376),x=c(377),v=c(378),u=c(379);c(371);var p=function(e){return Object(i.jsx)("div",{className:"skeleton skeleton-animation"})};t.a=function(e){return Object(i.jsxs)("section",{style:Object(s.a)({},e.style),className:"items",children:[Object(i.jsx)(h.a,{icon:e.data.icon||e.icon,title:e.data.title||"\u0644\u0627\u064a\u0648\u062c\u062f \u0639\u0646\u0648\u0627\u0646",number:e.sections&&e.sections.length||e.items&&e.items.length}),Object(i.jsxs)("div",{children:[(!e.items&&!e.sections||"skeleton"===e.type)&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:window.innerWidth<500?[1,2].map((function(e){return Object(i.jsx)(p,{})})):[1,2,3,4,6,7,8,9,10].map((function(e){return Object(i.jsx)(p,{})}))})}),e.sections&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,isFloat:!0,autoMove:e.autoMove,children:e.sections&&e.sections.map((function(t){return Object(i.jsx)(m,{centerIcon:e.centerIcon,section:t,isFloat:!0,onClick:e.onItemClick})}))})}),e.items&&("movie"==e.type||"movies"==e.type||-1!==e.type.search("serieses")||-1!==e.type.search("series")||"seasons"==e.type)&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:e.items&&e.items.map((function(t){return t?Object(i.jsx)(o.a,{selected:e.selectedItemId===t.id,onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,appendLink:e.appendLink},t.id):Object(i.jsxs)(l.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:r.a.host+e.image+"error"})]},t.id)}))})}),e.items&&"eps"==e.type&&Object(i.jsx)("div",{className:"cont-all ",children:e.items&&e.items.map((function(t){return t?Object(i.jsx)(b.a,{selected:e.selectedItemId===t.id,onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,eps:e.items}):Object(i.jsxs)(l.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:r.a.host+e.image+"error"})]},t.id)}))}),e.items&&"continue-watch"==e.type&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:e.items&&e.items.map((function(t){return t?Object(i.jsx)(d,{item:t,onClickIcon:function(e){r.a.delWatch(t.item.id),e.preventDefault()}}):Object(i.jsxs)(l.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:r.a.host+e.image+"error"})]},t.id)}))})}),e.items&&"favs"==e.type&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:e.items&&e.items.map((function(t){return t?"app"==t.type||"appsSameFolder"==t.type?Object(i.jsx)("div",{style:{marginBottom:40},children:Object(i.jsx)(O.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){r.a.delFav(t.id),e.preventDefault()}})},t.id):"books"==t.type||"booksSameFolder"==t.type?Object(i.jsx)(x.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){r.a.delFav(t.id),e.preventDefault()}},t.id):"imagesSameFolder"==t.type?Object(i.jsx)(v.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){r.a.delFav(t.id),e.preventDefault()}},t.id):"audioSameFolder"==t.type||"singer"==t.type||"album"==t.type?Object(i.jsx)(u.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){r.a.delFav(t.id),e.preventDefault()}},t.id):"series"==t.type?Object(i.jsx)("div",{style:{marginBottom:40},children:Object(i.jsx)(o.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){r.a.delFav(t.id),e.preventDefault()}},t.id)}):"vidsSameFolder"==t.type?Object(i.jsx)("div",{style:{marginBottom:40},children:Object(i.jsx)(b.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){r.a.delFav(t.id),e.preventDefault()}},t.id)}):Object(i.jsx)(o.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){r.a.delFav(t.id),e.preventDefault()}},t.id):Object(i.jsxs)(l.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:r.a.host+e.image+"error"})]},t.id)}))})})]})]})}},363:function(e,t,c){"use strict";var i=c(0),s=c(4),n=c.n(s),a=c(27),l=c(6),r=c(5),o=c(1),j=(c(372),c(2)),d=[];function m(){return b.apply(this,arguments)}function b(){return(b=Object(l.a)(n.a.mark((function e(){return n.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!=d.length){e.next=4;break}return e.next=3,j.a.fetch(j.a.host+"getLast20Items",{method:"GET"}).then((function(e){return e.json()}));case 3:d=e.sent;case 4:return e.abrupt("return",d);case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var h=c(12);t.a=function(e){var t=Object(o.useState)([]),c=Object(r.a)(t,2),s=c[0],d=c[1],b=Object(o.useState)(0),O=Object(r.a)(b,2),x=O[0],v=O[1],u=Object(o.useState)(window.innerWidth),p=Object(r.a)(u,2),f=p[0];return p[1],Object(o.useEffect)(Object(l.a)(n.a.mark((function e(){var t;return n.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m();case 2:t=e.sent,d([].concat(Object(a.a)(t),Object(a.a)(t),Object(a.a)(t),Object(a.a)(t),Object(a.a)(t)));case 4:case"end":return e.stop()}}),e)}))),[e]),Object(o.useEffect)((function(){0!==(null===s||void 0===s?void 0:s.length)&&setTimeout((function(){x>19*(s.length-2)?v(0):v(x+19)}),1500)}),[s,x]),Object(i.jsxs)(i.Fragment,{children:[!j.a.isApp&&f<570&&Object(i.jsx)("div",{className:"play-cont",children:Object(i.jsx)("a",{href:"https://play.google.com/store/apps/details?id=co.yetech.estra7ahpro",children:Object(i.jsx)("img",{style:{width:"100%"},src:"/googleplay.png"})})}),Object(i.jsxs)("div",{className:"footer",children:[Object(i.jsx)("div",{className:"over-img"}),Object(i.jsx)("div",{style:{clear:"both"}}),Object(i.jsx)("div",{className:"networkname",children:j.a.networkName}),Object(i.jsx)("div",{className:"networkdesc",children:j.a.networkDesc}),Object(i.jsxs)("div",{className:"icons-social",children:[Object(i.jsx)("a",{href:j.a.main_facebook,target:"_blank",children:Object(i.jsx)("i",{className:"lni lni-facebook"})}),Object(i.jsx)("a",{href:"https://wa.me/"+j.a.main_phone+"?text=\u0627\u0647\u0644\u0627\u064b  "+j.a.networkName,target:"_blank",children:Object(i.jsx)("i",{className:"lni lni-whatsapp"})})]}),Object(i.jsx)("div",{style:{clear:"both"}}),Object(i.jsx)("div",{className:"new-items",children:Object(i.jsx)("div",{className:"items-ani-cont",style:{bottom:x},children:s.map((function(e){return Object(i.jsx)(h.b,{style:{color:"rgb(225 219 241)"},to:"/itemView/"+e.type+"/"+e.id,children:Object(i.jsx)("div",{className:"item",children:e.name})})}))})}),Object(i.jsxs)("div",{className:"footer-rights",children:[Object(i.jsx)("div",{children:" \u0628\u0646\u064a \u0628\u0641\u062e\u0631\u064d \u0641\u064a \u0627\u0644\u064a\u0645\u0646 "}),Object(i.jsxs)("div",{children:["\u062c\u0645\u064a\u0639 \u0627\u0644\u062d\u0642\u0648\u0642 \u0645\u062d\u0641\u0648\u0638\u0629 \u0634\u0631\u0643\u0647"," ",Object(i.jsx)("span",{children:Object(i.jsxs)("a",{target:"_blank",href:"http://yetech.co",children:[" ","YeTech - 736828627 - 770162085"]})})]})]}),Object(i.jsx)("div",{className:"versions",children:Object(i.jsxs)("div",{className:"system",children:["Estra7ah: ",j.a.currentVersion]})})]})]})}},365:function(e,t,c){"use strict";var i=c(0),s=c(5),n=c(1);c(366);t.a=function(e){var t=Object(n.useState)(0),c=Object(s.a)(t,2),a=c[0],l=c[1];return e.isFloat?Object(i.jsx)("div",{className:"float-slider",children:e.children}):Object(i.jsxs)("div",{className:"slider-kc",children:[Object(i.jsx)("div",{className:"items",style:{left:a},children:e.children}),Object(i.jsxs)("div",{className:"arrows",children:[Object(i.jsx)("div",{className:"left-arrow",onClick:function(){l(a+400)},children:Object(i.jsx)("i",{className:"lni lni-chevron-left"})}),Object(i.jsx)("div",{className:"right-arrow",onClick:function(){l(a-400)},style:{display:0===a?"none":"block"},children:Object(i.jsx)("i",{className:"lni lni-chevron-right"})})]})]})}},366:function(e,t,c){},367:function(e,t,c){},369:function(e,t,c){},370:function(e,t,c){},371:function(e,t,c){},372:function(e,t,c){},415:function(e,t,c){},422:function(e,t,c){"use strict";c.r(t);var i=c(0),s=(c(1),c(415),c(362)),n=c(364),a=c(363);t.default=function(){return Object(i.jsxs)("div",{children:[Object(i.jsx)(n.a,{}),Object(i.jsxs)("div",{className:"book-cont",children:[Object(i.jsx)("div",{className:"bg-img",style:{backgroundImage:"url(../../../tmp/tmp/1.jpg)"}}),Object(i.jsxs)("div",{className:"desc-img-cont",children:[Object(i.jsx)("div",{className:"img",children:Object(i.jsx)("img",{src:"../../../tmp/tmp3/1.jpg"})}),Object(i.jsxs)("div",{className:"desc",children:[Object(i.jsx)("div",{className:"head",children:"\u0647\u0627\u0631\u064a \u0628\u0648\u062a\u0631"}),Object(i.jsx)("div",{className:"year",children:"2020"}),Object(i.jsx)("div",{className:"rate-bdg",children:Object(i.jsxs)("div",{className:"imdb",children:[Object(i.jsx)("span",{children:"203,492 / "})," 7.5"]})}),Object(i.jsxs)("div",{class:"tags",children:[Object(i.jsx)("div",{class:"tag",children:"\u062f\u0631\u0627\u0645\u0627"}),Object(i.jsx)("div",{class:"tag",children:"\u0631\u0639\u0628"}),Object(i.jsx)("div",{class:"tag",children:"\u0627\u062b\u0627\u0631\u0629"}),Object(i.jsx)("div",{class:"tag",children:"\u062a\u0634\u0648\u064a\u0642"})]}),Object(i.jsx)("div",{className:"desc-txt",children:"\u064a\u0642\u0648\u0645 \u0634\u0631\u0637\u064a \u0641\u064a \u0628\u0644\u062f\u0629 \u062c\u0631\u064a\u0646 \u0647\u064a\u0644\u0632 \u0627\u0644\u0631\u064a\u0641\u064a\u0629 \u0628\u0645\u0633\u0627\u0639\u062f\u0629 (\u0633\u0648\u0646\u064a\u0643 \u0627\u0644\u0642\u0646\u0641\u0630) \u0639\u0644\u0649 \u0627\u0644\u0647\u0631\u0648\u0628 \u0645\u0646 \u0627\u0644\u062d\u0643\u0648\u0645\u0629 \u0627\u0644\u062a\u064a \u062a\u062a\u0637\u0644\u0639 \u0644\u0644\u0642\u0628\u0636 \u0639\u0644\u064a\u0647\u060c \u0644\u0643\u0646 \u0647\u0644 \u0647\u0630\u0627 \u0644\u0647 \u0633\u0628\u0628 \u0637\u0628\u064a\u0639\u064a\u061f \u0648\u0644\u0645 \u062a\u0637\u0627\u0631\u062f\u0647 \u0627\u0644\u062d\u0643\u0648\u0645\u0629 \u0645\u0646 \u0627\u0644\u0623\u0633\u0627\u0633\u061f"}),Object(i.jsxs)("div",{class:"btns",children:[Object(i.jsxs)("div",{class:"btn play-btn",children:[Object(i.jsx)("i",{class:"lni lni-eye"})," \u0642\u0631\u0627\u0621\u0629"]}),Object(i.jsxs)("div",{class:"btn add-btn",children:[Object(i.jsx)("i",{class:"lni lni-plus"})," \u0627\u0636\u0627\u0641\u0629 \u0644\u0644\u0642\u0627\u0626\u0645\u0629"]})]})]})]})]}),Object(i.jsx)("div",{style:{clear:"both",marginBottom:"5%"}}),Object(i.jsx)(s.a,{data:{title:"\u0643\u062a\u0628 \u0645\u0634\u0627\u0628\u0647\u0629"},size:"small"}),Object(i.jsx)(a.a,{})]})}}}]);