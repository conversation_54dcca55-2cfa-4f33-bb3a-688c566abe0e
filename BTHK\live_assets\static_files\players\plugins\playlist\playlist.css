.mejs__container.mejs__video,
.mejs-container.mejs-video {
    overflow: hidden;
}
.mejs__container.mejs__audio,
.mejs-container.mejs-audio {
    min-height: 200px;
}
.mejs__container.mejs__audio.mejs__no-playlist,
.mejs-container.mejs-audio.mejs__no-playlist {
    min-height: 60px;
}
.mejs__playlist-button,
.mejs-playlist-button {
    position: relative;
}
.mejs__playlist-button > button,
.mejs-playlist-button > button,
.mejs__next-button > button,
.mejs-next-button > button,
.mejs__prev-button > button,
.mejs-prev-button > button,
.mejs__loop-button > button,
.mejs-loop-button > button,
.mejs__shuffle-button > button,
.mejs-shuffle-button > button {
    background: url('playlist-controls.svg') transparent no-repeat;
}
.mejs__playlist-button > button,
.mejs-playlist-button > button {
    background-position: -80px 0;
}
.mejs__next-button > button,
.mejs-next-button > button {
    background-position: 0 0;
}
.mejs__prev-button > button,
.mejs-prev-button > button {
    background-position: -20px 0;
}
.mejs__loop-button > button,
.mejs-loop-button > button {
    background-position: -120px 0;
}
.mejs__shuffle-button > button,
.mejs-shuffle-button > button {
    background-position: -100px 0;
}
.mejs__loop-button.mejs__loop-off > button,
.mejs-loop-button.mejs-loop-off > button,
.mejs__shuffle-button.mejs__shuffle-off > button,
.mejs-shuffle-button.mejs-shuffle-off > button {
    opacity: 0.7;
}
.mejs__playlist-button > .mejs__playlist-selector,
.mejs-playlist-button > .mejs-playlist-selector {
    background: rgba(50, 50, 50, 0.7);
    border: solid 1px transparent;
    border-radius: 0;
    bottom: 100%;
    margin-right: -43px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    right: 50%;
    visibility: visible;
    width: 86px;
}

.mejs__playlist-selector-list,
.mejs-playlist-selector-list {
    height: 100%;
    list-style-type: none !important;
    margin: 0;
    overflow-y: auto;
    padding: 0;
}

.mejs__playlist-selector-list-item,
.mejs-playlist-selector-list-item {
    border-bottom: 1px solid #c8c8c8;
    border-collapse: collapse;
    cursor: pointer;
    display: table;
    list-style-type: none !important;
    position: relative;
    width: 100%;
}
.mejs__playlist-item-inner {
    display: table-row;
    overflow: hidden;
}

.mejs__playlist-selector-list-item:hover,
.mejs-playlist-selector-list-item:hover {
    background-color: rgb(200, 200, 200) !important;
    background-color: rgba(255, 255, 255, 0.4) !important;
}

.mejs__playlist-selector-list-item:focus,
.mejs-playlist-selector-list-item:focus {
    outline: none;
}

.mejs__playlist-selector-input,
.mejs-playlist-selector-input {
    left: -1000px;
    position: absolute;
}

.mejs__playlist-selector-label,
.mejs-playlist-selector-label {
    cursor: pointer;
    float: left;
    font-size: 1.3em;
    margin: 0;
}

.mejs__playlist-selector-label > span,
.mejs-playlist-selector-label > span {
    -webkit-background-clip: text;
    background-clip: text;
    background-color: #eb5802;
    background-image: -webkit-linear-gradient(top, #eb5802, #f3ad39);
    background-image: linear-gradient(to bottom, #eb5802, #f3ad39);
    color: transparent;
    font-size: 0.65em;
    vertical-align: middle;
}
.mejs__playlist-selected,
.mejs-playlist-selected {
    background-color: #3a3a3a;
}

.mejs__playlist-layer,
.mejs-playlist-layer {
    background: #222;
    bottom: 40px;
    color: #fff;
    font-size: 12px;
    height: calc(100% - 40px) !important;
    overflow: hidden;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    width: 33.33333% !important;
    z-index: 2;
}

.mejs__container.mejs__audio .mejs__playlist-layer,
.mejs-container.mejs-audio .mejs-playlist-layer {
    bottom: 85px;
    height: calc(100% - 85px) !important;
    overflow: auto;
    width: 100% !important;
}

.mejs__playlist-hidden,
.mejs-playlist-hidden {
    right: -33.33333%;
    -webkit-transition: 0.5s;
    transition: 0.5s;
}

.mejs__playlist-item-thumbnail,
.mejs-playlist-item-thumbnail {
    display: table-cell;
    padding: 7px 5px;
    vertical-align: top;
    width: 30%;
}
.mejs__container.mejs__audio .mejs__playlist-item-thumbnail,
.mejs-container.mejs-audio .mejs-playlist-item-thumbnail {
    padding: 5px;
    width: 15%;
}

.mejs__playlist-item-thumbnail > img,
.mejs-playlist-item-thumbnail > img {
    height: auto;
    width: 100%;
}
.mejs__playlist-item-content,
.mejs-playlist-item-content {
    display: table-cell;
    padding: 10px;
    width: 70%;
}
.mejs__container.mejs__audio .mejs__playlist-item-content,
.mejs-container.mejs-audio .mejs-playlist-item-content {
    margin: 0;
    width: 85%;
}
.mejs__playlist-item-description,
.mejs-playlist-item-description {
    clear: left;
    padding: 5px 0;
    text-align: left;
}
.mejs__playlist-current,
.mejs-playlist-current {
    background: #000;
    bottom: 34px;
    height: auto !important;
    left: 0;
    position: absolute;
    z-index: 3;
}
.mejs__playlist-current p,
.mejs-playlist-current p {
    color: #fff;
    font-size: 0.8em;
    margin: 15px 10px;
}
.mejs__playlist-current > img,
.mejs-playlist-current > img {
    float: left;
    margin: 5px 10px;
    max-height: 35px;
}
.mejs__playlist-current-title,
.mejs-playlist-current-title,
.mejs__playlist-current-description,
.mejs-playlist-current-description {
    font-style: italic;
}