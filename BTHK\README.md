# BTHK Live System

## نظام BTHK للبث المباشر - Node.js Edition

### متطلبات التشغيل:
- Node.js (الإصدار 14 أو أحدث)
- npm (يأتي مع Node.js)
- متصفح ويب حديث

### طريقة التشغيل:

#### الطريقة الأولى (Windows - الأسهل):
1. انقر نقراً مزدوجاً على ملف `start.bat`
2. سيتم تثبيت المتطلبات تلقائياً في المرة الأولى
3. انتظر حتى يبدأ النظام
4. افتح المتصفح واذهب إلى العناوين المعروضة

#### الطريقة الثانية (يدوياً):
1. افتح موجه الأوامر (Command Prompt) في مجلد النظام
2. اكتب الأمر: `npm install` (في المرة الأولى فقط)
3. ا<PERSON><PERSON><PERSON> الأمر: `node server.js`
4. ا<PERSON><PERSON><PERSON> Enter
5. افتح المتصفح واذهب إلى العناوين المعروضة

#### الطريقة الثالثة (للمطورين):
1. افتح موجه الأوامر في مجلد النظام
2. اكتب الأمر: `npm start`
3. للتطوير مع إعادة التشغيل التلقائي: `npm run dev`

### الوصول للنظام:
- **صفحة المشاهدة الرئيسية**: `http://localhost:3333`
- **لوحة التحكم الإدارية**: `http://localhost:3333/admin`
- **البورت المستخدم**: 3333

### الميزات:
- ✅ واجهة مشاهدة حديثة وسريعة الاستجابة
- ✅ لوحة تحكم شاملة لإدارة القنوات والملفات
- ✅ دعم رفع الملفات (حتى 500 ميجابايت)
- ✅ إحصائيات المشاهدة في الوقت الفعلي
- ✅ دعم أنواع مختلفة من البث (مباشر، ملفات، روابط خارجية)
- ✅ واجهة عربية كاملة
- ✅ تصميم متجاوب يعمل على جميع الأجهزة

### استخدام النظام:

#### إضافة قناة جديدة:
1. اذهب إلى لوحة التحكم: `http://localhost:3333/admin`
2. انقر على تبويب "القنوات"
3. املأ بيانات القناة الجديدة
4. انقر "إضافة القناة"

#### رفع ملف للبث:
1. اذهب إلى تبويب "الملفات" في لوحة التحكم
2. اسحب الملف إلى منطقة الرفع أو انقر للاختيار
3. بعد الرفع، انقر "استخدام كقناة" لإضافته كقناة بث

#### مشاهدة البث:
1. اذهب إلى صفحة المشاهدة: `http://localhost:3333`
2. اختر القناة من القائمة الجانبية
3. استمتع بالمشاهدة!

### ملاحظات فنية:
- النظام يستخدم قاعدة بيانات SQLite محلية
- الملفات المرفوعة تُحفظ في مجلد `assets/uploads`
- النظام يدعم WebSocket للتحديثات الفورية
- تم تطبيق إجراءات الأمان الأساسية

### استكشاف الأخطاء:
- تأكد من أن Node.js مثبت: `node --version`
- تأكد من أن npm يعمل: `npm --version`
- تأكد من أن البورت 3333 غير مستخدم
- في حالة وجود مشاكل، تحقق من رسائل الخطأ في نافذة الأوامر

### الدعم:
للحصول على الدعم الفني، يرجى التواصل مع فريق التطوير BTHK.
