# BTHK Live System

## نظام BTHK للبث المباشر

### متطلبات التشغيل:
- Python 3.x
- متصفح ويب حديث

### طريقة التشغيل:

#### الطريقة الأولى (Windows):
1. انقر نقراً مزدوجاً على ملف `start.bat`
2. انتظر حتى يبدأ النظام
3. افتح المتصفح واذهب إلى العنوان الذي سيظهر في النافذة

#### الطريقة الثانية (يدوياً):
1. افتح موجه الأوامر (Command Prompt) في مجلد النظام
2. اكتب الأمر: `python live.engine`
3. اضغط Enter
4. افتح المتصفح واذهب إلى العنوان المعروض

### الوصول للنظام:
- بعد تشغيل النظام، ستحصل على عنوان مثل: `http://localhost:8080`
- افتح هذا العنوان في المتصفح للوصول للواجهة الرئيسية
- للوصول لوحة التحكم: `http://localhost:8080/admin`

### ملاحظات:
- تأكد من أن Python مثبت على النظام
- تأكد من أن المنفذ 8080 غير مستخدم من برنامج آخر
- في حالة وجود مشاكل، تحقق من رسائل الخطأ في نافذة الأوامر

### الدعم:
للحصول على الدعم الفني، يرجى التواصل مع فريق التطوير.
