<!DOCTYPE html>
<html>
    {% include "admin/head.html" %}
  <body dir="rtl" class="noselect" >
        {% include "admin/mnu.html" %}
    
    
    <div class=" nopadding" >
        <div class="page">
            <div class="msg info">من هنا تستطيع التحكم بخيارات جهاز DVR لاضافتة للبث لاحقاً</div>
            <h4>اضافة جهاز DVR جديد</h4>
            <form action="/{{admin_panel_path}}/save/{{admin_page}}" id="saveForm" method="POST" enctype="multipart/form-data">
                <div class="content" >
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                اسم قناة ال DVR
                            </div>
                            <div class="in-e col-sm-10">
                                <input  name="dvr_name" id="dvr_name"   placeholder="اسم قناة الـDVR">
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                IP جهاز الـDVR
                            </div>
                            <div class="in-e col-sm-10">
                                <input  name="dvr_ip" id="dvr_ip"   placeholder="IP جهاز ال DVR">
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                اسم مستخدم الجهاز
                            </div>
                            <div class="in-e col-sm-10">
                                <input  name="dvr_user" id="dvr_user"   placeholder="اسم مستخدم جهاز الـDVR">
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                كلمة مرور الجهاز
                            </div>
                            <div class="in-e col-sm-10">
                                <input  name="dvr_pass" id="dvr_pass"   placeholder=" كلمة مرور جهاز الـ DVR">
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                الشركات المدعومة من قبل النظام 
                            </div>
                            <div class="in-e col-sm-10">
                                <select  name="DVR_COMPANY" id="DVR_COMPANY">
                                    <option value="DAHUA">DAHUA</option>
                                    <option value="TECH-COM">TECH-COM</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                منفذ القناة
                            </div>
                            <div class="in-e col-sm-10">
                                <select  name="dvr_num" id="dvr_num">
                                    <option value="1">قناة 1</option>
                                    <option value="2">قناة 2</option>
                                    <option value="3">قناة 3</option>
                                    <option value="4">قناة 4</option>
                                    <option value="5">قناة 5</option>
                                    <option value="6">قناة 6</option>
                                    <option value="7">قناة 7</option>
                                </select>
                            </div>
                        </div>
                    </div>
  
                    <hr class="hard">
                    <div class="input">
                        <div class="row nopadding">
                            <div class="sbmt-btn" id="saveServer">
                                <div class=" reg-icon icon  col-xs-4   fa  fa-plus "></div>
                                <div  class=" text   col-xs-8 ">اضافة</div>
                            </div>
                            <div class="colasbmbtn" id="testDVRCurrent" style="margin-left: 10px;">
                                
                                <div class=" reg-icon icon  col-xs-4   fa  fa-play   "></div>
                                <div  class=" text   col-xs-8 ">اختبار البث</div>
                                
                            </div>
                            <div class="colasbmbtn" id="startStaticIP" style="margin-left: 10px;width:256px">
                                
                                <div class=" reg-icon icon  col-xs-4   fa  fa-wrench   "></div>
                                <div  class=" text   col-xs-8 ">تحديث كرت الشبكه USB</div>
                                
                            </div>

                        </div>
                    </div>
                </div>
            </form>

            <div class="warppingTable">
                    
                    <table id="example" class="display" width="100%"></table>
                    <script>

                        
                            $(document).ready(function() {
                                const TABLE = "{{admin_page}}";
                                var table = $('#example').DataTable( {
                                    
                                    "processing": true,
                                    "serverSide": true,
                                    "ajax": "/{{admin_panel_path}}/get/"+TABLE,
                                    
                                    language :{
                                        "decimal":        "",
                                        "emptyTable":     "لايوجد اي بيانات في الجدول",
                                        "info":           "عرض _START_ الى _END_ من _TOTAL_ عناصر",
                                        "infoEmpty":      "عرض 0 الى 0 من 0 عناصر",
                                        "infoFiltered":   "(فلترة من _MAX_ مجموعة عناصر)",
                                        "infoPostFix":    "",
                                        "thousands":      ",",
                                        "lengthMenu":     "عرض _MENU_ عناصر",
                                        "loadingRecords": "تحميل...",
                                        "processing":     "معالجة...",
                                        "search":         "بحث:",
                                        "zeroRecords":    "لايوجد سجلات بحسب بحثك",
                                        "paginate": {
                                            "first":      "الاول",
                                            "last":       "الاخر",
                                            "next":       "التالي",
                                            "previous":   "السابق"
                                        },
                                        "aria": {
                                            "sortAscending":  ": تفعيل ترتيب العمود من الاصغر للاكبر",
                                            "sortDescending": ": تفعيل ترتيب العمود من الاكبر للاصغر"
                                        }
                                    },
                                    columns: [
                                        
                                        { 
                                            title: "id" ,
                                            render:function(data, type, row, meta) {
                                                return row.id;
                                            }
                                        },
                                        { 
                                            title: "اسم قناة ال DVR",
                                            render:function(data, type, row, meta) {
                                                
                                                return row.dvr_name;
                                            }
                                        },
                                        {
                                            title: "IP جهاز الـDVR" ,
                                            render:function(data, type, row, meta) {
                                                
                                                return row.dvr_ip;
                                            }
                                        },
                                        { 
                                            title: "اسم مستخدم الجهاز",
                                            render:function(data, type, row, meta) {
                                                
                                                return row.dvr_user;
                                            }
                                        },
                                        { 
                                            title: "كلمة مرور الجهاز",
                                            render:function(data, type, row, meta) {
                                                
                                                return row.dvr_pass;
                                            } 
                                        },
                                        
                                        { 
                                            title: "شركة الجهاز",
                                            render:function(data, type, row, meta) {
                                                
                                                return row.DVR_COMPANY || "TECH-COM";
                                            } 
                                        },
                                        { 
                                            title: "منفذ القناة",
                                            render:function(data, type, row, meta) {
                                                
                                                return row.dvr_num;
                                            } 
                                        },
                                        { 
                                            title: "تحكم",
                                            render:function(data, type, row, meta) {
                                                
                                                return "<i class='testit fa fa-play' dataid='"+row.id+"' ></i> <i class='deleteROW fa fa-times' dataid='"+row.id+"' ></i>";
                                            }
                                        }
                                    ],
                                    "columnDefs": [
                                        { 
                                            "width": "2%", "targets": 0,
                                            "width": "10%", "targets": 6
                                        }
                                    ],
                                    
                                } );
                                $('body').delegate('.deleteROW','click',function(){
                                    var id = $(this).attr("dataid");
                                    
                                    swal({
                                            title: "حذف هذا السجل : "+id,
                                            text: "هل انت متاكد من حذف هذا السجل  : "+id,
                                            type: "warning",
                                            showCancelButton: true,
                                            confirmButtonColor: "#DD6B55",
                                            confirmButtonText: "نعم",
                                            cancelButtonText: "لا",
                                            closeOnConfirm: true,
                                            html:true
                                        },
                                        function(isConfirm){
                                            if(!isConfirm)
                                                return ;
                                            $.ajax({url:"/{{admin_panel_path}}/delete/"+TABLE+"/"+id,success:function(){
                                                table.ajax.reload();
                                                setTimeout(function(){
                                                    swal("تم الحذف بنجاح", "تم حذف السجل بنجاح", "success");
                                                },300);
                                                
                                            }});
                                        }
                                    );
                                   
                                });
                               
                                $('#example tbody').on( 'click', 'tr', function () {
                                   // var id = $(this)[0].childNodes[0].innerHTML;
                                    //$.ajax({url:"/delete/"+id,success:function(){
                                    //    table.ajax.reload();
                                    //}});
                                } );
                            
                                $('#button').click( function () {
                                    table.row('.selected').remove().draw( false );
                                });
    
                                $("#saveServer").click(function(){
                                    var formData = new FormData($("#saveForm")[0]);
    
                                    $.ajax({
                                        url: $("#saveForm").attr("action"),
                                        type: 'POST',
                                        data: formData,
                                        success: function (data) {
                                           
                                            if(data.msg=="ok"){
                                                setTimeout(function(){
                                                    swal("تم الحفظ بنجاح", "تم حفظ بياناتك بنجاح", "success");
                                                },500);
                                                
                                                table.ajax.reload();
                                            }else{
                                                swal("خطاء", "حدث خطاء غير معروف!", "error");
                                            }
                                        },
                                        error:function(){
                                        
                                        },
                                        cache: false,
                                        contentType: false,
                                        processData: false
                                    });
                                })
                               
                            } );
                            // calc data
                            $('input[name=server_hls_time]').change(function() {
                                var valt = $(this).val();
                                var valn = $('input[name=server_hls_num]').val();
                                $('#setSUg').text(valt*valn);
                            });
                            $('input[name=server_hls_num]').change(function() {
                                var valn = $(this).val();
                                var valt = $('input[name=server_hls_time]').val();
                                $('#setSUg').text(valt*valn);
                            });
                            $('body').delegate('.testit','click',function(){
                                
                                var dataid = $(this).attr("dataid");
                                swal({
                                    title: "يتم الاختبار انتظر قليلاً",
                                    text: "يتم اختبار البث",
                                    icon: "info",
                                    buttons: false,
                                    timer: 30000,
                                        
                                });
                                $.ajax({
                                    url:"/{{admin_panel_path}}/testdvr/"+dataid,
                                    success:function(data){
                                        
                                       if(data.is_ok){
                                          
                                           swal("الاعدادات صحيحة", "تم تخطي الاختبار بنجاح", "success");
                                       }else{
                                           
                                           swal("تاكد من تركيب الجهاز", "خطاء في تخطي الاختبار", "error");
                                       }
                                       
                                       
                                    }
                                });
                                
                            });
                            
                            $("#startStaticIP").click(function(){
                                $.getJSON("/{{admin_panel_path}}/startStaticIP/",
                                    function(data){
                                        
                                        if(data.msg=="ok"){
                                            swal("الاعدادات صحيحة", "تم تشغيل منفذ ال USB بنجاح", "success");
                                        }else{
                                            swal("يوجد خطاء في الاعدادات", "تاكد من توصيل منفذ ال USB", "error");
                                        }   
                                    }
                                );
                            });
                            $("#testDVRCurrent").click(function(){
                                var dvr_ip = $("#dvr_ip").val();
                                var dvr_user = $("#dvr_user").val();
                                var dvr_pass = $("#dvr_pass").val();
                                var dvr_num = $("#dvr_num").val();
                                var DVR_COMPANY = $("#DVR_COMPANY").val();
                                
                                $(".fa-play").addClass("fa-spinner");
                                $(".fa-play").addClass("fa-spin");
                                $(".fa-play").removeClass("fa-play");
                                swal({
                                    title: "يتم الاختبار انتظر قليلاً",
                                    text: "يتم اختبار البث",
                                    icon: "info",
                                    buttons: false,
                                    timer: 30000,
                                        
                                });
                                $.ajax({
                                    
                                    url:"/{{admin_panel_path}}/checkDvr/"+dvr_ip+"/"+dvr_user+"/"+(dvr_pass||"null")+"/"+dvr_num+"/"+DVR_COMPANY,
                                    success:function(data){
                                      
                                       if(data.is_ok){
                                          
                                           swal("الاعدادات صحيحة", "تم تخطي الاختبار بنجاح", "success");
                                       }else{
                                           
                                           swal("يوجد خطاء في الاعدادات", "خطاء في تخطي الاختبار", "error");
                                       }
                                       $(".fa-spinner").addClass("fa-play");
                                       $(".fa-play").removeClass("fa-spinner");
                                       $(".fa-play").removeClass("fa-spin");
                                       
                                    }
                                });
                                
                            });
                        </script>
                </div>
        </div>
    </div>
    
    
    {% include "admin/footer.html" %}
  </body>
</html>
