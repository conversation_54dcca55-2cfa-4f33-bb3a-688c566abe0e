'use strict';/*!
 * This is a `i18n` language object.
 *
 * Czech
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.cs === undefined) {
		exports.cs = {
			'mejs.plural-form': 8,			
			'mejs.download-file': 'Stáhnout soubor',			
			'mejs.install-flash': 'Použív<PERSON><PERSON>, kter<PERSON> nemá Flash Player povolen nebo nainstalován. Zapněte plugin Flash Player nebo stáhněte nejnovější verzi z adresy https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': '<PERSON><PERSON><PERSON> obrazov<PERSON>',			
			'mejs.play': '<PERSON><PERSON><PERSON><PERSON><PERSON>t',
			'mejs.pause': 'Pozastavit',			
			'mejs.time-slider': 'Posuvný běžec nastavení času',
			'mejs.time-help-text': '<PERSON>užijte tlačítka se šipkami doleva / doprava pro posun o jednu vteřinu, tlačítka se šipkami nahoru / dolů pro posun o deset vteřin.',
			'mejs.live-broadcast' : 'Živé vysílání',			
			'mejs.volume-help-text': 'Použijte tlačítka se šipkami nahoru / dolů pro zesílení nebo zeslabení hlasitosti.',
			'mejs.unmute': 'Zapnout zvuk',
			'mejs.mute': 'Vypnout zvuk',
			'mejs.volume-slider': 'Posuvný běžec nastavení hlasitosti',			
			'mejs.video-player': 'Přehrávač videa',
			'mejs.audio-player': 'Přehrávač hudby',			
			'mejs.captions-subtitles': 'Titulky',
			'mejs.captions-chapters': 'Kapitoly',
			'mejs.none': 'Žádný',
			'mejs.afrikaans': 'Afrikánština',
			'mejs.albanian': 'Albánský',
			'mejs.arabic': 'Arabština',
			'mejs.belarusian': 'Běloruské',
			'mejs.bulgarian': 'Bulharský',
			'mejs.catalan': 'Katalánština',
			'mejs.chinese': 'čínština',
			'mejs.chinese-simplified': 'Zjednodušená čínština)',
			'mejs.chinese-traditional': 'Čínština (tradiční)',
			'mejs.croatian': 'Chorvatský',
			'mejs.czech': 'čeština',
			'mejs.danish': 'Dánština',
			'mejs.dutch': 'Holandský',
			'mejs.english': 'Angličtina',
			'mejs.estonian': 'Estonština',
			'mejs.filipino': 'Filipino',
			'mejs.finnish': 'Finština',
			'mejs.french': 'Francouzština',
			'mejs.galician': 'Galicijština',
			'mejs.german': 'Němec',
			'mejs.greek': 'řecký',
			'mejs.haitian-creole': 'Haitian kreolský',
			'mejs.hebrew': 'Hebrejština',
			'mejs.hindi': 'Hindština',
			'mejs.hungarian': 'Maďarský',
			'mejs.icelandic': 'Islandský',
			'mejs.indonesian': 'Indonéština',
			'mejs.irish': 'Irština',
			'mejs.italian': 'Italština',
			'mejs.japanese': 'Japonský',
			'mejs.korean': 'Korejština',
			'mejs.latvian': 'Lotyšský',
			'mejs.lithuanian': 'Lithuanian',
			'mejs.macedonian': 'Makedonština',
			'mejs.malay': 'Malay',
			'mejs.maltese': 'Maltština',
			'mejs.norwegian': 'Norština',
			'mejs.persian': 'Peršan',
			'mejs.polish': 'Polština',
			'mejs.portuguese': 'Portugalština',
			'mejs.romanian': 'Rumunština',
			'mejs.russian': 'Ruština',
			'mejs.serbian': 'Srbština',
			'mejs.slovak': 'Slovák',
			'mejs.slovenian': 'Slovinský',
			'mejs.spanish': 'španělština',
			'mejs.swahili': 'Svahilský',
			'mejs.swedish': 'švédský',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thajština',
			'mejs.turkish': 'Turečtina',
			'mejs.ukrainian': 'Ukrajinština',
			'mejs.vietnamese': 'Vietnamština',
			'mejs.welsh': 'Velština',
			'mejs.yiddish': 'Jidiš'
		};
	}
})(mejs.i18n);