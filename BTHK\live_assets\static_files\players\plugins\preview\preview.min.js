/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,o,n){function i(r,d){if(!o[r]){if(!t[r]){var s="function"==typeof require&&require;if(!d&&s)return s(r,!0);if(a)return a(r,!0);var u=new Error("Cannot find module '"+r+"'");throw u.code="MODULE_NOT_FOUND",u}var l=o[r]={exports:{}};t[r][0].call(l.exports,function(e){var o=t[r][1][e];return i(o||e)},l,l.exports,e,t,o,n)}return o[r].exports}for(var a="function"==typeof require&&require,r=0;r<n.length;r++)i(n[r]);return i}({1:[function(e,t,o){"use strict";Object.assign(mejs.MepDefaults,{previewMode:!1,muteOnPreviewMode:!0,fadeInAudioStart:0,fadeInAudioInterval:0,fadeOutAudioStart:0,fadeOutAudioInterval:0,fadePercent:.02,pauseOnlyOnPreview:!1,delayPreview:0}),Object.assign(MediaElementPlayer.prototype,{buildpreview:function(e){var t=!1,o=!1,n=void 0,i=!1,a=this,r=function(){if(a.options.fadeInAudioInterval&&(Math.floor(a.media.currentTime)<a.options.fadeIntAudioStart&&(a.media.setVolume(0),a.media.setMuted(!0)),Math.floor(a.media.currentTime)===a.options.fadeInAudioStart)){t=!0;var e=0,o=a.options.fadeInAudioInterval,n=setInterval(function(){e<1?((e+=a.options.fadePercent)>1&&(e=1),a.media.setVolume(e.toFixed(2))):(clearInterval(n),n=null,a.media.setMuted(!1),setTimeout(function(){t=!1},300))},o)}},d=function(){if(a.options.fadeOutAudioInterval&&(Math.floor(a.media.currentTime)<a.options.fadeOutAudioStart&&(a.media.setVolume(1),a.media.setMuted(!1)),Math.floor(a.media.currentTime)===a.options.fadeOutAudioStart)){o=!0;var e=1,t=a.options.fadeOutAudioInterval,n=setInterval(function(){e>0?((e-=a.options.fadePercent)<0&&(e=0),a.media.setVolume(e.toFixed(2))):(clearInterval(n),n=null,a.media.setMuted(!1),setTimeout(function(){o=!1},300))},t)}};a.options.muteOnPreviewMode||a.options.fadeInAudioInterval?(a.media.setVolume(0),a.media.setMuted(!0)):a.options.fadeOutAudioInterval&&(a.media.setVolume(1),a.media.setMuted(!1)),a.media.addEventListener("timeupdate",function(){t?a.media.removeEventListener("timeupdate",r):o?a.media.removeEventListener("timeupdate",d):(r(),d())}),e.isVideo&&(document.body.addEventListener("mouseover",function(e){e.target===a.container||e.target.closest("."+a.options.classPrefix+"container")?(i=!0,a.container.querySelector("."+a.options.classPrefix+"overlay-loading").parentNode.style.display="flex",a.container.querySelector("."+a.options.classPrefix+"overlay-play").style.display="none",a.media.paused?n=setTimeout(function(){i?a.media.play():(clearTimeout(n),n=null),a.container.querySelector("."+a.options.classPrefix+"overlay-loading").parentNode.style.display="none"},a.options.delayPreview):a.container.querySelector("."+a.options.classPrefix+"overlay-loading").parentNode.style.display="none"):(i=!1,clearTimeout(n),n=null,a.media.paused||a.media.pause(),a.container.querySelector("."+a.options.classPrefix+"overlay-loading").parentNode.style.display="none")}),document.body.addEventListener("mouseout",function(e){e.target===a.container||e.target.closest("."+a.options.classPrefix+"container")||(i=!1,a.container.querySelector("."+a.options.classPrefix+"overlay-loading").parentNode.style.display="none",a.media.paused||(a.media.pause(),a.options.pauseOnlyOnPreview||a.media.setCurrentTime(0)),clearTimeout(n),n=null)}),window.addEventListener("scroll",function(){i=!1,a.container.querySelector("."+a.options.classPrefix+"overlay-loading").parentNode.style.display="none",a.media.paused||a.media.pause()}))}})},{}]},{},[1]);