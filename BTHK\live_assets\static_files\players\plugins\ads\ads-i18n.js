'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.ad-skip'] = 'Salta publicitat';
	mejs.i18n.ca['mejs.ad-skip-info'] = ['Salta en 1 segon', 'Salta en %1 segons'];
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.ad-skip'] = 'Přeskočit reklamu';
	mejs.i18n.cs['mejs.ad-skip-info'] = ['Přeskočit za 1 sekundu', 'Přeskočte za %1 sekundy', 'Přeskočte za %1 sekund'];
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.ad-skip'] = 'Werbung überspringen';
	mejs.i18n.de['mejs.ad-skip-info'] = ['Überspringen in 1 Sekunde', 'Überspringen in %1 Sekunden'];
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.ad-skip'] = 'Saltar publicidad';
	mejs.i18n.es['mejs.ad-skip-info'] = ['Saltar en 1 segundo', 'Saltar en %1 segundos'];
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.ad-skip'] = 'پرش تبلیغ';
	mejs.i18n.fa['mejs.ad-skip-info'] = 'پرش در1% ثانیه';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.ad-skip'] = 'Passer la publicité';
	mejs.i18n.fr['mejs.ad-skip-info'] = 'Passer la publicité dans %1 secondes';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.ad-skip'] = 'Preskoči oglas';
	mejs.i18n.hr['mejs.ad-skip-info'] = ['Preskoči za 1 sekundu', 'Preskoči za %1 sekunde', 'Preskoči za %1 sekundi'];
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.ad-skip'] = 'Hirdetés átugrása';
	mejs.i18n.hu['mejs.ad-skip-info'] = ['Ugrás 1 másodperc alatt', 'Ugrás %1 másodpercen belül'];
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.ad-skip'] = 'Salta annuncio';
	mejs.i18n.it['mejs.ad-skip-info'] = ['Salta in 1 secondo', 'Salta in %1 secondi'];
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.ad-skip'] = '広告をスキップ';
	mejs.i18n.ja['mejs.ad-skip-info'] = '%1秒でスキップする';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.ad-skip'] = '광고를 건너 뛸 수';
	mejs.i18n.ko['mejs.ad-skip-info'] = '%1 초 후 건너 뛰기';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.ad-skip'] = 'Ad overslaan';
	mejs.i18n.nl['mejs.ad-skip-info'] = 'Overslaan in %1 seconden';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.ad-skip'] = 'Pomiń reklamę';
	mejs.i18n.pl['mejs.ad-skip-info'] = ['Pomiń za sekundę', 'Pomiń za %1 sekundy', 'Pomiń za %1 sekund'];
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.ad-skip'] = 'Pular Anúncio';
	mejs.i18n.pt['mejs.ad-skip-info'] = ['Pular em 1 segundo', 'Pular em %1 segundos'];
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.ad-skip'] = 'Ștergeți anunțul';
	mejs.i18n.ro['mejs.ad-skip-info'] = ['Treci peste 1 secundă', 'Treci peste %d secunde'];
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.ad-skip'] = 'Пропустить рекламу';
	mejs.i18n.ru['mejs.ad-skip-info'] = ['Пропустить через %1 секунду', 'Пропустить через %1 секунды', 'Пропустить через %1 секунд'];
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.ad-skip'] = 'Preskočiť reklamu';
	mejs.i18n.sk['mejs.ad-skip-info'] = ['Preskočiť za 1 sekundu', 'Preskočte za %1 sekundy', 'Preskočte za %1 sekúnd'];
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.ad-skip'] = 'Hoppa över reklam';
	mejs.i18n.sv['mejs.ad-skip-info'] = ['Hoppa in 1 second', 'Hoppa in %1 sekunder'];
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.ad-skip'] = 'Пропустити рекламу';
	mejs.i18n.uk['mejs.ad-skip-info'] = ['Пропустити через %1 секунду', 'Пропустити через %1 секунди', 'Пропустити через %1 секунд'];
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.ad-skip'] = '跳過廣告';
	mejs.i18n.zh['mejs.ad-skip-info'] = '跳過%1秒';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.ad-skip'] = '跳过广告';
	mejs.i18n['zh-CN']['mejs.ad-skip-info'] = '跳过%1秒';
}