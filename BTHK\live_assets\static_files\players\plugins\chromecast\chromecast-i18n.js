'use strict';

if (mejs.i18n.ca !== undefined) {
	// mejs.i18n.ca["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.ca["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.cs !== undefined) {
	// mejs.i18n.cs["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.cs["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.de !== undefined) {
	// mejs.i18n.de["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.de["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.es !== undefined) {
	// mejs.i18n.es["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.es["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.fr !== undefined) {
	// mejs.i18n.fr["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.fr["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.hr !== undefined) {
	// mejs.i18n.hr["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.hr["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.hu !== undefined) {
	// mejs.i18n.hu["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.hu["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.it !== undefined) {
	// mejs.i18n.it["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.it["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.ja !== undefined) {
	// mejs.i18n.ja["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.ja["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.ko !== undefined) {
	// mejs.i18n.ko["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.ko["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.nl !== undefined) {
	// mejs.i18n.nl["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.nl["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.pl !== undefined) {
	// mejs.i18n.pl["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.pl["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.pt !== undefined) {
	// mejs.i18n.pt["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.pt["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n['pt-BR'] !== undefined) {
	// mejs.i18n['pt-BR']["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n['pt-BR']["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.ro !== undefined) {
	// mejs.i18n.ro["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.ro["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.ru !== undefined) {
	// mejs.i18n.ru["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.ru["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.sk !== undefined) {
	// mejs.i18n.sk["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.sk["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.sv !== undefined) {
	// mejs.i18n.sv["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.sv["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.uk !== undefined) {
	// mejs.i18n.uk["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.uk["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n.zh !== undefined) {
	// mejs.i18n.zh["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n.zh["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}
if (mejs.i18n['zh-CN'] !== undefined) {
	// mejs.i18n['zh-CN']["mejs.chromecast-title"] = "Chromecast";
	// mejs.i18n['zh-CN']["mejs.chromecast-subtitle"] = "Chromecast Subtitle";
}