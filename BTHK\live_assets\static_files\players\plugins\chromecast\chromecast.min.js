/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,a,r){function i(o,n){if(!a[o]){if(!t[o]){var c="function"==typeof require&&require;if(!n&&c)return c(o,!0);if(s)return s(o,!0);var l=new Error("Cannot find module '"+o+"'");throw l.code="MODULE_NOT_FOUND",l}var u=a[o]={exports:{}};t[o][0].call(u.exports,function(e){var a=t[o][1][e];return i(a||e)},u,u.exports,e,t,a,r)}return a[o].exports}for(var s="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,a){"use strict";var r=function(e){return e&&e.__esModule?e:{default:e}}(e(2));mejs.i18n.en["mejs.chromecast-legend"]="Casting to:",Object.assign(mejs.MepDefaults,{castTitle:null,castAppID:null,castPolicy:"origin",castEnableTracks:!1,castIsLive:!1}),Object.assign(MediaElementPlayer.prototype,{buildchromecast:function(e,t,a,r){var i=this,s=document.createElement("div"),o=mejs.Utils.isString(i.options.castTitle)?i.options.castTitle:"Chromecast";if(e.chromecastLayer=document.createElement("div"),e.chromecastLayer.className=i.options.classPrefix+"chromecast-layer "+i.options.classPrefix+"layer",e.chromecastLayer.innerHTML='<div class="'+i.options.classPrefix+'chromecast-info"></div>',e.chromecastLayer.style.display="none",a.insertBefore(e.chromecastLayer,a.firstChild),s.className=i.options.classPrefix+"button "+i.options.classPrefix+"chromecast-button",s.innerHTML='<button type="button" is="google-cast-button" aria-controls="'+i.id+'" title="'+o+'" aria-label="'+o+'" tabindex="0"></button>',s.style.display="none",i.addControlElement(s,"chromecast"),i.castButton=s,e.chromecastLayer.innerHTML='<div class="'+i.options.classPrefix+'chromecast-container"><span class="'+i.options.classPrefix+'chromecast-icon"></span><span class="'+i.options.classPrefix+'chromecast-info">'+mejs.i18n.t("mejs.chromecast-legend")+' <span class="device"></span></span></div>',r.originalNode.getAttribute("poster")&&(e.chromecastLayer.innerHTML+='<img src="'+r.originalNode.getAttribute("poster")+'" width="100%" height="100%">',e.chromecastLayer.querySelector("img").addEventListener("click",function(){if(e.options.clickToPlayPause){var t=i.container.querySelector("."+i.options.classPrefix+"overlay-button"),a=t.getAttribute("aria-pressed");e.paused?e.play():e.pause(),t.setAttribute("aria-pressed",!!a),e.container.focus()}})),window.__onGCastApiAvailable=function(e){var t=mejs.Utils.getTypeFromFile(r.originalNode.src).toLowerCase(),a=t&&["application/x-mpegurl","application/vnd.apple.mpegurl","application/dash+xml","video/mp4","audio/mp3","audio/mp4"].indexOf(t)>-1;e&&a&&i._initializeCastPlayer()},window.cast){var n=i.controls.querySelector("."+i.options.classPrefix+"chromecast-button>button");return n&&"none"!==n.style.display&&(i.controls.querySelector("."+i.options.classPrefix+"chromecast-button").style.display=""),void i._initializeCastPlayer()}mejs.Utils.loadScript("https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1")},cleanchromecast:function(e){if(window.cast){var t=cast.framework.CastContext.getInstance().getCurrentSession();t&&t.endSession(!0)}e.castButton&&e.castButton.remove(),e.chromecastLayer&&e.chromecastLayer.remove()},_initializeCastPlayer:function(){var e=this,t=void 0;switch(this.options.castPolicy){case"tab":t="TAB_AND_ORIGIN_SCOPED";break;case"page":t="PAGE_SCOPED";break;default:t="ORIGIN_SCOPED"}var a=cast.framework.CastContext.getInstance(),r=a.getCurrentSession();if(a.setOptions({receiverApplicationId:e.options.castAppID||chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID,autoJoinPolicy:chrome.cast.AutoJoinPolicy[t]}),a.addEventListener(cast.framework.CastContextEventType.CAST_STATE_CHANGED,e._checkCastButtonStatus.bind(e)),e.remotePlayer=new cast.framework.RemotePlayer,e.remotePlayerController=new cast.framework.RemotePlayerController(e.remotePlayer),e.remotePlayerController.addEventListener(cast.framework.RemotePlayerEventType.IS_CONNECTED_CHANGED,e._switchToCastPlayer.bind(this)),r){var i=a.getCastState(),s=e.controls.querySelector("."+e.options.classPrefix+"chromecast-button");s&&i===cast.framework.CastState.NO_DEVICES_AVAILABLE?s.style.display="none":s&&(e.chromecastLayer&&(e.chromecastLayer.style.display=i===cast.framework.CastState.CONNECTED?"":"none"),s.style.display=""),e._switchToCastPlayer()}},_checkCastButtonStatus:function(e){var t=this,a=t.controls.querySelector("."+t.options.classPrefix+"chromecast-button");a&&e.castState===cast.framework.CastState.NO_DEVICES_AVAILABLE?a.style.display="none":a&&(t.chromecastLayer&&(t.chromecastLayer.style.display=e.castState===cast.framework.CastState.CONNECTED?"":"none"),a.style.display=""),setTimeout(function(){t.setPlayerSize(t.width,t.height),t.setControlsSize()},0)},_switchToCastPlayer:function(){var e=this;e.proxy&&e.proxy.pause(),cast&&cast.framework&&(cast.framework.CastContext.getInstance().addEventListener(cast.framework.CastContextEventType.CAST_STATE_CHANGED,e._checkCastButtonStatus.bind(e)),e.remotePlayer.isConnected)?e._setupCastPlayer():e._setDefaultPlayer()},_setupCastPlayer:function(){var e=this,t=cast.framework.CastContext.getInstance().getCurrentSession(),a=e.layers.querySelector("."+e.options.classPrefix+"chromecast-info");!0!==e.loadedChromecast&&(e.loadedChromecast=!0,e.proxy=new r.default(e.remotePlayer,e.remotePlayerController,e.media,e.options),a&&(a.querySelector(".device").innerText=t.getCastDevice().friendlyName),e.chromecastLayer&&(e.chromecastLayer.style.display=""),!0===e.options.castEnableTracks&&function(){var a=void 0!==e.captionsButton?e.captionsButton.querySelectorAll("input[type=radio]"):null;if(null!==a)for(var r=0,i=a.length;r<i;r++)!function(e,r){a[e].addEventListener("click",function(){var r=parseInt(a[e].id.replace(/^.*?track_(\d+)_.*$/,"$1")),i="none"===a[e].value?[]:[r],s=new chrome.cast.media.EditTracksInfoRequest(i);t.getMediaSession().editTracksInfo(s,function(){},function(e){console.error(e)})})}(r)}(),e.media.addEventListener("loadedmetadata",function(){-1===["SESSION_ENDING","SESSION_ENDED","NO_SESSION"].indexOf(t.getSessionState())&&e.proxy instanceof DefaultPlayer&&(e.proxy.pause(),e.proxy=new r.default(e.remotePlayer,e.remotePlayerController,e.media,e.options))}),e.media.addEventListener("timeupdate",function(){e.currentMediaTime=e.getCurrentTime()}))}})},{2:2}],2:[function(e,t,a){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(a,"__esModule",{value:!0});var i=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),s=function(){function e(t,a,i,s){r(this,e);var o=this;return o.player=t,o.controller=a,o.media=i,o.endedMedia=!1,o.enableTracks=s.castEnableTracks,o.isLive=s.castIsLive,o.controller.addEventListener(cast.framework.RemotePlayerEventType.IS_PAUSED_CHANGED,function(){o.paused?o.pause():o.play(),o.endedMedia=!1}),o.controller.addEventListener(cast.framework.RemotePlayerEventType.IS_MUTED_CHANGED,function(){o.setMuted(o.player.isMuted),o.volume=0}),o.controller.addEventListener(cast.framework.RemotePlayerEventType.IS_MEDIA_LOADED_CHANGED,function(){var e=mejs.Utils.createEvent("loadedmetadata",o.media);o.media.dispatchEvent(e)}),o.controller.addEventListener(cast.framework.RemotePlayerEventType.VOLUME_LEVEL_CHANGED,function(){o.volume=o.player.volumeLevel;var e=mejs.Utils.createEvent("volumechange",o.media);o.media.dispatchEvent(e)}),o.controller.addEventListener(cast.framework.RemotePlayerEventType.DURATION_CHANGED,function(){var e=mejs.Utils.createEvent("timeupdate",o.media);o.media.dispatchEvent(e)}),o.controller.addEventListener(cast.framework.RemotePlayerEventType.CURRENT_TIME_CHANGED,function(){var e=mejs.Utils.createEvent("timeupdate",o.media);o.media.dispatchEvent(e),!o.isLive&&o.getCurrentTime()>=o.getDuration()&&(o.endedMedia=!0,setTimeout(function(){var e=mejs.Utils.createEvent("ended",o.media);o.media.dispatchEvent(e)},50))}),o.controller.addEventListener(cast.framework.RemotePlayerEventType.IS_MUTED_CHANGED,function(){o.setMuted(o.player.isMuted)}),o.load(),o}return i(e,[{key:"getSrc",value:function(){return this.media.originalNode.src}},{key:"setSrc",value:function(e){this.media.originalNode.src="string"==typeof e?e:e[0].src,this.load()}},{key:"setCurrentTime",value:function(e){this.player.currentTime=e,this.controller.seek();var t=mejs.Utils.createEvent("timeupdate",this.media);this.media.dispatchEvent(t)}},{key:"getCurrentTime",value:function(){return this.player.currentTime}},{key:"getDuration",value:function(){return this.player.duration}},{key:"setVolume",value:function(e){this.player.volumeLevel=e,this.controller.setVolumeLevel();var t=mejs.Utils.createEvent("volumechange",this.media);this.media.dispatchEvent(t)}},{key:"getVolume",value:function(){return this.player.volumeLevel}},{key:"play",value:function(){if(this.player.isPaused){this.controller.playOrPause();var e=mejs.Utils.createEvent("play",this.media);this.media.dispatchEvent(e)}}},{key:"pause",value:function(){if(!this.player.isPaused){this.controller.playOrPause();var e=mejs.Utils.createEvent("pause",this.media);this.media.dispatchEvent(e)}}},{key:"load",value:function(){var e=this,t=this.media.originalNode.src,a=mejs.Utils.getTypeFromFile(t),r=new chrome.cast.media.MediaInfo(t,a),i=cast.framework.CastContext.getInstance().getCurrentSession();if(t!==window.location.href&&i){if(!0===e.enableTracks){for(var s=[],o=e.media.originalNode.children,n=1,c=0,l=o.length;c<l;c++){var u=o[c];if("track"===u.tagName.toLowerCase()&&("subtitles"===u.getAttribute("kind")||"captions"===u.getAttribute("kind"))){var d=new chrome.cast.media.Track(n,chrome.cast.media.TrackType.TEXT);d.trackContentId=mejs.Utils.absolutizeUrl(u.getAttribute("src")),d.trackContentType="text/vtt",d.subtype=chrome.cast.media.TextTrackType.SUBTITLES,d.name=u.getAttribute("label"),d.language=u.getAttribute("srclang"),d.customData=null,s.push(d),n++}}r.textTrackStyle=new chrome.cast.media.TextTrackStyle,r.tracks=s}if(r.metadata=new chrome.cast.media.GenericMediaMetadata,r.streamType=e.isLive?chrome.cast.media.StreamType.LIVE:chrome.cast.media.StreamType.BUFFERED,r.customData=null,r.duration=null,r.currentTime=e.isLive?1/0:0,e.media.originalNode.getAttribute("data-cast-title")&&(r.metadata.title=e.media.originalNode.getAttribute("data-cast-title")),e.media.originalNode.getAttribute("data-cast-description")&&(r.metadata.subtitle=e.media.originalNode.getAttribute("data-cast-description")),e.media.originalNode.getAttribute("poster")||e.media.originalNode.getAttribute("data-cast-poster")){var m=e.media.originalNode.getAttribute("poster")||e.media.originalNode.getAttribute("data-cast-poster");r.metadata.images=[{url:mejs.Utils.absolutizeUrl(m)}]}var p=new chrome.cast.media.LoadRequest(r);i.loadMedia(p).then(function(){var t=e.media.originalNode.currentTime;e.setCurrentTime(t),e.play();var a=mejs.Utils.createEvent("play",e.media);e.media.dispatchEvent(a)},function(t){e._getErrorMessage(t)})}}},{key:"setMuted",value:function(e){var t=this;!0!==e||this.player.isMuted?!1===e&&this.player.isMuted&&this.controller.muteOrUnmute():this.controller.muteOrUnmute(),setTimeout(function(){var e=mejs.Utils.createEvent("volumechange",t.media);t.media.dispatchEvent(e)},50)}},{key:"canPlayType",value:function(e){return~["application/x-mpegurl","vnd.apple.mpegurl","application/dash+xml","video/mp4"].indexOf(e)}},{key:"_getErrorMessage",value:function(e){var t=e.description?" : "+e.description:".",a=void 0;switch(e.code){case chrome.cast.ErrorCode.API_NOT_INITIALIZED:a="The API is not initialized"+t;break;case chrome.cast.ErrorCode.CANCEL:a="The operation was canceled by the user"+t;break;case chrome.cast.ErrorCode.CHANNEL_ERROR:a="A channel to the receiver is not available"+t;break;case chrome.cast.ErrorCode.EXTENSION_MISSING:a="The Cast extension is not available"+t;break;case chrome.cast.ErrorCode.INVALID_PARAMETER:a="The parameters to the operation were not valid"+t;break;case chrome.cast.ErrorCode.RECEIVER_UNAVAILABLE:a="No receiver was compatible with the session request"+t;break;case chrome.cast.ErrorCode.SESSION_ERROR:a="A session could not be created, or a session was invalid"+t;break;case chrome.cast.ErrorCode.TIMEOUT:a="The operation timed out"+t;break;default:a="Unknown error: "+e}console.error(a)}},{key:"paused",get:function(){return this.player.isPaused}},{key:"muted",set:function(e){this.setMuted(e)},get:function(){return this.player.isMuted}},{key:"ended",get:function(){return this.endedMedia}},{key:"readyState",get:function(){return this.media.originalNode.readyState}},{key:"currentTime",set:function(e){this.setCurrentTime(e)},get:function(){return this.getCurrentTime()}},{key:"duration",get:function(){return this.getDuration()}},{key:"volume",set:function(e){this.setVolume(e)},get:function(){return this.getVolume()}},{key:"src",set:function(e){this.setSrc(e)},get:function(){return this.getSrc()}}]),e}();a.default=s,window.ChromecastPlayer=s},{}]},{},[1,2]);