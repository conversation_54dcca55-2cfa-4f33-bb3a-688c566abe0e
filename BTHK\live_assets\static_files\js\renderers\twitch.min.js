/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,n,r){function i(s,o){if(!n[s]){if(!t[s]){var u="function"==typeof require&&require;if(!o&&u)return u(s,!0);if(a)return a(s,!0);var l=new Error("Cannot find module '"+s+"'");throw l.code="MODULE_NOT_FOUND",l}var d=n[s]={exports:{}};t[s][0].call(d.exports,function(e){var n=t[s][1][e];return i(n||e)},d,d.exports,e,t,n,r)}return n[s].exports}for(var a="function"==typeof require&&require,s=0;s<r.length;s++)i(r[s]);return i}({1:[function(e,t,n){"use strict";var r={promise:null,load:function(e){"undefined"!=typeof Twitch?r.promise=new Promise(function(e){e()}).then(function(){r._createPlayer(e)}):(r.promise=r.promise||mejs.Utils.loadScript("https://player.twitch.tv/js/embed/v1.js"),r.promise.then(function(){r._createPlayer(e)}))},_createPlayer:function(e){var t=new Twitch.Player(e.id,e);window["__ready__"+e.id](t)},getTwitchId:function(e){var t="";return e.indexOf("?")>0?""===(t=r.getTwitchIdFromParam(e))&&(t=r.getTwitchIdFromUrl(e)):t=r.getTwitchIdFromUrl(e),t},getTwitchIdFromParam:function(e){if(void 0===e||null===e||!e.trim().length)return null;for(var t=e.split("?")[1].split("&"),n="",r=0,i=t.length;r<i;r++){var a=t[r].split("=");if(~a[0].indexOf("channel")){n=a[1];break}if(~a[0].indexOf("video")){n="v"+a[1];break}}return n},getTwitchIdFromUrl:function(e){if(void 0===e||null===e||!e.trim().length)return null;var t=(e=e.split("?")[0]).substring(e.lastIndexOf("/")+1);return/^\d+$/i.test(t)?"v"+t:t},getTwitchType:function(e){return/^v\d+/i.test(e)?"video":"channel"}},i={name:"twitch_iframe",options:{prefix:"twitch_iframe"},canPlayType:function(e){return~["video/twitch","video/x-twitch"].indexOf(e.toLowerCase())},create:function(e,t,n){function i(t){for(var n=0,r=t.length;n<r;n++){var i=mejs.Utils.createEvent(t[n],a);e.dispatchEvent(i)}}var a={},s=[],o=r.getTwitchId(n[0].src),u=null,l=!0,d=!1,c=!1,h=1,f=1/0,p=0;a.options=t,a.id=e.id+"_"+t.prefix,a.mediaElement=e;for(var v=mejs.html5media.properties,m=0,g=v.length;m<g;m++)!function(t){var n=""+t.substring(0,1).toUpperCase()+t.substring(1);a["get"+n]=function(){if(null!==u){switch(t){case"currentTime":return p=u.getCurrentTime();case"duration":return f=u.getDuration();case"volume":return h=u.getVolume();case"paused":return l=u.isPaused();case"ended":return d=u.getEnded();case"muted":return u.getMuted();case"buffered":return{start:function(){return 0},end:function(){return 0},length:1};case"src":return"channel"===r.getTwitchType(o)?u.getChannel():u.getVideo();case"readyState":return 4}return null}return null},a["set"+n]=function(n){if(null!==u)switch(t){case"src":var i="string"==typeof n?n:n[0].src,l=r.getTwitchId(i);"channel"===r.getTwitchType(o)?u.setChannel(l):u.setVideo(l);break;case"currentTime":u.seek(n),setTimeout(function(){var t=mejs.Utils.createEvent("timeupdate",a);e.dispatchEvent(t)},50);break;case"muted":u.setMuted(n),setTimeout(function(){var t=mejs.Utils.createEvent("volumechange",a);e.dispatchEvent(t)},50);break;case"volume":h=n,u.setVolume(n),setTimeout(function(){var t=mejs.Utils.createEvent("volumechange",a);e.dispatchEvent(t)},50);break;case"readyState":var d=mejs.Utils.createEvent("canplay",a);e.dispatchEvent(d)}else s.push({type:"set",propName:t,value:n})}}(v[m]);for(var y=mejs.html5media.methods,w=0,T=y.length;w<T;w++)!function(e){a[e]=function(){if(null!==u)switch(e){case"play":return l=!1,u.play();case"pause":return l=!0,u.pause();case"load":return null}else s.push({type:"call",methodName:e})}}(y[w]);window["__ready__"+a.id]=function(t){if(e.twitchPlayer=u=t,s.length)for(var n=0,r=s.length;n<r;n++){var o=s[n];if("set"===o.type){var h=o.propName,f=""+h.substring(0,1).toUpperCase()+h.substring(1);a["set"+f](o.value)}else"call"===o.type&&a[o.methodName]()}var p=document.getElementById(a.id).firstChild;p.style.width="100%",p.style.height="100%";for(var v=["mouseover","mouseout"],m=0,g=v.length;m<g;m++)p.addEventListener(v[m],function(t){var n=mejs.Utils.createEvent(t.type,a);e.dispatchEvent(n)},!1);var y=void 0;u.addEventListener(Twitch.Player.READY,function(){l=!1,d=!1,i(["rendererready","loadedmetadata","loadeddata","canplay"])}),u.addEventListener(Twitch.Player.PLAY,function(){c||(c=!0),l=!1,d=!1,i(["play","playing","progress"]),y=setInterval(function(){u.getCurrentTime(),i(["timeupdate"])},250)}),u.addEventListener(Twitch.Player.PAUSE,function(){l=!0,d=!1,u.getEnded()||i(["pause"])}),u.addEventListener(Twitch.Player.ENDED,function(){l=!0,d=!0,i(["ended"]),clearInterval(y),c=!1,y=null})};var E=e.originalNode.height,N=e.originalNode.width,P=document.createElement("div"),U=r.getTwitchType(o),_={id:a.id,width:N,height:E,playsinline:!1,autoplay:e.originalNode.autoplay,muted:e.originalNode.muted};return _[U]=o,P.id=a.id,P.style.width="100%",P.style.height="100%",e.originalNode.parentNode.insertBefore(P,e.originalNode),e.originalNode.style.display="none",e.originalNode.autoplay=!1,a.setSize=function(e,t){null===r||isNaN(e)||isNaN(t)||(P.setAttribute("width",e),P.setAttribute("height",t))},a.hide=function(){a.pause(),P.style.display="none"},a.show=function(){P.style.display=""},a.destroy=function(){},r.load(_),a}};mejs.Utils.typeChecks.push(function(e){return/\/\/(www|player).twitch.tv/i.test(e)?"video/x-twitch":null}),mejs.Renderers.add(i)},{}]},{},[1]);