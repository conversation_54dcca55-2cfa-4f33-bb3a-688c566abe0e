@echo off
echo Installing BTHK Live System Dependencies...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js is not installed!
    echo Please download and install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version

echo.
echo Installing npm packages...
echo This may take a few minutes...

REM Enable script execution temporarily
powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"

REM Install packages using PowerShell
powershell -Command "& npm install express@4.18.2"
powershell -Command "& npm install socket.io@4.7.2"
powershell -Command "& npm install multer@1.4.5-lts.1"
powershell -Command "& npm install body-parser@1.20.2"
powershell -Command "& npm install cors@2.8.5"
powershell -Command "& npm install path@0.12.7"
powershell -Command "& npm install fs-extra@11.1.1"
powershell -Command "& npm install sqlite3@5.1.6"
powershell -Command "& npm install uuid@9.0.0"
powershell -Command "& npm install moment@2.29.4"
powershell -Command "& npm install express-rate-limit@6.10.0"
powershell -Command "& npm install helmet@7.0.0"

echo.
echo Installation completed!
echo You can now run the system using start.bat
echo.
pause
