<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BTHK Templates - قوالب نظام BTHK</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #4CAF50;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .template-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .template-card h2 {
            color: #4CAF50;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        .template-card p {
            margin-bottom: 1.5rem;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .features {
            list-style: none;
            margin-bottom: 1.5rem;
        }
        
        .features li {
            padding: 0.3rem 0;
            opacity: 0.8;
        }
        
        .features li:before {
            content: "✅ ";
            margin-left: 0.5rem;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            text-decoration: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: bold;
            text-align: center;
            flex: 1;
            min-width: 120px;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #2196F3;
        }
        
        .btn-secondary:hover {
            background: #1976D2;
        }
        
        .info-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-section h3 {
            color: #4CAF50;
            margin-bottom: 1rem;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .tech-tag {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 BTHK Templates</h1>
            <p>قوالب نظام البث المباشر - مُحسّنة ومطورة بالكامل</p>
        </div>
        
        <div class="templates-grid">
            <!-- Admin Panel Template -->
            <div class="template-card">
                <h2>📋 لوحة التحكم الإدارية</h2>
                <p>لوحة تحكم شاملة ومتقدمة لإدارة نظام البث المباشر مع جميع الميزات المطلوبة.</p>
                
                <ul class="features">
                    <li>إحصائيات شاملة للنظام</li>
                    <li>إدارة القنوات والملفات</li>
                    <li>التقاط الفيديو من USB/HDMI</li>
                    <li>رفع الملفات وإدارتها</li>
                    <li>إعدادات النظام المتقدمة</li>
                    <li>واجهة عربية كاملة</li>
                </ul>
                
                <div class="buttons">
                    <a href="admin.html" class="btn" target="_blank">🚀 فتح لوحة التحكم</a>
                    <a href="#" class="btn btn-secondary" onclick="viewSource('admin.html')">📝 عرض الكود</a>
                </div>
            </div>
            
            <!-- Viewer Template -->
            <div class="template-card">
                <h2>📺 صفحة المشاهدة</h2>
                <p>واجهة مشاهدة حديثة وسهلة الاستخدام مع مشغل فيديو متقدم وتحكم كامل.</p>
                
                <ul class="features">
                    <li>مشغل فيديو متطور</li>
                    <li>قائمة القنوات التفاعلية</li>
                    <li>عداد المشاهدين الفوري</li>
                    <li>تحكم كامل بالتشغيل</li>
                    <li>تصميم متجاوب</li>
                    <li>دعم ملء الشاشة</li>
                </ul>
                
                <div class="buttons">
                    <a href="viewer.html" class="btn" target="_blank">📱 فتح صفحة المشاهدة</a>
                    <a href="#" class="btn btn-secondary" onclick="viewSource('viewer.html')">📝 عرض الكود</a>
                </div>
            </div>
        </div>
        
        <div class="info-section">
            <h3>🔧 التقنيات المستخدمة</h3>
            <div class="tech-stack">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">CSS3</span>
                <span class="tech-tag">JavaScript ES6+</span>
                <span class="tech-tag">Socket.IO</span>
                <span class="tech-tag">Node.js</span>
                <span class="tech-tag">Express.js</span>
                <span class="tech-tag">SQLite</span>
                <span class="tech-tag">FFmpeg</span>
                <span class="tech-tag">HLS Streaming</span>
                <span class="tech-tag">Responsive Design</span>
            </div>
        </div>
        
        <div class="info-section">
            <h3>📋 معلومات إضافية</h3>
            <p><strong>تاريخ الإنشاء:</strong> <span id="currentDate"></span></p>
            <p><strong>المصدر:</strong> BTHK Live System</p>
            <p><strong>الترخيص:</strong> للاستخدام الشخصي والتجاري</p>
            <p><strong>الدعم:</strong> واجهة عربية كاملة مع تصميم RTL</p>
            
            <div style="margin-top: 1rem;">
                <a href="BTHK_TEMPLATES_README.md" class="btn" target="_blank">📖 دليل الاستخدام التفصيلي</a>
            </div>
        </div>
    </div>
    
    <script>
        // Set current date
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('ar-SA');
        
        // View source function
        function viewSource(filename) {
            // Open file in new tab for viewing source
            const newWindow = window.open('', '_blank');
            fetch(filename)
                .then(response => response.text())
                .then(html => {
                    newWindow.document.write(`
                        <html>
                        <head>
                            <title>Source Code: ${filename}</title>
                            <style>
                                body { 
                                    font-family: 'Courier New', monospace; 
                                    background: #1e1e1e; 
                                    color: #d4d4d4; 
                                    padding: 20px; 
                                    line-height: 1.5;
                                }
                                pre { 
                                    white-space: pre-wrap; 
                                    word-wrap: break-word; 
                                }
                                .line-numbers {
                                    color: #858585;
                                    margin-left: 10px;
                                }
                            </style>
                        </head>
                        <body>
                            <h2>Source Code: ${filename}</h2>
                            <pre>${html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                        </body>
                        </html>
                    `);
                })
                .catch(error => {
                    newWindow.document.write(`
                        <html>
                        <body>
                            <h2>Error loading source code</h2>
                            <p>Could not load ${filename}</p>
                            <p>Error: ${error.message}</p>
                        </body>
                        </html>
                    `);
                });
        }
    </script>
</body>
</html>
