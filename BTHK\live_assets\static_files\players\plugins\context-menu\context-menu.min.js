/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(n,t,o){function i(r,c){if(!t[r]){if(!n[r]){var s="function"==typeof require&&require;if(!c&&s)return s(r,!0);if(u)return u(r,!0);var l=new Error("Cannot find module '"+r+"'");throw l.code="MODULE_NOT_FOUND",l}var a=t[r]={exports:{}};n[r][0].call(a.exports,function(e){var t=n[r][1][e];return i(t||e)},a,a.exports,e,n,t,o)}return t[r].exports}for(var u="function"==typeof require&&require,r=0;r<o.length;r++)i(o[r]);return i}({1:[function(e,n,t){"use strict";mejs.i18n.en["mejs.fullscreen-off"]="Turn off Fullscreen",mejs.i18n.en["mejs.fullscreen-on"]="Go Fullscreen",mejs.i18n.en["mejs.download-video"]="Download Video",Object.assign(mejs.MepDefaults,{contextMenuItems:[{render:function(e){return void 0===e.enterFullScreen?null:e.isFullScreen?mejs.i18n.t("mejs.fullscreen-off"):mejs.i18n.t("mejs.fullscreen-on")},click:function(e){e.isFullScreen?e.exitFullScreen():e.enterFullScreen()}},{render:function(e){return e.media.muted?mejs.i18n.t("mejs.unmute"):mejs.i18n.t("mejs.mute")},click:function(e){e.media.muted?e.setMuted(!1):e.setMuted(!0)}},{isSeparator:!0},{render:function(){return mejs.i18n.t("mejs.download-video")},click:function(e){window.location.href=e.media.currentSrc}}]}),Object.assign(MediaElementPlayer.prototype,{isContextMenuEnabled:!0,contextMenuTimeout:null,buildcontextmenu:function(e){e.isVideo&&(document.querySelector("."+e.options.classPrefix+"contextmenu")||(e.contextMenu=document.createElement("div"),e.contextMenu.className=e.options.classPrefix+"contextmenu",e.contextMenu.style.display="none",document.body.appendChild(e.contextMenu)),e.container.addEventListener("contextmenu",function(n){!e.isContextMenuEnabled||3!==n.keyCode&&3!==n.which||(e.renderContextMenu(n),n.preventDefault(),n.stopPropagation())}),e.container.addEventListener("click",function(){e.contextMenu.style.display="none"}),e.contextMenu.addEventListener("mouseleave",function(){e.startContextMenuTimer()}))},cleancontextmenu:function(e){e.contextMenu.remove()},enableContextMenu:function(){this.isContextMenuEnabled=!0},disableContextMenu:function(){this.isContextMenuEnabled=!1},startContextMenuTimer:function(){var e=this;e.killContextMenuTimer(),e.contextMenuTimer=setTimeout(function(){e.hideContextMenu(),e.killContextMenuTimer()},750)},killContextMenuTimer:function(){var e=this.contextMenuTimer;null!==e&&void 0!==e&&(clearTimeout(e),e=null)},hideContextMenu:function(){this.contextMenu.style.display="none"},renderContextMenu:function(e){for(var n=this,t="",o=n.options.contextMenuItems,i=0,u=o.length;i<u;i++){var r=o[i];if(r.isSeparator)t+='<div class="'+n.options.classPrefix+'contextmenu-separator"></div>';else{var c=r.render(n);null!==c&&void 0!==c&&(t+='<div class="'+n.options.classPrefix+'contextmenu-item" data-itemindex="'+i+'" id="element-'+1e6*Math.random()+'">'+c+"</div>")}}n.contextMenu.innerHTML=t;var s=n.contextMenu.offsetWidth,l=n.contextMenu.offsetHeight,a=e.pageX,d=e.pageY,f=document.documentElement,m=(window.pageXOffset||f.scrollLeft)-(f.clientLeft||0),x=(window.pageYOffset||f.scrollTop)-(f.clientTop||0),M=a+s>window.innerWidth+m?a-s:a,p=d+l>window.innerHeight+x?d-l:d;n.contextMenu.style.display="",n.contextMenu.style.left=M+"px",n.contextMenu.style.top=p+"px";for(var v=n.contextMenu.querySelectorAll("."+n.options.classPrefix+"contextmenu-item"),h=0,y=v.length;h<y;h++)!function(e,t){var o=v[e],i=parseInt(o.getAttribute("data-itemindex"),10),u=n.options.contextMenuItems[i];void 0!==u.show&&u.show(o,n),o.addEventListener("click",function(){void 0!==u.click&&u.click(n),n.contextMenu.style.display="none"})}(h);setTimeout(function(){n.killControlsTimer()},100)}})},{}]},{},[1]);