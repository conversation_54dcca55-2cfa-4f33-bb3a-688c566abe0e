'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.playlist'] = 'Llista de reproducció';
	mejs.i18n.ca['mejs.playlist-prev'] = 'Anterior';
	mejs.i18n.ca['mejs.playlist-next'] = 'Pròxim';
	mejs.i18n.ca['mejs.playlist-loop'] = 'Bucle';
	mejs.i18n.ca['mejs.playlist-shuffle'] = 'Barrejar';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.playlist'] = 'Seznam skladeb';
	mejs.i18n.cs['mejs.playlist-prev'] = 'Předchozí';
	mejs.i18n.cs['mejs.playlist-next'] = 'Další';
	mejs.i18n.cs['mejs.playlist-loop'] = 'Smyčka';
	mejs.i18n.cs['mejs.playlist-shuffle'] = 'Zamíchat';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.playlist'] = 'Wiedergabeliste';
	mejs.i18n.de['mejs.playlist-prev'] = 'Bisherige';
	mejs.i18n.de['mejs.playlist-next'] = 'Nächster';
	mejs.i18n.de['mejs.playlist-loop'] = 'Schleife';
	mejs.i18n.de['mejs.playlist-shuffle'] = 'Shuffle';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.playlist'] = 'Lista de Reproducción';
	mejs.i18n.es['mejs.playlist-prev'] = 'Previo';
	mejs.i18n.es['mejs.playlist-next'] = 'Siguiente';
	mejs.i18n.es['mejs.playlist-loop'] = 'Ciclar';
	mejs.i18n.es['mejs.playlist-shuffle'] = 'Mezclar';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.playlist'] = 'لیست پخش';
	mejs.i18n.fa['mejs.playlist-prev'] = 'قبلی';
	mejs.i18n.fa['mejs.playlist-next'] = 'بعد';
	mejs.i18n.fa['mejs.playlist-loop'] = 'حلقه';
	mejs.i18n.fa['mejs.playlist-shuffle'] = 'زدن';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.playlist'] = 'Playlist';
	mejs.i18n.fr['mejs.playlist-prev'] = 'Précédent';
	mejs.i18n.fr['mejs.playlist-next'] = 'Prochain';
	mejs.i18n.fr['mejs.playlist-loop'] = 'Boucle';
	mejs.i18n.fr['mejs.playlist-shuffle'] = 'Mélanger';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.playlist'] = 'Popis';
	mejs.i18n.hr['mejs.playlist-prev'] = 'Prijašnji';
	mejs.i18n.hr['mejs.playlist-next'] = 'Sljedeći';
	mejs.i18n.hr['mejs.playlist-loop'] = 'Petlja';
	mejs.i18n.hr['mejs.playlist-shuffle'] = 'Miješanje';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.playlist'] = 'Lejátszási lista';
	mejs.i18n.hu['mejs.playlist-prev'] = 'Előző';
	mejs.i18n.hu['mejs.playlist-next'] = 'Következő';
	mejs.i18n.hu['mejs.playlist-loop'] = 'Hurok';
	mejs.i18n.hu['mejs.playlist-shuffle'] = 'Keverés';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.playlist'] = 'Elenco di riproduzione';
	mejs.i18n.it['mejs.playlist-prev'] = 'Precedente';
	mejs.i18n.it['mejs.playlist-next'] = 'Prossimo';
	mejs.i18n.it['mejs.playlist-loop'] = 'Ciclo continuo';
	mejs.i18n.it['mejs.playlist-shuffle'] = 'Rimescolare';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.playlist'] = 'プレイリスト';
	mejs.i18n.ja['mejs.playlist-prev'] = '前';
	mejs.i18n.ja['mejs.playlist-next'] = '次';
	mejs.i18n.ja['mejs.playlist-loop'] = 'ループ';
	mejs.i18n.ja['mejs.playlist-shuffle'] = 'シャッフル';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.playlist'] = '재생 목록';
	mejs.i18n.ko['mejs.playlist-prev'] = '너무 이른';
	mejs.i18n.ko['mejs.playlist-next'] = '다음 것';
	mejs.i18n.ko['mejs.playlist-loop'] = '고리';
	mejs.i18n.ko['mejs.playlist-shuffle'] = '혼합';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.playlist'] = 'Afspeellijst';
	mejs.i18n.nl['mejs.playlist-prev'] = 'Voorgaand';
	mejs.i18n.nl['mejs.playlist-next'] = 'Volgende';
	mejs.i18n.nl['mejs.playlist-loop'] = 'Lus';
	mejs.i18n.nl['mejs.playlist-shuffle'] = 'Schuifelen';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.playlist'] = 'Playlistę';
	mejs.i18n.pl['mejs.playlist-prev'] = 'Poprzedni';
	mejs.i18n.pl['mejs.playlist-next'] = 'Następny';
	mejs.i18n.pl['mejs.playlist-loop'] = 'Pętla';
	mejs.i18n.pl['mejs.playlist-shuffle'] = 'Człapać';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.playlist'] = 'Lista de reprodução';
	mejs.i18n.pt['mejs.playlist-prev'] = 'Anterior';
	mejs.i18n.pt['mejs.playlist-next'] = 'Próximo';
	mejs.i18n.pt['mejs.playlist-loop'] = 'Ciclar';
	mejs.i18n.pt['mejs.playlist-shuffle'] = 'Baralhar';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.playlist'] = 'Lista de redare';
	mejs.i18n.ro['mejs.playlist-prev'] = 'Anterior';
	mejs.i18n.ro['mejs.playlist-next'] = 'Următor';
	mejs.i18n.ca['mejs.playlist-loop'] = 'Buclă';
	mejs.i18n.ca['mejs.playlist-shuffle'] = 'Amesteca';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.playlist'] = 'плейлист';
	mejs.i18n.ru['mejs.playlist-prev'] = 'предыдущий';
	mejs.i18n.ru['mejs.playlist-next'] = 'следующий';
	mejs.i18n.ru['mejs.playlist-loop'] = 'петля';
	mejs.i18n.ru['mejs.playlist-shuffle'] = 'шарканье';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.playlist'] = 'Playlist';
	mejs.i18n.sk['mejs.playlist-prev'] = 'Predchádzajúca';
	mejs.i18n.sk['mejs.playlist-next'] = 'Ďalšie';
	mejs.i18n.sk['mejs.playlist-loop'] = 'Slučka';
	mejs.i18n.sk['mejs.playlist-shuffle'] = 'Miešanie';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.playlist'] = 'Spellista';
	mejs.i18n.sv['mejs.playlist-prev'] = 'Tidigare';
	mejs.i18n.sv['mejs.playlist-next'] = 'Nästa';
	mejs.i18n.sv['mejs.playlist-loop'] = 'Slinga';
	mejs.i18n.sv['mejs.playlist-shuffle'] = 'Blanda';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.playlist'] = 'Плейлист';
	mejs.i18n.uk['mejs.playlist-prev'] = 'Попередній';
	mejs.i18n.uk['mejs.playlist-next'] = 'Далі';
	mejs.i18n.uk['mejs.playlist-loop'] = 'Петля';
	mejs.i18n.uk['mejs.playlist-shuffle'] = 'Перемішувати';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.playlist'] = '播放列表';
	mejs.i18n.zh['mejs.playlist-prev'] = '以前';
	mejs.i18n.zh['mejs.playlist-next'] = '下一個';
	mejs.i18n.zh['mejs.playlist-loop'] = '循環';
	mejs.i18n.zh['mejs.playlist-shuffle'] = '拖曳';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.playlist'] = '播放列表';
	mejs.i18n['zh-CN']['mejs.playlist-prev'] = '以前';
	mejs.i18n['zh-CN']['mejs.playlist-next'] = '下一个';
	mejs.i18n['zh-CN']['mejs.playlist-loop'] = '循环';
	mejs.i18n['zh-CN']['mejs.playlist-shuffle'] = '拖曳';
}