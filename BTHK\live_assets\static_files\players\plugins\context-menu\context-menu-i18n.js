'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.fullscreen-off'] = 'Desconnectar pantalla completaa';
	mejs.i18n.ca['mejs.fullscreen-on'] = 'Anar a pantalla completa';
	mejs.i18n.ca['mejs.download-video'] = 'Descarregar vídeo';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.fullscreen-off'] = 'Vypnout režim celá obrazovka';
	mejs.i18n.cs['mejs.fullscreen-on'] = 'Na celou obrazovku';
	mejs.i18n.cs['mejs.download-video'] = 'Stáhnout video';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.fullscreen-off'] = 'Vollbildmodus beenden';
	mejs.i18n.de['mejs.fullscreen-on'] = 'Vollbild';
	mejs.i18n.de['mejs.download-video'] = 'Video herunterladen';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.fullscreen-off'] = 'Desconectar pantalla completa';
	mejs.i18n.es['mejs.fullscreen-on'] = 'Ir a pantalla completa';
	mejs.i18n.es['mejs.download-video'] = 'Descargar vídeo';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.fullscreen-off'] = 'تمام صفحه را خاموش کنید';
	mejs.i18n.fa['mejs.fullscreen-on'] = 'برو تمام صفحه';
	mejs.i18n.fa['mejs.download-video'] = 'دانلود فیلم';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.fullscreen-off'] = 'Quitter le mode plein écran';
	mejs.i18n.fr['mejs.fullscreen-on'] = 'Afficher en plein écran';
	mejs.i18n.fr['mejs.download-video'] = 'Télécharger la vidéo';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.fullscreen-off'] = 'Isključi puni zaslon';
	mejs.i18n.hr['mejs.fullscreen-on'] = 'Uključi puni zaslon';
	mejs.i18n.hr['mejs.download-video'] = 'Preuzmi video';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.fullscreen-off'] = 'Teljes képernyő kikapcsolása';
	mejs.i18n.hu['mejs.fullscreen-on'] = 'Átlépés teljes képernyős módra';
	mejs.i18n.hu['mejs.download-video'] = 'Videó letöltése';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.fullscreen-off'] = 'Disattivare lo schermo intero';
	mejs.i18n.it['mejs.fullscreen-on'] = 'Attivare lo schermo intero';
	mejs.i18n.it['mejs.download-video'] = 'Scaricare il video';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.fullscreen-off'] = '全画面をオフにする';
	mejs.i18n.ja['mejs.fullscreen-on'] = '全画面にする';
	mejs.i18n.ja['mejs.download-video'] = '動画をダウンロードする';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.fullscreen-off'] = '전체화면 해제';
	mejs.i18n.ko['mejs.fullscreen-on'] = '전체화면 가기';
	mejs.i18n.ko['mejs.download-video'] = '비디오 다운로드';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.fullscreen-off'] = 'Volledig scherm uitschakelen';
	mejs.i18n.nl['mejs.fullscreen-on'] = 'Volledig scherm';
	mejs.i18n.nl['mejs.download-video'] = 'Video downloaden';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.fullscreen-off'] = 'Wyłącz pełny ekran';
	mejs.i18n.pl['mejs.fullscreen-on'] = 'Przejdź na pełny ekran';
	mejs.i18n.pl['mejs.download-video'] = 'Pobierz wideo';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.fullscreen-off'] = 'Desligar ecrã completo';
	mejs.i18n.pt['mejs.fullscreen-on'] = 'Ir para ecrã completo';
	mejs.i18n.pt['mejs.download-video'] = 'Descarregar o vídeo';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.fullscreen-off'] = 'Opreşte ecranul complet';
	mejs.i18n.ro['mejs.fullscreen-on'] = 'Treci la ecran complet';
	mejs.i18n.ro['mejs.download-video'] = 'Descarcă fişierul video';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.fullscreen-off'] = 'Выключить полноэкранный режим';
	mejs.i18n.ru['mejs.fullscreen-on'] = 'Перейти в полноэкранный режим';
	mejs.i18n.ru['mejs.download-video'] = 'Скачать видео';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.fullscreen-off'] = 'Vypnúť celú obrazovku';
	mejs.i18n.sk['mejs.fullscreen-on'] = 'Prejsť na celú obrazovku';
	mejs.i18n.sk['mejs.download-video'] = 'Prevziať video';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.fullscreen-off'] = 'Stäng av Fullskärmläge';
	mejs.i18n.sv['mejs.fullscreen-on'] = 'Visa i Fullskärmsläge';
	mejs.i18n.sv['mejs.download-video'] = 'Ladda ner Video';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.fullscreen-off'] = 'Вимкнути повноекранний режим';
	mejs.i18n.uk['mejs.fullscreen-on'] = 'Увійти в повноекранний режим';
	mejs.i18n.uk['mejs.download-video'] = 'Скачати відео';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.fullscreen-off'] = '關閉全屏';
	mejs.i18n.zh['mejs.fullscreen-on'] = '轉向全屏';
	mejs.i18n.zh['mejs.download-video'] = '下載視頻';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.fullscreen-off'] = '关闭全屏';
	mejs.i18n['zh-CN']['mejs.fullscreen-on'] = '转向全屏';
	mejs.i18n['zh-CN']['mejs.download-video'] = '下载视频';
}