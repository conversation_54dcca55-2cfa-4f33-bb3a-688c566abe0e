# BTHK Templates - قوالب نظام BTHK

## الملفات المنسوخة من نظام BTHK:

### 📋 **admin.html** - لوحة التحكم الكاملة
**الوصف:** لوحة تحكم شاملة لإدارة نظام البث المباشر

**الميزات:**
- ✅ **لوحة المعلومات**: إحصائيات شاملة للنظام
- ✅ **إدارة القنوات**: إضافة وحذف وتعديل القنوات
- ✅ **التقاط المباشر**: تقاط الفيديو من أجهزة USB/HDMI باستخدام FFmpeg
- ✅ **إدارة الملفات**: رفع ومعاينة الملفات
- ✅ **الإعدادات**: تخصيص إعدادات النظام

**التبويبات المتاحة:**
1. **📊 الرئيسية** - إحصائيات النظام والمعلومات العامة
2. **📺 القنوات** - إدارة قنوات البث
3. **🎥 التقاط مباشر** - تقاط الفيديو من الأجهزة الخارجية
4. **📁 الملفات** - إدارة الملفات المرفوعة
5. **⚙️ الإعدادات** - إعدادات النظام

**التقنيات المستخدمة:**
- HTML5 + CSS3 + JavaScript
- Socket.IO للتحديثات الفورية
- RESTful API
- تصميم متجاوب (Responsive Design)
- واجهة عربية كاملة

---

### 📺 **viewer.html** - صفحة المشاهدة
**الوصف:** واجهة المشاهدة الرئيسية للمستخدمين

**الميزات:**
- ✅ **مشغل فيديو متقدم**: دعم جميع صيغ البث
- ✅ **قائمة القنوات**: عرض جميع القنوات المتاحة
- ✅ **عداد المشاهدين**: إحصائيات فورية
- ✅ **تحكم كامل**: تشغيل، إيقاف، مستوى الصوت، ملء الشاشة
- ✅ **تصميم متجاوب**: يعمل على جميع الأجهزة

**المكونات الرئيسية:**
1. **الهيدر**: عنوان النظام وعداد المشاهدين
2. **مشغل الفيديو**: منطقة عرض البث الرئيسية
3. **أدوات التحكم**: أزرار التشغيل والتحكم
4. **القائمة الجانبية**: قائمة القنوات المتاحة

**التقنيات المستخدمة:**
- HTML5 Video API
- Socket.IO للتحديثات الفورية
- CSS Grid & Flexbox
- JavaScript ES6+
- HLS.js لدعم البث المباشر

---

## 🚀 كيفية الاستخدام:

### **لاستخدام لوحة التحكم:**
```html
<!-- في ملف HTML الخاص بك -->
<iframe src="admin.html" width="100%" height="800px"></iframe>
```

### **لاستخدام صفحة المشاهدة:**
```html
<!-- في ملف HTML الخاص بك -->
<iframe src="viewer.html" width="100%" height="600px"></iframe>
```

### **للتخصيص:**
1. افتح الملف في محرر النصوص
2. عدل الألوان في قسم CSS
3. غير النصوص والعناوين حسب الحاجة
4. أضف ميزات جديدة في قسم JavaScript

---

## 🎨 التخصيص:

### **تغيير الألوان:**
```css
/* في قسم <style> */
body {
    background: linear-gradient(135deg, #YOUR_COLOR1 0%, #YOUR_COLOR2 100%);
}

.header h1 {
    color: #YOUR_ACCENT_COLOR;
}
```

### **تغيير العناوين:**
```html
<!-- في قسم <head> -->
<title>اسم نظامك هنا</title>

<!-- في قسم <body> -->
<h1>🎥 اسم نظامك</h1>
```

### **إضافة ميزات جديدة:**
```javascript
// في قسم <script>
function myCustomFunction() {
    // الكود الخاص بك هنا
}
```

---

## 📋 متطلبات التشغيل:

### **للوحة التحكم:**
- خادم Node.js مع Express
- قاعدة بيانات SQLite
- Socket.IO للتحديثات الفورية
- FFmpeg (للتقاط الفيديو)

### **لصفحة المشاهدة:**
- متصفح ويب حديث
- دعم HTML5 Video
- اتصال بالإنترنت

---

## 🔧 الدعم والصيانة:

### **للحصول على الدعم:**
- راجع ملف README.md الرئيسي
- تحقق من ملف FFMPEG_SETUP.md للتقاط الفيديو
- راجع سجلات الأخطاء في المتصفح (F12)

### **للتطوير:**
- استخدم أدوات المطور في المتصفح
- راجع وثائق Socket.IO و Express.js
- اختبر على متصفحات مختلفة

---

## 📝 ملاحظات مهمة:

1. **الملفات مُحسّنة للغة العربية** - اتجاه RTL
2. **تصميم متجاوب** - يعمل على الهاتف والكمبيوتر
3. **أمان عالي** - حماية من الهجمات الأساسية
4. **أداء محسّن** - تحميل سريع وسلس

---

**تاريخ النسخ:** $(Get-Date)
**المصدر:** BTHK/templates/
**الإصدار:** 1.0.0
