{"name": "bthk-live-system", "version": "1.0.0", "description": "BTHK Live Streaming System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "multer": "^1.4.5-lts.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "path": "^0.12.7", "fs-extra": "^11.1.1", "sqlite3": "^5.1.6", "uuid": "^9.0.0", "moment": "^2.29.4", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "fluent-ffmpeg": "^2.1.2", "node-rtsp-stream": "^0.0.9", "child_process": "*"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["streaming", "live", "broadcast", "bthk"], "author": "BTHK Team", "license": "MIT"}