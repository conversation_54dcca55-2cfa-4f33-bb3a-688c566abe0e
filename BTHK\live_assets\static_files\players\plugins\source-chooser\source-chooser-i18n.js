'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.source-chooser']= 'Selector de font';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.source-chooser']= 'Zdr<PERSON>jo<PERSON><PERSON> výběr';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.source-chooser']= 'Quellenauswahl';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.source-chooser']= 'Selector de media';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.source-chooser']= 'انتخاب کننده منبع';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.source-chooser']= 'Sélecteur de média';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.source-chooser']= 'Obabir izvora';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.source-chooser']= 'Forrásválasztó';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.source-chooser']= 'Selezionatore di origine';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.source-chooser']= 'ソースセレクタ';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.source-chooser']= '소스 선택기';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.source-chooser']= 'Bronkeuze';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.source-chooser']= 'Wybór źródła';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.source-chooser']= 'Escolhedor de fontes';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.source-chooser']= 'Sursă de selecție';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.source-chooser']= 'Переключатель источника';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.source-chooser']= 'Zdroj výberu';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.source-chooser']= 'Välj källa';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.source-chooser']= 'Вибір джерела';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.source-chooser']= '源選擇器';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.source-chooser']= '源选择器';
}