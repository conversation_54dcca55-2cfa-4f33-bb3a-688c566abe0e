<!DOCTYPE html>
<html>
    {% include "admin/head.html" %}
  <body dir="rtl" class="noselect" >
        {% include "admin/mnu.html" %}
    
        <style>
                .tvcard{
                    background: -moz-linear-gradient(top, rgba(0,0,0,0) 0%, rgba(0,0,0,0.65) 55%, rgba(0,0,0,0.69) 60%, rgba(0,0,0,1) 95%, rgba(0,0,0,1) 100%);
                    background: -webkit-linear-gradient(top, rgba(0,0,0,0) 0%,rgba(0,0,0,0.65) 55%,rgba(0,0,0,0.69) 60%,rgba(0,0,0,1) 95%,rgba(0,0,0,1) 100%);
                    background: linear-gradient(to bottom, rgba(0,0,0,0) 0%,rgba(0,0,0,0.65) 55%,rgba(0,0,0,0.69) 60%,rgba(0,0,0,1) 95%,rgba(0,0,0,1) 100%);
                    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#000000',GradientType=0 );
                                    
                    height: 200px;
                    width: 235px;
                    z-index: 2;
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0px 0px 64px 0px #0000004f inset;
                    position: relative;
                    margin: auto;
                    margin-top: 20px;
              
                }
                .tvimg{
                    width: 100%;
                    height: 100%;
                    overflow: hidden;
                    position: relative;
                    opacity:0.3;
                }
                .tvimg > img{
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    z-index: -1;
                    left: 0px;
                    top: 0px;
                }
                .lightplace{
                    width: 15px;
                    height: 15px;
                   
                    z-index: 999;
                    position: absolute;
                    top: 9px;
                    left: 24px;
                    border-radius: 20px;
                    border: 1px solid #868383;
                    
                }
                .live{
                    box-shadow: 0px 0px 10px 1px #54ff00;
                    background: #54ff00;
                }
                .nlive{
                    box-shadow: 0px 0px 10px 1px #c32828;
                    background: #ff0000;
                }
                .tvtext{
                    color:#fff;
                    
                    z-index: 999;
                    position: absolute;
                    bottom: 17px;
                    right: 12px;
                    font-size: 22px;
                }
                .page{
                    padding:20px;
                }
                .tvViews{
                    
                    direction: ltr;
                    position: absolute;
                    top: 48px;
                    text-align: center;
                    width: 100%;
                    font-size: 35px;
                    font-weight: bold;
                                            
                }
                .tvViews2{
                    direction: ltr;
                    position: absolute;
                    top: 81px;
                    text-align: center;
                    width: 100%;
                    margin-top:20px;
                    font-size: 14px;
                   
                }
                .tvViewsIUp{
                                    
                    background: none;
                    color: #60ff00;
                    font-size: 39px;
                    position: absolute;
                    margin-left:  20px;
                    margin-top: 4px;

                }
                .tvViewsIDown{
                                    
                    background: none;
                    color: #ff0000;
                    font-size: 39px;
                    position: absolute;
                    margin-left:  20px;
                    margin-top: 4px;

                }
                .tvViewsIMinus{
                    background: none;
                    color: #999;
                    font-size: 39px;
                    position: absolute;
                    margin-left:  20px;
                    margin-top: 4px;
                }

            </style>

    <div class=" nopadding" >
        <div class="page">

            {% if channels.length==0 %}
                <div style="font-size:70px;text-align:center;">
                    لايوجد اي قنوات حالياً 
                </div>
            {% endif %}
            <div class="row" style="margin:0px;">
                {% for c in channels %}
    
                    <div class="col-md-4 col-xs-12">
                    
                        <div class="tvcard">
                            <div class="lightplace {%if c.islive %} live {% else %} nlive{% endif %}"></div>
                            <div class="tvimg">
                                <img src="/getChannelImg/{{c.id}}">
                            </div>
                            <div class="tvViews">{{c.todayViews}} {% if c.isUp==1 %}<span class=" fa fa-caret-up tvViewsIUp">{% elseif c.isUp==-1  %}<span class=" fa fa-caret-down tvViewsIDown"></span>{% else %}<span class=" fa fa-minus tvViewsIMinus"></span>{% endif%}</span></div>
                            <div class="tvViews2">{{c.totalViews}} عدد المشاهدات الكلي </div>
                            <div class="tvtext">{{c.cha_name}}</div>
                        </div>
                      
                        
                    </div>
                {% endfor %}
                

            </div>
        </div>
    </div>
    
    
    {% include "admin/footer.html" %}
  </body>
</html>
