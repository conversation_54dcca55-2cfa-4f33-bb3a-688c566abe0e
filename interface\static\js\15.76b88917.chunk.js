(this.webpackJsonpestra7ah5=this.webpackJsonpestra7ah5||[]).push([[15],{420:function(t,e,a){},424:function(t,e,a){"use strict";a.r(e),a.d(e,"default",(function(){return c}));var n=a(0),i=a(1),s=a(8),o=(a(420),a(2));function c(){var t=Object(i.useRef)(null);return Object(i.useEffect)((function(){o.a.fetch(o.a.host+"getCard").then((function(t){return t.json()})).then((function(t){"ok"===t.msg&&t.card&&(window.location.href="/")}))}),[]),Object(n.jsxs)("div",{className:"login-card",children:[Object(n.jsx)("div",{className:"back-btn",onClick:function(){window.history.back(),window.history.back()},children:"\u0631\u062c\u0648\u0639"}),Object(n.jsxs)("div",{className:"login-box",children:[Object(n.jsx)("i",{className:"lni lni-user"}),Object(n.jsxs)("div",{className:"head",children:["\u062f\u062e\u0648\u0644"," ",Object(n.jsx)("span",{style:{fontStyle:"italic"},children:o.a.networkName})]})," ",Object(n.jsx)("div",{children:"\u0627\u0644\u0645\u062a\u062d\u0648\u0649 \u0627\u0644\u0630\u064a \u062a\u0631\u064a\u062f \u0627\u0644\u062f\u062e\u0648\u0644 \u0644\u0647 \u0645\u062e\u0635\u0635 \u0644\u0627\u0634\u062a\u0631\u0627\u0643\u0627\u062a VIP \u0645\u062f\u0641\u0648\u0639\u0647 \u0628\u0643\u0631\u0648\u062a "}),Object(n.jsx)("input",{placeholder:"\u0631\u0645\u0632 \u0627\u0644\u0643\u0631\u062a",ref:t,onChange:function(t){t.target.value=t.target.value.toUpperCase()}}),Object(n.jsx)("div",{className:"inputs",children:Object(n.jsx)("button",{className:"login-btn",onClick:function(){if(!t.current.value)return s.Store.addNotification({title:"\u062e\u0637\u0627\u0621",message:"\u0627\u0644\u0631\u062c\u0627\u0621 \u0627\u062f\u062e\u0627\u0644 \u0631\u0645\u0632 \u0627\u0644\u0643\u0631\u062a",type:"warning",insert:"bottom",container:"top-left",animationIn:["animate__animated","animate__fadeIn"],animationOut:["animate__animated","animate__fadeOut"],dismiss:{duration:5e3}});var e;(e={cardCode:t.current.value},o.a.fetch(o.a.host+"login/"+e.cardCode,{method:"GET"}).then((function(t){return t.json()}))).then((function(t){if("valid"!==t.msg){if("blocked"===t.msg)return s.Store.addNotification({title:"\u062e\u0637\u0627\u0621",message:"\u0627\u0644\u0646\u0638\u0627\u0645 \u0645\u063a\u0644\u0642",type:"danger",insert:"bottom",container:"top-left",animationIn:["animate__animated","animate__fadeIn"],animationOut:["animate__animated","animate__fadeOut"],dismiss:{duration:5e3}});if("inuse"===t.msg)return s.Store.addNotification({title:"\u062e\u0637\u0627\u0621",message:"\u064a\u062a\u0645 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0627\u0644\u0643\u0631\u062a \u0641\u064a \u062c\u0647\u0627\u0632 \u0627\u062e\u0631 \u062a\u0628\u0642\u0649 \u0644\u062f\u064a\u0643 "+t.trys+" \u0645\u062d\u0627\u0648\u0644\u0627\u062a",type:"danger",insert:"bottom",container:"top-left",animationIn:["animate__animated","animate__fadeIn"],animationOut:["animate__animated","animate__fadeOut"],dismiss:{duration:5e3}});if("invalid"===t.msg)return s.Store.addNotification({title:"\u062e\u0637\u0627\u0621",message:"\u0627\u0644\u0643\u0631\u062a \u0627\u0644\u0645\u062f\u062e\u0644 \u063a\u064a\u0631 \u0635\u062d\u064a\u062d \u062a\u0628\u0642\u0649 \u0644\u062f\u064a\u0643 "+t.trys+" \u0645\u062d\u0627\u0648\u0644\u0627\u062a",type:"danger",insert:"bottom",container:"top-left",animationIn:["animate__animated","animate__fadeIn"],animationOut:["animate__animated","animate__fadeOut"],dismiss:{duration:5e3}})}window.location.href=localStorage.getItem("prevURL")||"/"}))},children:"\u062f\u062e\u0648\u0644"})}),Object(n.jsxs)("div",{style:{fontSize:13,margin:20,opacity:.4},children:[Object(n.jsx)("div",{children:" \u0628\u0646\u064a \u0628\u0641\u062e\u0631\u064d \u0641\u064a \u0627\u0644\u064a\u0645\u0646 "}),Object(n.jsxs)("div",{style:{paddingTop:5},children:["\u062c\u0645\u064a\u0639 \u0627\u0644\u062d\u0642\u0648\u0642 \u0645\u062d\u0641\u0648\u0638\u0629 \u0634\u0631\u0643\u0647",Object(n.jsxs)("span",{children:[Object(n.jsxs)("a",{target:"_blank",href:"http://yetech.co",children:[" ","YeTech"]})," "]})]})]})]}),Object(n.jsx)(s.ReactNotifications,{})]})}}}]);