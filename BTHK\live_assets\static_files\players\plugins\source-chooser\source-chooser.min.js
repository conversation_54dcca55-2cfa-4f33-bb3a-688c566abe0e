/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(o,t,r){function s(i,u){if(!t[i]){if(!o[i]){var n="function"==typeof require&&require;if(!u&&n)return n(i,!0);if(c)return c(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var l=t[i]={exports:{}};o[i][0].call(l.exports,function(e){var t=o[i][1][e];return s(t||e)},l,l.exports,e,o,t,r)}return t[i].exports}for(var c="function"==typeof require&&require,i=0;i<r.length;i++)s(r[i]);return s}({1:[function(e,o,t){"use strict";mejs.i18n.en["mejs.source-chooser"]="Source Chooser",Object.assign(mejs.MepDefaults,{sourcechooserText:null}),Object.assign(MediaElementPlayer.prototype,{buildsourcechooser:function(e,o,t,r){for(var s=this,c=mejs.Utils.isString(s.options.sourcechooserText)?s.options.sourcechooserText:mejs.i18n.t("mejs.source-chooser"),i=[],u=s.mediaFiles?s.mediaFiles:s.node.children,n=void 0,a=0,l=u.length;a<l;a++){var h=u[a];s.mediaFiles?i.push(h):"SOURCE"===h.nodeName&&i.push(h)}if(!(i.length<=1)){e.sourcechooserButton=document.createElement("div"),e.sourcechooserButton.className=s.options.classPrefix+"button "+s.options.classPrefix+"sourcechooser-button",e.sourcechooserButton.innerHTML='<button type="button" role="button" aria-haspopup="true" aria-owns="'+s.id+'" title="'+c+'" aria-label="'+c+'" tabindex="0"></button><div class="'+s.options.classPrefix+"sourcechooser-selector "+s.options.classPrefix+'offscreen" role="menu" aria-expanded="false" aria-hidden="true"><ul></ul></div>',s.addControlElement(e.sourcechooserButton,"sourcechooser");for(var d=0,f=i.length;d<f;d++){var p=i[d];void 0!==p.type&&"function"==typeof r.canPlayType&&e.addSourceButton(p.src,p.title,p.type,r.src===p.src)}e.sourcechooserButton.addEventListener("mouseover",function(){clearTimeout(n),e.showSourcechooserSelector()}),e.sourcechooserButton.addEventListener("mouseout",function(){n=setTimeout(function(){e.hideSourcechooserSelector()},0)}),e.sourcechooserButton.addEventListener("keydown",function(o){if(s.options.keyActions.length){switch(o.which||o.keyCode||0){case 32:mejs.MediaFeatures.isFirefox||e.showSourcechooserSelector(),e.sourcechooserButton.querySelector("input[type=radio]:checked").focus();break;case 13:e.showSourcechooserSelector(),e.sourcechooserButton.querySelector("input[type=radio]:checked").focus();break;case 27:e.hideSourcechooserSelector(),e.sourcechooserButton.querySelector("button").focus();break;default:return!0}o.preventDefault(),o.stopPropagation()}}),e.sourcechooserButton.addEventListener("focusout",mejs.Utils.debounce(function(){setTimeout(function(){document.activeElement.closest("."+s.options.classPrefix+"sourcechooser-selector")||e.hideSourcechooserSelector()},0)},100));for(var v=e.sourcechooserButton.querySelectorAll("input[type=radio]"),S=0,y=v.length;S<y;S++)v[S].addEventListener("click",function(){this.setAttribute("aria-selected",!0),this.checked=!0;for(var e=this.closest("."+s.options.classPrefix+"sourcechooser-selector").querySelectorAll("input[type=radio]"),o=0,t=e.length;o<t;o++)e[o]!==this&&(e[o].setAttribute("aria-selected","false"),e[o].removeAttribute("checked"));var c=this.value;if(r.getSrc()!==c){var i=r.currentTime,u=r.paused,n=function e(){u||(r.setCurrentTime(i),r.play()),r.removeEventListener("canplay",e)};r.pause(),r.setSrc(c),r.load(),r.addEventListener("canplay",n)}});e.sourcechooserButton.querySelector("button").addEventListener("click",function(){mejs.Utils.hasClass(mejs.Utils.siblings(this,"."+s.options.classPrefix+"sourcechooser-selector"),s.options.classPrefix+"offscreen")?(e.showSourcechooserSelector(),e.sourcechooserButton.querySelector("input[type=radio]:checked").focus()):e.hideSourcechooserSelector()})}},addSourceButton:function(e,o,t,r){var s=this;""!==o&&void 0!==o||(o=e),t=t.split("/")[1],s.sourcechooserButton.querySelector("ul").innerHTML+='<li><input type="radio" name="'+s.id+'_sourcechooser" id="'+s.id+"_sourcechooser_"+o+t+'" role="menuitemradio" value="'+e+'" '+(r?'checked="checked"':"")+' aria-selected="'+r+'"/><label for="'+s.id+"_sourcechooser_"+o+t+'" aria-hidden="true">'+o+" ("+t+")</label></li>",s.adjustSourcechooserBox()},adjustSourcechooserBox:function(){var e=this;e.sourcechooserButton.querySelector("."+e.options.classPrefix+"sourcechooser-selector").style.height=parseFloat(e.sourcechooserButton.querySelector("."+e.options.classPrefix+"sourcechooser-selector ul").offsetHeight)+"px"},hideSourcechooserSelector:function(){var e=this;if(void 0!==e.sourcechooserButton&&e.sourcechooserButton.querySelector("input[type=radio]")){var o=e.sourcechooserButton.querySelector("."+e.options.classPrefix+"sourcechooser-selector"),t=o.querySelectorAll("input[type=radio]");o.setAttribute("aria-expanded","false"),o.setAttribute("aria-hidden","true"),mejs.Utils.addClass(o,e.options.classPrefix+"offscreen");for(var r=0,s=t.length;r<s;r++)t[r].setAttribute("tabindex","-1")}},showSourcechooserSelector:function(){var e=this;if(void 0!==e.sourcechooserButton&&e.sourcechooserButton.querySelector("input[type=radio]")){var o=e.sourcechooserButton.querySelector("."+e.options.classPrefix+"sourcechooser-selector"),t=o.querySelectorAll("input[type=radio]");o.setAttribute("aria-expanded","true"),o.setAttribute("aria-hidden","false"),mejs.Utils.removeClass(o,e.options.classPrefix+"offscreen");for(var r=0,s=t.length;r<s;r++)t[r].setAttribute("tabindex","0")}}})},{}]},{},[1]);