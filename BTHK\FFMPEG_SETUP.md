# دليل تثبيت FFmpeg للنظام

## ما هو FFmpeg؟
FFmpeg هو أداة قوية لمعالجة الفيديو والصوت، وهو ضروري لتقاط الفيديو من أجهزة USB/HDMI وتحويله إلى بث مباشر.

## تثبيت FFmpeg على Windows:

### الطريقة الأولى (الأسهل):
1. اذهب إلى: https://www.gyan.dev/ffmpeg/builds/
2. حمل "release builds" -> "ffmpeg-release-essentials.zip"
3. فك الضغط في مجلد مثل: `C:\ffmpeg`
4. أضف `C:\ffmpeg\bin` إلى متغير البيئة PATH:
   - اضغط Win + R واكتب `sysdm.cpl`
   - اذهب إلى تبويب "Advanced" -> "Environment Variables"
   - في "System Variables" ابحث عن "Path" واضغط "Edit"
   - اضغط "New" وأضف: `C:\ffmpeg\bin`
   - اضغط OK على جميع النوافذ

### الطريقة الثانية (باستخدام Chocolatey):
1. ثبت Chocolatey أولاً من: https://chocolatey.org/install
2. افتح PowerShell كمدير (Run as Administrator)
3. اكتب: `choco install ffmpeg`

### الطريقة الثالثة (باستخدام Winget):
1. افتح Command Prompt أو PowerShell
2. اكتب: `winget install ffmpeg`

## التحقق من التثبيت:
1. افتح Command Prompt
2. اكتب: `ffmpeg -version`
3. يجب أن تظهر معلومات الإصدار

## إعداد أجهزة USB/HDMI:

### أجهزة التقاط الفيديو المدعومة:
- Elgato Game Capture HD
- AVerMedia Live Gamer
- Blackmagic Design devices
- Generic USB Video Class (UVC) devices
- HDMI capture cards

### خطوات الإعداد:
1. وصل جهاز التقاط الفيديو بـ USB
2. وصل مصدر الفيديو (كاميرا، جهاز ألعاب، إلخ) بـ HDMI
3. تأكد من تثبيت تعريفات الجهاز
4. افتح لوحة التحكم في النظام
5. اذهب إلى تبويب "التقاط المباشر"
6. اضغط "تحديث" لرؤية الأجهزة المتاحة

## استكشاف الأخطاء:

### مشكلة: لا تظهر أجهزة الفيديو
**الحل:**
- تأكد من تثبيت تعريفات الجهاز
- جرب منافذ USB مختلفة
- أعد تشغيل الكمبيوتر بعد توصيل الجهاز

### مشكلة: جودة الفيديو ضعيفة
**الحل:**
- زد معدل البت (Bitrate) في الإعدادات
- اختر دقة أعلى إذا كان الجهاز يدعمها
- تأكد من قوة إشارة HDMI

### مشكلة: تأخير في البث
**الحل:**
- قلل معدل البت
- استخدم دقة أقل
- تأكد من قوة الكمبيوتر

### مشكلة: لا يعمل الصوت
**الحل:**
- تأكد من اختيار جهاز الصوت الصحيح
- تحقق من إعدادات الصوت في Windows
- جرب معدل عينة صوت مختلف

## إعدادات مُوصى بها:

### للبث عالي الجودة:
- الدقة: 1920x1080
- معدل الإطارات: 30 FPS
- معدل البت: 4000-6000 kbps

### للبث متوسط الجودة:
- الدقة: 1280x720
- معدل الإطارات: 30 FPS
- معدل البت: 2000-3000 kbps

### للبث منخفض الجودة (للإنترنت البطيء):
- الدقة: 854x480
- معدل الإطارات: 25 FPS
- معدل البت: 1000-1500 kbps

## ملاحظات مهمة:
- تأكد من قوة الكمبيوتر قبل استخدام إعدادات عالية
- استخدم كابل HDMI عالي الجودة
- أغلق البرامج غير الضرورية أثناء البث
- راقب استخدام المعالج والذاكرة

## الدعم:
إذا واجهت مشاكل، تحقق من:
1. حالة FFmpeg في لوحة التحكم
2. سجلات الأخطاء في نافذة الأوامر
3. إعدادات الجهاز في Windows Device Manager
