'use strict';/*!
 * This is a `i18n` language object.
 *
 * Hungarian
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.hu === undefined) {
		exports.hu = {
			'mejs.plural-form': 1,			
			'mejs.download-file': 'Fájl letöltése',			
			'mejs.install-flash': '<PERSON><PERSON><PERSON> b<PERSON>, amely<PERSON><PERSON> nincs engedélyezve vagy telepítve a Flash player. K<PERSON><PERSON><PERSON><PERSON><PERSON>, kapcsolja be a Flash-lej<PERSON><PERSON><PERSON><PERSON> bőv<PERSON>, vagy töltse le a legfrissebb verziót a https://get.adobe.com/flashplayer/ címen',			
			'mejs.fullscreen': 'Teljes képerny<PERSON>',			
			'mejs.play': 'Lej<PERSON>tsz<PERSON>',
			'mejs.pause': '<PERSON><PERSON><PERSON><PERSON>',			
			'mejs.time-slider': '<PERSON><PERSON><PERSON>',
			'mejs.time-help-text': 'Használja a Bal/Jobb nyíl gombokat az egy másodperces léptetéshez, a Fel/Le nyíl gombokat a tíz másodperces léptetéshez.',
			'mejs.live-broadcast' : 'Élő közvetítés',			
			'mejs.volume-help-text': 'Használja a Fel/Le nyíl gombokat a hangerő növeléséhez vagy csökkentéséhez.',
			'mejs.unmute': 'Némítás feloldása',
			'mejs.mute': 'Némítás',
			'mejs.volume-slider': 'Hangerőcsúszka',			
			'mejs.video-player': 'Videolejátszó',
			'mejs.audio-player': 'Audiolejátszó',			
			'mejs.captions-subtitles': 'Képaláírás/Feliratok',
			'mejs.captions-chapters': 'Fejezetek',
			'mejs.none': 'Nincs',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albán',
			'mejs.arabic': 'Arab',
			'mejs.belarusian': 'Belorusz',
			'mejs.bulgarian': 'Bolgár',
			'mejs.catalan': 'Katalán',
			'mejs.chinese': 'Kínai',
			'mejs.chinese-simplified': 'Kínai (Egyszerűsített)',
			'mejs.chinese-traditional': 'Kínai (Hagyományos)',
			'mejs.croatian': 'Horvát',
			'mejs.czech': 'Cseh',
			'mejs.danish': 'Dán',
			'mejs.dutch': 'Holland',
			'mejs.english': 'Angol',
			'mejs.estonian': 'Észt',
			'mejs.filipino': 'Filippínó',
			'mejs.finnish': 'Finn',
			'mejs.french': 'Francia',
			'mejs.galician': 'Galíciai',
			'mejs.german': 'Német',
			'mejs.greek': 'Görög',
			'mejs.haitian-creole': 'Haiti Kreol',
			'mejs.hebrew': 'Héber',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Magyar',
			'mejs.icelandic': 'Izlandi',
			'mejs.indonesian': 'Indonéz',
			'mejs.irish': 'Ír',
			'mejs.italian': 'Olasz',
			'mejs.japanese': 'Japán',
			'mejs.korean': 'Koreai',
			'mejs.latvian': 'Lett',
			'mejs.lithuanian': 'Litván',
			'mejs.macedonian': 'Macedóniai',
			'mejs.malay': 'Maláj',
			'mejs.maltese': 'Máltai',
			'mejs.norwegian': 'Norvég',
			'mejs.persian': 'Perzsa',
			'mejs.polish': 'Lengyel',
			'mejs.portuguese': 'Portugál',
			'mejs.romanian': 'Román',
			'mejs.russian': 'Orosz',
			'mejs.serbian': 'Szerb',
			'mejs.slovak': 'Szlovák',
			'mejs.slovenian': 'Szlovén',
			'mejs.spanish': 'Spanyol',
			'mejs.swahili': 'Szuahéli',
			'mejs.swedish': 'Svéd',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thai',
			'mejs.turkish': 'Török',
			'mejs.ukrainian': 'Ukrán',
			'mejs.vietnamese': 'Vietnami',
			'mejs.welsh': 'Walesi',
			'mejs.yiddish': 'Jiddis'
		};
	}
})(mejs.i18n);