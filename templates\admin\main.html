<!DOCTYPE html>
<html>
  {% include "admin/head.html" %}
  <body dir="rtl" class="noselect">
    {% include "admin/mnu.html" %}

    <div class="nopadding">
      <div class="page">
        <div class="msg info">يمكنك الان تعديل اسم المستخدم وكلمه المرور واسم الشبكه من هنا</div>

        <form
          action="/{{admin_panel_path}}/save/{{admin_page}}"
          id="saveForm"
          method="POST"
          enctype="multipart/form-data"
        >
          <div class="content">
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اسم المستخدم</div>
                <div class="in-e col-sm-10">
                  <input name="u" id="u" value="{{main.u}}" placeholder="اسم المستخدم" />
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">كلمة المرور</div>
                <div class="in-e col-sm-10">
                  <input name="p" id="p" value="{{main.p}}" type="password" placeholder="كلمة المرور" />
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اسم الشبكه</div>
                <div class="in-e col-sm-10">
                  <input name="main_name" id="main_name" value="{{main.main_name}}" placeholder="اسم الشبكه هنا " />
                </div>
              </div>
            </div>
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">عنوان الاي بي لنظام مباشر</div>
                <div class="in-e col-sm-10">
                  <input name="ip" id="ip" value="{{main.ip}}" placeholder="اسم الشبكه هنا " />
                </div>
              </div>
            </div>
            <hr class="hard" />
            <div class="input">
              <div class="row nopadding">
                <div class="sbmt-btn" id="saveServer">
                  <div class="reg-icon icon col-xs-4 fa fa-plus"></div>
                  <div class="text col-xs-8">حفظ</div>
                </div>
              </div>
            </div>
          </div>
        </form>

        <script>
          $("#saveServer").click(function () {
            var formData = new FormData($("#saveForm")[0]);

            $.ajax({
              url: $("#saveForm").attr("action"),
              type: "POST",
              data: formData,
              success: function (data) {
                if (data.msg == "ok") {
                  setTimeout(function () {
                    swal("تم الحفظ بنجاح", "تم حفظ بياناتك بنجاح", "success");
                  }, 500);

                  table.ajax.reload();
                } else {
                  swal("خطاء", "حدث خطاء غير معروف!", "error");
                }
              },
              error: function () {},
              cache: false,
              contentType: false,
              processData: false,
            });
          });
        </script>
      </div>
    </div>

    {% include "admin/footer.html" %}
  </body>
</html>
