'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.time-skip-back'] = ['Retornar 1 segon', 'Retornar %1 segons'];
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.time-skip-back'] = ['Přeskočte zpět o 1 sekundu', 'Přeskočte zpět %1 vteřiny', '<PERSON><PERSON><PERSON><PERSON><PERSON>te zpět %1 sekund'];
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.time-skip-back'] = ['1 Sekunde zurückspulen', '%1 Sekunden zurückspulen'];
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.time-skip-back'] = ['Rebobinar 1 segundo', 'Rebobinar %1 segundos'];
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.time-skip-back'] = ['Reculer de %1 seconde', 'Reculer de %1 secondes'];
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.time-skip-back'] = '%1 ثانیه به عقب برگردید';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.time-skip-back'] = ['Skoči natrag 1 sekundu', 'Skoči natrag %1 sekunde', 'Skoči natrag %1 sekundi'];
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.time-skip-back'] = ['Menj vissza 1 másodpercig', 'Ugrás vissza %1 másodperccel'];
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.time-skip-back'] = ['Riavvolgere 1 secondo', 'Riavvolgere %1 secondi'];
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.time-skip-back'] = '%1秒スキップバックする';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.time-skip-back'] = '%1초 를 뒤로 건너뛰세요';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.time-skip-back'] = ['Sla 1 seconde terug', 'Sla %1 seconden terug'];
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.time-skip-back'] = ['Cofnij o 1 sekundę', 'Cofnij o %1 sekundy', 'Cofnij o %1 sekund'];
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.time-skip-back'] = ['Retroceder %1 segundo', 'Retroceder %1 segundos'];
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.time-skip-back'] = ['Treceți înapoi 1 secundă', 'Treceți înapoi în %1 secunde'];
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.time-skip-back'] = ['Перейти назад на %1 секунду', 'Перейти назад на %1 секунды', 'Перейти назад на %1 секунд'];
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.time-skip-back'] = ['Preskočte späť 1 sekundu', 'Preskočte %1 sekundy', 'Preskočte späť %1 sekúnd'];
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.time-skip-back'] = ['Hoppa tillbaka 1 sekund', 'Hoppa tillbaka %1 sekunder'];
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.time-skip-back'] = ['Перейти назад на %1 секунду', 'Перейти назад на %1 секунди', 'Перейти назад на %1 секунд'];
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.time-skip-back'] = '跳躍式迴繞%1秒';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.time-skip-back'] = '后退%1秒';
}