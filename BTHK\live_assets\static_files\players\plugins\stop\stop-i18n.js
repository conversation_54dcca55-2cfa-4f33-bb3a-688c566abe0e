'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.stop'] = 'Aturar';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.stop'] = 'Stop';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.stop'] = 'Stop';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.stop'] = 'Parar';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.stop'] = 'متوقف کردن';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.stop'] = 'Stop';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.stop'] = 'Zaustavi';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.stop'] = 'Állj meg';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.stop'] = 'Stop';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.stop'] = '止';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.stop'] = '중지';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.stop'] = 'Hou op';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.stop'] = 'Zatrzymaj';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.stop'] = 'Pare';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.stop'] = 'Stop';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.stop'] = 'Остановить';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.stop'] = 'Prestať';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.stop'] = 'Stoppa';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.stop'] = 'Зупинити';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.stop'] = '停止';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.stop'] = '停止';
}