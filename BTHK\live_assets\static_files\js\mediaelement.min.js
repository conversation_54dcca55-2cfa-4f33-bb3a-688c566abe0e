/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,n,r){function i(a,l){if(!n[a]){if(!t[a]){var s="function"==typeof require&&require;if(!l&&s)return s(a,!0);if(o)return o(a,!0);var d=new Error("Cannot find module '"+a+"'");throw d.code="MODULE_NOT_FOUND",d}var u=n[a]={exports:{}};t[a][0].call(u.exports,function(e){var n=t[a][1][e];return i(n||e)},u,u.exports,e,t,n,r)}return n[a].exports}for(var o="function"==typeof require&&require,a=0;a<r.length;a++)i(r[a]);return i}({1:[function(e,t,n){},{}],2:[function(e,t,n){(function(n){var r,i=void 0!==n?n:"undefined"!=typeof window?window:{},o=e(1);"undefined"!=typeof document?r=document:(r=i["__GLOBAL_DOCUMENT_CACHE@4"])||(r=i["__GLOBAL_DOCUMENT_CACHE@4"]=o),t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{1:1}],3:[function(e,t,n){(function(e){var n;n="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{},t.exports=n}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(e,t,n){!function(e){function n(){}function r(e,t){return function(){e.apply(t,arguments)}}function i(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],u(e,this)}function o(e,t){for(;3===e._state;)e=e._value;0!==e._state?(e._handled=!0,i._immediateFn(function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var r;try{r=n(e._value)}catch(e){return void l(t.promise,e)}a(t.promise,r)}else(1===e._state?a:l)(t.promise,e._value)})):e._deferreds.push(t)}function a(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if(t instanceof i)return e._state=3,e._value=t,void s(e);if("function"==typeof n)return void u(r(n,t),e)}e._state=1,e._value=t,s(e)}catch(t){l(e,t)}}function l(e,t){e._state=2,e._value=t,s(e)}function s(e){2===e._state&&0===e._deferreds.length&&i._immediateFn(function(){e._handled||i._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)o(e,e._deferreds[t]);e._deferreds=null}function d(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function u(e,t){var n=!1;try{e(function(e){n||(n=!0,a(t,e))},function(e){n||(n=!0,l(t,e))})}catch(e){if(n)return;n=!0,l(t,e)}}var c=setTimeout;i.prototype.catch=function(e){return this.then(null,e)},i.prototype.then=function(e,t){var r=new this.constructor(n);return o(this,new d(e,t,r)),r},i.all=function(e){var t=Array.prototype.slice.call(e);return new i(function(e,n){function r(o,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var l=a.then;if("function"==typeof l)return void l.call(a,function(e){r(o,e)},n)}t[o]=a,0==--i&&e(t)}catch(e){n(e)}}if(0===t.length)return e([]);for(var i=t.length,o=0;o<t.length;o++)r(o,t[o])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(t){t(e)})},i.reject=function(e){return new i(function(t,n){n(e)})},i.race=function(e){return new i(function(t,n){for(var r=0,i=e.length;r<i;r++)e[r].then(t,n)})},i._immediateFn="function"==typeof setImmediate&&function(e){setImmediate(e)}||function(e){c(e,0)},i._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},i._setImmediateFn=function(e){i._immediateFn=e},i._setUnhandledRejectionFn=function(e){i._unhandledRejectionFn=e},void 0!==t&&t.exports?t.exports=i:e.Promise||(e.Promise=i)}(this)},{}],5:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(e){return e&&e.__esModule?e:{default:e}}(e(7)),o=e(9),a=e(18),l={lang:"en",en:o.EN};l.language=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(null!==t&&void 0!==t&&t.length){if("string"!=typeof t[0])throw new TypeError("Language code must be a string value");if(!/^[a-z]{2,3}((\-|_)[a-z]{2})?$/i.test(t[0]))throw new TypeError("Language code must have format 2-3 letters and. optionally, hyphen, underscore followed by 2 more letters");l.lang=t[0],void 0===l[t[0]]?(t[1]=null!==t[1]&&void 0!==t[1]&&"object"===r(t[1])?t[1]:{},l[t[0]]=(0,a.isObjectEmpty)(t[1])?o.EN:t[1]):null!==t[1]&&void 0!==t[1]&&"object"===r(t[1])&&(l[t[0]]=t[1])}return l.lang},l.t=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e&&e.length){var n=void 0,i=void 0,o=l.language(),s=function(e,t,n){return"object"!==(void 0===e?"undefined":r(e))||"number"!=typeof t||"number"!=typeof n?e:[function(){return arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 0===(arguments.length<=0?void 0:arguments[0])||1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:0!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])||11===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])||12===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>0&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])>=2&&(arguments.length<=0?void 0:arguments[0])<=4?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%100==1?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100==2?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100==3||(arguments.length<=0?void 0:arguments[0])%100==4?arguments.length<=4?void 0:arguments[4]:arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<7?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])>6&&(arguments.length<=0?void 0:arguments[0])<11?arguments.length<=4?void 0:arguments[4]:arguments.length<=5?void 0:arguments[5]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100>=3&&(arguments.length<=0?void 0:arguments[0])%100<=10?arguments.length<=4?void 0:arguments[4]:(arguments.length<=0?void 0:arguments[0])%100>=11?arguments.length<=5?void 0:arguments[5]:arguments.length<=6?void 0:arguments[6]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>1&&(arguments.length<=0?void 0:arguments[0])%100<11?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100>10&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10==2?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 11!==(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:8!==(arguments.length<=0?void 0:arguments[0])&&11!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:3===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]}][n].apply(null,[t].concat(e))};return void 0!==l[o]&&(n=l[o][e],null!==t&&"number"==typeof t&&(i=l[o]["mejs.plural-form"],n=s.apply(null,[n,t,i]))),!n&&l.en&&(n=l.en[e],null!==t&&"number"==typeof t&&(i=l.en["mejs.plural-form"],n=s.apply(null,[n,t,i]))),n=n||e,null!==t&&"number"==typeof t&&(n=n.replace("%1",t)),(0,a.escapeHTML)(n)}return e},i.default.i18n=l,"undefined"!=typeof mejsL10n&&i.default.i18n.language(mejsL10n.language,mejsL10n.strings),n.default=l},{18:18,7:7,9:9}],6:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=r(e(3)),l=r(e(2)),s=r(e(7)),d=e(18),u=e(19),c=e(8),f=e(16),m=function e(t,n,r){var m=this;i(this,e);var p=this;r=Array.isArray(r)?r:null,p.defaults={renderers:[],fakeNodeName:"mediaelementwrapper",pluginPath:"build/",shimScriptAccess:"sameDomain"},n=Object.assign(p.defaults,n),p.mediaElement=l.default.createElement(n.fakeNodeName);var h=t,v=!1;if("string"==typeof t?p.mediaElement.originalNode=l.default.getElementById(t):(p.mediaElement.originalNode=t,h=t.id),void 0===p.mediaElement.originalNode||null===p.mediaElement.originalNode)return null;p.mediaElement.options=n,h=h||"mejs_"+Math.random().toString().slice(2),p.mediaElement.originalNode.setAttribute("id",h+"_from_mejs");var g=p.mediaElement.originalNode.tagName.toLowerCase();["video","audio"].indexOf(g)>-1&&!p.mediaElement.originalNode.getAttribute("preload")&&p.mediaElement.originalNode.setAttribute("preload","none"),p.mediaElement.originalNode.parentNode.insertBefore(p.mediaElement,p.mediaElement.originalNode),p.mediaElement.appendChild(p.mediaElement.originalNode);var y=function(e,t){if("https:"===a.default.location.protocol&&0===e.indexOf("http:")&&f.IS_IOS&&s.default.html5media.mediaTypes.indexOf(t)>-1){var n=new XMLHttpRequest;n.onreadystatechange=function(){if(4===this.readyState&&200===this.status){var t=(a.default.URL||a.default.webkitURL).createObjectURL(this.response);return p.mediaElement.originalNode.setAttribute("src",t),t}return e},n.open("GET",e),n.responseType="blob",n.send()}return e},E=void 0;if(null!==r)E=r;else if(null!==p.mediaElement.originalNode)switch(E=[],p.mediaElement.originalNode.nodeName.toLowerCase()){case"iframe":E.push({type:"",src:p.mediaElement.originalNode.getAttribute("src")});break;case"audio":case"video":var b=p.mediaElement.originalNode.children.length,w=p.mediaElement.originalNode.getAttribute("src");if(w){var _=p.mediaElement.originalNode,S=(0,u.formatType)(w,_.getAttribute("type"));E.push({type:S,src:y(w,S)})}for(var N=0;N<b;N++){var j=p.mediaElement.originalNode.children[N];if("source"===j.tagName.toLowerCase()){var A=j.getAttribute("src"),T=(0,u.formatType)(A,j.getAttribute("type"));E.push({type:T,src:y(A,T)})}}}p.mediaElement.id=h,p.mediaElement.renderers={},p.mediaElement.events={},p.mediaElement.promises=[],p.mediaElement.renderer=null,p.mediaElement.rendererName=null,p.mediaElement.changeRenderer=function(e,t){var n=m,r=Object.keys(t[0]).length>2?t[0]:t[0].src;if(void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&n.mediaElement.renderer.name===e)return n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.show(),n.mediaElement.renderer.setSrc(r),!0;void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&(n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.hide());var i=n.mediaElement.renderers[e],o=null;if(void 0!==i&&null!==i)return i.show(),i.setSrc(r),n.mediaElement.renderer=i,n.mediaElement.rendererName=e,!0;for(var a=n.mediaElement.options.renderers.length?n.mediaElement.options.renderers:c.renderer.order,l=0,s=a.length;l<s;l++){var d=a[l];if(d===e){o=c.renderer.renderers[d];var u=Object.assign(o.options,n.mediaElement.options);return i=o.create(n.mediaElement,u,t),i.name=e,n.mediaElement.renderers[o.name]=i,n.mediaElement.renderer=i,n.mediaElement.rendererName=e,i.show(),!0}}return!1},p.mediaElement.setSize=function(e,t){void 0!==p.mediaElement.renderer&&null!==p.mediaElement.renderer&&p.mediaElement.renderer.setSize(e,t)},p.mediaElement.generateError=function(e,t){e=e||"",t=Array.isArray(t)?t:[];var n=(0,d.createEvent)("error",p.mediaElement);n.message=e,n.urls=t,p.mediaElement.dispatchEvent(n),v=!0};var F=s.default.html5media.properties,P=s.default.html5media.methods,x=function(e,t,n,r){var i=e[t];Object.defineProperty(e,t,{get:function(){return n.apply(e,[i])},set:function(t){return i=r.apply(e,[t])}})},L=function(){return void 0!==p.mediaElement.renderer&&null!==p.mediaElement.renderer?p.mediaElement.renderer.getSrc():null},O=function(e){var t=[];if("string"==typeof e)t.push({src:e,type:e?(0,u.getTypeFromFile)(e):""});else if("object"===(void 0===e?"undefined":o(e))&&void 0!==e.src){var n=(0,u.absolutizeUrl)(e.src),r=e.type,i=Object.assign(e,{src:n,type:""!==r&&null!==r&&void 0!==r||!n?r:(0,u.getTypeFromFile)(n)});t.push(i)}else if(Array.isArray(e))for(var a=0,l=e.length;a<l;a++){var s=(0,u.absolutizeUrl)(e[a].src),f=e[a].type,m=Object.assign(e[a],{src:s,type:""!==f&&null!==f&&void 0!==f||!s?f:(0,u.getTypeFromFile)(s)});t.push(m)}var h=c.renderer.select(t,p.mediaElement.options.renderers.length?p.mediaElement.options.renderers:[]),v=void 0;if(p.mediaElement.paused||(p.mediaElement.pause(),v=(0,d.createEvent)("pause",p.mediaElement),p.mediaElement.dispatchEvent(v)),p.mediaElement.originalNode.src=t[0].src||"",null!==h||!t[0].src)return t[0].src?p.mediaElement.changeRenderer(h.rendererName,t):null;p.mediaElement.generateError("No renderer found",t)},C=function(e,t){try{if("play"===e&&"native_dash"===p.mediaElement.rendererName){var n=p.mediaElement.renderer[e](t);n&&"function"==typeof n.then&&n.catch(function(){p.mediaElement.paused&&setTimeout(function(){var e=p.mediaElement.renderer.play();void 0!==e&&e.catch(function(){p.mediaElement.renderer.paused||p.mediaElement.renderer.pause()})},150)})}else p.mediaElement.renderer[e](t)}catch(e){p.mediaElement.generateError(e,E)}};x(p.mediaElement,"src",L,O),p.mediaElement.getSrc=L,p.mediaElement.setSrc=O;for(var I=0,k=F.length;I<k;I++)!function(e){if("src"!==e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1),n=function(){return void 0!==p.mediaElement.renderer&&null!==p.mediaElement.renderer&&"function"==typeof p.mediaElement.renderer["get"+t]?p.mediaElement.renderer["get"+t]():null},r=function(e){void 0!==p.mediaElement.renderer&&null!==p.mediaElement.renderer&&"function"==typeof p.mediaElement.renderer["set"+t]&&p.mediaElement.renderer["set"+t](e)};x(p.mediaElement,e,n,r),p.mediaElement["get"+t]=n,p.mediaElement["set"+t]=r}}(F[I]);for(var U=0,M=P.length;U<M;U++)!function(e){p.mediaElement[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return void 0!==p.mediaElement.renderer&&null!==p.mediaElement.renderer&&"function"==typeof p.mediaElement.renderer[e]&&(p.mediaElement.promises.length?Promise.all(p.mediaElement.promises).then(function(){C(e,n)}).catch(function(e){p.mediaElement.generateError(e,E)}):C(e,n)),null}}(P[U]);return p.mediaElement.addEventListener=function(e,t){p.mediaElement.events[e]=p.mediaElement.events[e]||[],p.mediaElement.events[e].push(t)},p.mediaElement.removeEventListener=function(e,t){if(!e)return p.mediaElement.events={},!0;var n=p.mediaElement.events[e];if(!n)return!0;if(!t)return p.mediaElement.events[e]=[],!0;for(var r=0;r<n.length;r++)if(n[r]===t)return p.mediaElement.events[e].splice(r,1),!0;return!1},p.mediaElement.dispatchEvent=function(e){var t=p.mediaElement.events[e.type];if(t)for(var n=0;n<t.length;n++)t[n].apply(null,[e])},p.mediaElement.destroy=function(){var e=p.mediaElement.originalNode.cloneNode(!0),t=p.mediaElement.parentElement;e.removeAttribute("id"),e.remove(),p.mediaElement.remove(),t.append(e)},E.length&&(p.mediaElement.src=E),p.mediaElement.promises.length?Promise.all(p.mediaElement.promises).then(function(){p.mediaElement.options.success&&p.mediaElement.options.success(p.mediaElement,p.mediaElement.originalNode)}).catch(function(){v&&p.mediaElement.options.error&&p.mediaElement.options.error(p.mediaElement,p.mediaElement.originalNode)}):(p.mediaElement.options.success&&p.mediaElement.options.success(p.mediaElement,p.mediaElement.originalNode),v&&p.mediaElement.options.error&&p.mediaElement.options.error(p.mediaElement,p.mediaElement.originalNode)),p.mediaElement};a.default.MediaElement=m,s.default.MediaElement=m,n.default=m},{16:16,18:18,19:19,2:2,3:3,7:7,8:8}],7:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=function(e){return e&&e.__esModule?e:{default:e}}(e(3)),i={};i.version="4.2.9",i.html5media={properties:["volume","src","currentTime","muted","duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable","currentSrc","preload","bufferedBytes","bufferedTime","initialTime","startOffsetTime","defaultPlaybackRate","playbackRate","played","autoplay","loop","controls"],readOnlyProperties:["duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable"],methods:["load","play","pause","canPlayType"],events:["loadstart","durationchange","loadedmetadata","loadeddata","progress","canplay","canplaythrough","suspend","abort","error","emptied","stalled","play","playing","pause","waiting","seeking","seeked","timeupdate","ended","ratechange","volumechange"],mediaTypes:["audio/mp3","audio/ogg","audio/oga","audio/wav","audio/x-wav","audio/wave","audio/x-pn-wav","audio/mpeg","audio/mp4","video/mp4","video/webm","video/ogg","video/ogv"]},r.default.mejs=i,n.default=i},{3:3}],8:[function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0}),n.renderer=void 0;var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),a=function(e){return e&&e.__esModule?e:{default:e}}(e(7)),l=function(){function e(){r(this,e),this.renderers={},this.order=[]}return o(e,[{key:"add",value:function(e){if(void 0===e.name)throw new TypeError("renderer must contain at least `name` property");this.renderers[e.name]=e,this.order.push(e.name)}},{key:"select",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=t.length;if(t=t.length?t:this.order,!n){var r=[/^(html5|native)/i,/^flash/i,/iframe$/i],i=function(e){for(var t=0,n=r.length;t<n;t++)if(r[t].test(e))return t;return r.length};t.sort(function(e,t){return i(e)-i(t)})}for(var o=0,a=t.length;o<a;o++){var l=t[o],s=this.renderers[l];if(null!==s&&void 0!==s)for(var d=0,u=e.length;d<u;d++)if("function"==typeof s.canPlayType&&"string"==typeof e[d].type&&s.canPlayType(e[d].type))return{rendererName:s.name,src:e[d].src}}return null}},{key:"order",set:function(e){if(!Array.isArray(e))throw new TypeError("order must be an array of strings.");this._order=e},get:function(){return this._order}},{key:"renderers",set:function(e){if(null!==e&&"object"!==(void 0===e?"undefined":i(e)))throw new TypeError("renderers must be an array of objects.");this._renderers=e},get:function(){return this._renderers}}]),e}(),s=n.renderer=new l;a.default.Renderers=s},{7:7}],9:[function(e,t,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0});n.EN={"mejs.plural-form":1,"mejs.download-file":"Download File","mejs.install-flash":"You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/","mejs.fullscreen":"Fullscreen","mejs.play":"Play","mejs.pause":"Pause","mejs.time-slider":"Time Slider","mejs.time-help-text":"Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds.","mejs.live-broadcast":"Live Broadcast","mejs.volume-help-text":"Use Up/Down Arrow keys to increase or decrease volume.","mejs.unmute":"Unmute","mejs.mute":"Mute","mejs.volume-slider":"Volume Slider","mejs.video-player":"Video Player","mejs.audio-player":"Audio Player","mejs.captions-subtitles":"Captions/Subtitles","mejs.captions-chapters":"Chapters","mejs.none":"None","mejs.afrikaans":"Afrikaans","mejs.albanian":"Albanian","mejs.arabic":"Arabic","mejs.belarusian":"Belarusian","mejs.bulgarian":"Bulgarian","mejs.catalan":"Catalan","mejs.chinese":"Chinese","mejs.chinese-simplified":"Chinese (Simplified)","mejs.chinese-traditional":"Chinese (Traditional)","mejs.croatian":"Croatian","mejs.czech":"Czech","mejs.danish":"Danish","mejs.dutch":"Dutch","mejs.english":"English","mejs.estonian":"Estonian","mejs.filipino":"Filipino","mejs.finnish":"Finnish","mejs.french":"French","mejs.galician":"Galician","mejs.german":"German","mejs.greek":"Greek","mejs.haitian-creole":"Haitian Creole","mejs.hebrew":"Hebrew","mejs.hindi":"Hindi","mejs.hungarian":"Hungarian","mejs.icelandic":"Icelandic","mejs.indonesian":"Indonesian","mejs.irish":"Irish","mejs.italian":"Italian","mejs.japanese":"Japanese","mejs.korean":"Korean","mejs.latvian":"Latvian","mejs.lithuanian":"Lithuanian","mejs.macedonian":"Macedonian","mejs.malay":"Malay","mejs.maltese":"Maltese","mejs.norwegian":"Norwegian","mejs.persian":"Persian","mejs.polish":"Polish","mejs.portuguese":"Portuguese","mejs.romanian":"Romanian","mejs.russian":"Russian","mejs.serbian":"Serbian","mejs.slovak":"Slovak","mejs.slovenian":"Slovenian","mejs.spanish":"Spanish","mejs.swahili":"Swahili","mejs.swedish":"Swedish","mejs.tagalog":"Tagalog","mejs.thai":"Thai","mejs.turkish":"Turkish","mejs.ukrainian":"Ukrainian","mejs.vietnamese":"Vietnamese","mejs.welsh":"Welsh","mejs.yiddish":"Yiddish"}},{}],10:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=r(e(3)),a=r(e(7)),l=e(8),s=e(18),d=e(19),u=e(16),c=e(17),f={promise:null,load:function(e){return"undefined"!=typeof dashjs?f.promise=new Promise(function(e){e()}).then(function(){f._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.dashjs.org/latest/dash.all.min.js",f.promise=f.promise||(0,c.loadScript)(e.options.path),f.promise.then(function(){f._createPlayer(e)})),f.promise},_createPlayer:function(e){var t=dashjs.MediaPlayer().create();return o.default["__ready__"+e.id](t),t}},m={name:"native_dash",options:{prefix:"native_dash",dash:{path:"https://cdn.dashjs.org/latest/dash.all.min.js",debug:!1,drm:{},robustnessLevel:""}},canPlayType:function(e){return u.HAS_MSE&&["application/dash+xml"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var r=e.originalNode,d=e.id+"_"+t.prefix,u=r.autoplay,c=r.children,m=null,p=null;r.removeAttribute("type");for(var h=0,v=c.length;h<v;h++)c[h].removeAttribute("type");m=r.cloneNode(!0),t=Object.assign(t,e.options);for(var g=a.default.html5media.properties,y=a.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),E=function(t){var n=(0,s.createEvent)(t.type,e);e.dispatchEvent(n)},b=0,w=g.length;b<w;b++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);m["get"+n]=function(){return null!==p?m[e]:null},m["set"+n]=function(n){if(-1===a.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){var r="object"===(void 0===n?"undefined":i(n))&&n.src?n.src:n;if(m[e]=r,null!==p){p.reset();for(var o=0,l=y.length;o<l;o++)m.removeEventListener(y[o],E);p=f._createPlayer({options:t.dash,id:d}),n&&"object"===(void 0===n?"undefined":i(n))&&"object"===i(n.drm)&&(p.setProtectionData(n.drm),(0,s.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(r),u&&p.play()}}else m[e]=n}}(g[b]);if(o.default["__ready__"+d]=function(n){e.dashPlayer=p=n;for(var r=dashjs.MediaPlayer.events,o=0,l=y.length;o<l;o++)!function(e){"loadedmetadata"===e&&(p.getDebug().setLogToBrowserConsole(t.dash.debug),p.initialize(),p.setScheduleWhilePaused(!1),p.setFastSwitchEnabled(!0),p.attachView(m),p.setAutoPlay(!1),"object"!==i(t.dash.drm)||a.default.Utils.isObjectEmpty(t.dash.drm)||(p.setProtectionData(t.dash.drm),(0,s.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(m.getSrc())),m.addEventListener(e,E)}(y[o]);var d=function(t){if("error"===t.type.toLowerCase())e.generateError(t.message,m.src),console.error(t);else{var n=(0,s.createEvent)(t.type,e);n.data=t,e.dispatchEvent(n)}};for(var u in r)r.hasOwnProperty(u)&&p.on(r[u],function(e){return d(e)})},n&&n.length>0)for(var _=0,S=n.length;_<S;_++)if(l.renderer.renderers[t.prefix].canPlayType(n[_].type)){m.setAttribute("src",n[_].src),void 0!==n[_].drm&&(t.dash.drm=n[_].drm);break}m.setAttribute("id",d),r.parentNode.insertBefore(m,r),r.autoplay=!1,r.style.display="none",m.setSize=function(e,t){return m.style.width=e+"px",m.style.height=t+"px",m},m.hide=function(){return m.pause(),m.style.display="none",m},m.show=function(){return m.style.display="",m},m.destroy=function(){null!==p&&p.reset()};var N=(0,s.createEvent)("rendererready",m);return e.dispatchEvent(N),e.promises.push(f.load({options:t.dash,id:d})),m}};d.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".mpd")?"application/dash+xml":null}),l.renderer.add(m)},{16:16,17:17,18:18,19:19,3:3,7:7,8:8}],11:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0}),n.PluginDetector=void 0;var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=r(e(3)),a=r(e(2)),l=r(e(7)),s=r(e(5)),d=e(8),u=e(18),c=e(16),f=e(19),m=n.PluginDetector={plugins:[],hasPluginVersion:function(e,t){var n=m.plugins[e];return t[1]=t[1]||0,t[2]=t[2]||0,n[0]>t[0]||n[0]===t[0]&&n[1]>t[1]||n[0]===t[0]&&n[1]===t[1]&&n[2]>=t[2]},addPlugin:function(e,t,n,r,i){m.plugins[e]=m.detectPlugin(t,n,r,i)},detectPlugin:function(e,t,n,r){var a=[0,0,0],l=void 0,s=void 0;if(null!==c.NAV.plugins&&void 0!==c.NAV.plugins&&"object"===i(c.NAV.plugins[e])){if((l=c.NAV.plugins[e].description)&&(void 0===c.NAV.mimeTypes||!c.NAV.mimeTypes[t]||c.NAV.mimeTypes[t].enabledPlugin))for(var d=0,u=(a=l.replace(e,"").replace(/^\s+/,"").replace(/\sr/gi,".").split(".")).length;d<u;d++)a[d]=parseInt(a[d].match(/\d+/),10)}else if(void 0!==o.default.ActiveXObject)try{(s=new ActiveXObject(n))&&(a=r(s))}catch(e){}return a}};m.addPlugin("flash","Shockwave Flash","application/x-shockwave-flash","ShockwaveFlash.ShockwaveFlash",function(e){var t=[],n=e.GetVariable("$version");return n&&(n=n.split(" ")[1].split(","),t=[parseInt(n[0],10),parseInt(n[1],10),parseInt(n[2],10)]),t});var p={create:function(e,t,n){var r={},i=!1;r.options=t,r.id=e.id+"_"+r.options.prefix,r.mediaElement=e,r.flashState={},r.flashApi=null,r.flashApiStack=[];for(var m=l.default.html5media.properties,p=0,h=m.length;p<h;p++)!function(e){r.flashState[e]=null;var t=""+e.substring(0,1).toUpperCase()+e.substring(1);r["get"+t]=function(){if(null!==r.flashApi){if("function"==typeof r.flashApi["get_"+e]){var t=r.flashApi["get_"+e]();return"buffered"===e?{start:function(){return 0},end:function(){return t},length:1}:t}return null}return null},r["set"+t]=function(t){if("src"===e&&(t=(0,f.absolutizeUrl)(t)),null!==r.flashApi&&void 0!==r.flashApi["set_"+e])try{r.flashApi["set_"+e](t)}catch(e){}else r.flashApiStack.push({type:"set",propName:e,value:t})}}(m[p]);var v=l.default.html5media.methods;v.push("stop");for(var g=0,y=v.length;g<y;g++)!function(e){r[e]=function(){if(i)if(null!==r.flashApi){if(r.flashApi["fire_"+e])try{r.flashApi["fire_"+e]()}catch(e){}}else r.flashApiStack.push({type:"call",methodName:e})}}(v[g]);for(var E=["rendererready"],b=0,w=E.length;b<w;b++){var _=(0,u.createEvent)(E[b],r);e.dispatchEvent(_)}o.default["__ready__"+r.id]=function(){if(r.flashReady=!0,r.flashApi=a.default.getElementById("__"+r.id),r.flashApiStack.length)for(var e=0,t=r.flashApiStack.length;e<t;e++){var n=r.flashApiStack[e];if("set"===n.type){var i=n.propName,o=""+i.substring(0,1).toUpperCase()+i.substring(1);r["set"+o](n.value)}else"call"===n.type&&r[n.methodName]()}},o.default["__event__"+r.id]=function(e,t){var n=(0,u.createEvent)(e,r);if(t)try{n.data=JSON.parse(t),n.details.data=JSON.parse(t)}catch(e){n.message=t}r.mediaElement.dispatchEvent(n)},r.flashWrapper=a.default.createElement("div"),-1===["always","sameDomain"].indexOf(r.options.shimScriptAccess)&&(r.options.shimScriptAccess="sameDomain");var S=e.originalNode.autoplay,N=["uid="+r.id,"autoplay="+S,"allowScriptAccess="+r.options.shimScriptAccess,"preload="+(e.originalNode.getAttribute("preload")||"")],j=null!==e.originalNode&&"video"===e.originalNode.tagName.toLowerCase(),A=j?e.originalNode.height:1,T=j?e.originalNode.width:1;e.originalNode.getAttribute("src")&&N.push("src="+e.originalNode.getAttribute("src")),!0===r.options.enablePseudoStreaming&&(N.push("pseudostreamstart="+r.options.pseudoStreamingStartQueryParam),N.push("pseudostreamtype="+r.options.pseudoStreamingType)),r.options.streamDelimiter&&N.push("streamdelimiter="+encodeURIComponent(r.options.streamDelimiter)),r.options.proxyType&&N.push("proxytype="+r.options.proxyType),e.appendChild(r.flashWrapper),e.originalNode.style.display="none";var F=[];if(c.IS_IE||c.IS_EDGE){var P=a.default.createElement("div");r.flashWrapper.appendChild(P),F=c.IS_EDGE?['type="application/x-shockwave-flash"','data="'+r.options.pluginPath+r.options.filename+'"','id="__'+r.id+'"','width="'+T+'"','height="'+A+"'\""]:['classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"','codebase="//download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab"','id="__'+r.id+'"','width="'+T+'"','height="'+A+'"'],j||F.push('style="clip: rect(0 0 0 0); position: absolute;"'),P.outerHTML="<object "+F.join(" ")+'><param name="movie" value="'+r.options.pluginPath+r.options.filename+"?x="+new Date+'" /><param name="flashvars" value="'+N.join("&amp;")+'" /><param name="quality" value="high" /><param name="bgcolor" value="#000000" /><param name="wmode" value="transparent" /><param name="allowScriptAccess" value="'+r.options.shimScriptAccess+'" /><param name="allowFullScreen" value="true" /><div>'+s.default.t("mejs.install-flash")+"</div></object>"}else F=['id="__'+r.id+'"','name="__'+r.id+'"','play="true"','loop="false"','quality="high"','bgcolor="#000000"','wmode="transparent"','allowScriptAccess="'+r.options.shimScriptAccess+'"','allowFullScreen="true"','type="application/x-shockwave-flash"','pluginspage="//www.macromedia.com/go/getflashplayer"','src="'+r.options.pluginPath+r.options.filename+'"','flashvars="'+N.join("&")+'"'],j?(F.push('width="'+T+'"'),F.push('height="'+A+'"')):F.push('style="position: fixed; left: -9999em; top: -9999em;"'),r.flashWrapper.innerHTML="<embed "+F.join(" ")+">";if(r.flashNode=r.flashWrapper.lastChild,r.hide=function(){i=!1,j&&(r.flashNode.style.display="none")},r.show=function(){i=!0,j&&(r.flashNode.style.display="")},r.setSize=function(e,t){r.flashNode.style.width=e+"px",r.flashNode.style.height=t+"px",null!==r.flashApi&&"function"==typeof r.flashApi.fire_setSize&&r.flashApi.fire_setSize(e,t)},r.destroy=function(){r.flashNode.remove()},n&&n.length>0)for(var x=0,L=n.length;x<L;x++)if(d.renderer.renderers[t.prefix].canPlayType(n[x].type)){r.setSrc(n[x].src);break}return r}};if(m.hasPluginVersion("flash",[10,0,0])){f.typeChecks.push(function(e){return(e=e.toLowerCase()).startsWith("rtmp")?~e.indexOf(".mp3")?"audio/rtmp":"video/rtmp":/\.og(a|g)/i.test(e)?"audio/ogg":~e.indexOf(".m3u8")?"application/x-mpegURL":~e.indexOf(".mpd")?"application/dash+xml":~e.indexOf(".flv")?"video/flv":null});var h={name:"flash_video",options:{prefix:"flash_video",filename:"mediaelement-flash-video.swf",enablePseudoStreaming:!1,pseudoStreamingStartQueryParam:"start",pseudoStreamingType:"byte",proxyType:"",streamDelimiter:""},canPlayType:function(e){return~["video/mp4","video/rtmp","audio/rtmp","rtmp/mp4","audio/mp4","video/flv","video/x-flv"].indexOf(e.toLowerCase())},create:p.create};d.renderer.add(h);var v={name:"flash_hls",options:{prefix:"flash_hls",filename:"mediaelement-flash-video-hls.swf"},canPlayType:function(e){return~["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())},create:p.create};d.renderer.add(v);var g={name:"flash_dash",options:{prefix:"flash_dash",filename:"mediaelement-flash-video-mdash.swf"},canPlayType:function(e){return~["application/dash+xml"].indexOf(e.toLowerCase())},create:p.create};d.renderer.add(g);var y={name:"flash_audio",options:{prefix:"flash_audio",filename:"mediaelement-flash-audio.swf"},canPlayType:function(e){return~["audio/mp3"].indexOf(e.toLowerCase())},create:p.create};d.renderer.add(y);var E={name:"flash_audio_ogg",options:{prefix:"flash_audio_ogg",filename:"mediaelement-flash-audio-ogg.swf"},canPlayType:function(e){return~["audio/ogg","audio/oga","audio/ogv"].indexOf(e.toLowerCase())},create:p.create};d.renderer.add(E)}},{16:16,18:18,19:19,2:2,3:3,5:5,7:7,8:8}],12:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=r(e(3)),a=r(e(7)),l=e(8),s=e(18),d=e(16),u=e(19),c=e(17),f={promise:null,load:function(e){return"undefined"!=typeof flvjs?f.promise=new Promise(function(e){e()}).then(function(){f._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.jsdelivr.net/npm/flv.js@latest",f.promise=f.promise||(0,c.loadScript)(e.options.path),f.promise.then(function(){f._createPlayer(e)})),f.promise},_createPlayer:function(e){flvjs.LoggingControl.enableDebug=e.options.debug,flvjs.LoggingControl.enableVerbose=e.options.debug;var t=flvjs.createPlayer(e.options,e.configs);return o.default["__ready__"+e.id](t),t}},m={name:"native_flv",options:{prefix:"native_flv",flv:{path:"https://cdn.jsdelivr.net/npm/flv.js@latest",cors:!0,debug:!1}},canPlayType:function(e){return d.HAS_MSE&&["video/x-flv","video/flv"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var r=e.originalNode,d=e.id+"_"+t.prefix,u=null,c=null;u=r.cloneNode(!0),t=Object.assign(t,e.options);for(var m=a.default.html5media.properties,p=a.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),h=function(t){var n=(0,s.createEvent)(t.type,e);e.dispatchEvent(n)},v=0,g=m.length;v<g;v++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);u["get"+n]=function(){return null!==c?u[e]:null},u["set"+n]=function(n){if(-1===a.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){if(u[e]="object"===(void 0===n?"undefined":i(n))&&n.src?n.src:n,null!==c){var r={};r.type="flv",r.url=n,r.cors=t.flv.cors,r.debug=t.flv.debug,r.path=t.flv.path;var o=t.flv.configs;c.destroy();for(var l=0,s=p.length;l<s;l++)u.removeEventListener(p[l],h);(c=f._createPlayer({options:r,configs:o,id:d})).attachMediaElement(u),c.load()}}else u[e]=n}}(m[v]);if(o.default["__ready__"+d]=function(t){e.flvPlayer=c=t;for(var n=flvjs.Events,r=0,i=p.length;r<i;r++)!function(e){"loadedmetadata"===e&&(c.unload(),c.detachMediaElement(),c.attachMediaElement(u),c.load()),u.addEventListener(e,h)}(p[r]);var o=function(t,n){if("error"===t){var r=n[0]+": "+n[1]+" "+n[2].msg;e.generateError(r,u.src)}else{var i=(0,s.createEvent)(t,e);i.data=n,e.dispatchEvent(i)}};for(var a in n)!function(e){n.hasOwnProperty(e)&&c.on(n[e],function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return o(n[e],r)})}(a)},n&&n.length>0)for(var y=0,E=n.length;y<E;y++)if(l.renderer.renderers[t.prefix].canPlayType(n[y].type)){u.setAttribute("src",n[y].src);break}u.setAttribute("id",d),r.parentNode.insertBefore(u,r),r.autoplay=!1,r.style.display="none";var b={};b.type="flv",b.url=u.src,b.cors=t.flv.cors,b.debug=t.flv.debug,b.path=t.flv.path;var w=t.flv.configs;u.setSize=function(e,t){return u.style.width=e+"px",u.style.height=t+"px",u},u.hide=function(){return null!==c&&c.pause(),u.style.display="none",u},u.show=function(){return u.style.display="",u},u.destroy=function(){null!==c&&c.destroy()};var _=(0,s.createEvent)("rendererready",u);return e.dispatchEvent(_),e.promises.push(f.load({options:b,configs:w,id:d})),u}};u.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".flv")?"video/flv":null}),l.renderer.add(m)},{16:16,17:17,18:18,19:19,3:3,7:7,8:8}],13:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=r(e(3)),a=r(e(7)),l=e(8),s=e(18),d=e(16),u=e(19),c=e(17),f={promise:null,load:function(e){return"undefined"!=typeof Hls?f.promise=new Promise(function(e){e()}).then(function(){f._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.jsdelivr.net/npm/hls.js@latest",f.promise=f.promise||(0,c.loadScript)(e.options.path),f.promise.then(function(){f._createPlayer(e)})),f.promise},_createPlayer:function(e){var t=new Hls(e.options);return o.default["__ready__"+e.id](t),t}},m={name:"native_hls",options:{prefix:"native_hls",hls:{path:"https://cdn.jsdelivr.net/npm/hls.js@latest",autoStartLoad:!1,debug:!1}},canPlayType:function(e){return d.HAS_MSE&&["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var r=e.originalNode,d=e.id+"_"+t.prefix,u=r.getAttribute("preload"),c=r.autoplay,m=null,p=null,h=0,v=n.length;p=r.cloneNode(!0),(t=Object.assign(t,e.options)).hls.autoStartLoad=u&&"none"!==u||c;for(var g=a.default.html5media.properties,y=a.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),E=function(t){var n=(0,s.createEvent)(t.type,e);e.dispatchEvent(n)},b=0,w=g.length;b<w;b++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);p["get"+n]=function(){return null!==m?p[e]:null},p["set"+n]=function(n){if(-1===a.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){if(p[e]="object"===(void 0===n?"undefined":i(n))&&n.src?n.src:n,null!==m){m.destroy();for(var r=0,o=y.length;r<o;r++)p.removeEventListener(y[r],E);(m=f._createPlayer({options:t.hls,id:d})).loadSource(n),m.attachMedia(p)}}else p[e]=n}}(g[b]);if(o.default["__ready__"+d]=function(t){e.hlsPlayer=m=t;for(var r=Hls.Events,i=0,o=y.length;i<o;i++)!function(t){if("loadedmetadata"===t){var n=e.originalNode.src;m.detachMedia(),m.loadSource(n),m.attachMedia(p)}p.addEventListener(t,E)}(y[i]);var a=void 0,l=void 0,d=function(t,r){if("hlsError"===t){if(console.warn(r),(r=r[1]).fatal)switch(r.type){case"mediaError":var i=(new Date).getTime();if(!a||i-a>3e3)a=(new Date).getTime(),m.recoverMediaError();else if(!l||i-l>3e3)l=(new Date).getTime(),console.warn("Attempting to swap Audio Codec and recover from media error"),m.swapAudioCodec(),m.recoverMediaError();else{var o="Cannot recover, last media error recovery failed";e.generateError(o,p.src),console.error(o)}break;case"networkError":if("manifestLoadError"===r.details)if(h<v&&void 0!==n[h+1])p.setSrc(n[h++].src),p.load(),p.play();else{e.generateError("Network error",n),console.error("Network error")}else{e.generateError("Network error",n),console.error("Network error")}break;default:m.destroy()}}else{var d=(0,s.createEvent)(t,e);d.data=r,e.dispatchEvent(d)}};for(var u in r)!function(e){r.hasOwnProperty(e)&&m.on(r[e],function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return d(r[e],n)})}(u)},v>0)for(;h<v;h++)if(l.renderer.renderers[t.prefix].canPlayType(n[h].type)){p.setAttribute("src",n[h].src);break}"auto"===u||c||(p.addEventListener("play",function(){null!==m&&m.startLoad()}),p.addEventListener("pause",function(){null!==m&&m.stopLoad()})),p.setAttribute("id",d),r.parentNode.insertBefore(p,r),r.autoplay=!1,r.style.display="none",p.setSize=function(e,t){return p.style.width=e+"px",p.style.height=t+"px",p},p.hide=function(){return p.pause(),p.style.display="none",p},p.show=function(){return p.style.display="",p},p.destroy=function(){null!==m&&(m.stopLoad(),m.destroy())};var _=(0,s.createEvent)("rendererready",p);return e.dispatchEvent(_),e.promises.push(f.load({options:t.hls,id:d})),p}};u.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".m3u8")?"application/x-mpegURL":null}),l.renderer.add(m)},{16:16,17:17,18:18,19:19,3:3,7:7,8:8}],14:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var i=r(e(3)),o=r(e(2)),a=r(e(7)),l=e(8),s=e(18),d=e(16),u={name:"html5",options:{prefix:"html5"},canPlayType:function(e){var t=o.default.createElement("video");return d.IS_ANDROID&&/\/mp(3|4)$/i.test(e)||~["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())&&d.SUPPORTS_NATIVE_HLS?"yes":t.canPlayType?t.canPlayType(e.toLowerCase()).replace(/no/,""):""},create:function(e,t,n){var r=e.id+"_"+t.prefix,i=!1,d=null;void 0===e.originalNode||null===e.originalNode?(d=o.default.createElement("audio"),e.appendChild(d)):d=e.originalNode,d.setAttribute("id",r);for(var u=a.default.html5media.properties,c=0,f=u.length;c<f;c++)!function(e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1);d["get"+t]=function(){return d[e]},d["set"+t]=function(t){-1===a.default.html5media.readOnlyProperties.indexOf(e)&&(d[e]=t)}}(u[c]);for(var m=a.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),p=0,h=m.length;p<h;p++)!function(t){d.addEventListener(t,function(t){if(i){var n=(0,s.createEvent)(t.type,t.target);e.dispatchEvent(n)}})}(m[p]);d.setSize=function(e,t){return d.style.width=e+"px",d.style.height=t+"px",d},d.hide=function(){return i=!1,d.style.display="none",d},d.show=function(){return i=!0,d.style.display="",d};var v=0,g=n.length;if(g>0)for(;v<g;v++)if(l.renderer.renderers[t.prefix].canPlayType(n[v].type)){d.setAttribute("src",n[v].src);break}d.addEventListener("error",function(t){4===t.target.error.code&&i&&(v<g&&void 0!==n[v+1]?(d.src=n[v++].src,d.load(),d.play()):e.generateError("Media error: Format(s) not supported or source(s) not found",n))});var y=(0,s.createEvent)("rendererready",d);return e.dispatchEvent(y),d}};i.default.HtmlMediaElement=a.default.HtmlMediaElement=u,l.renderer.add(u)},{16:16,18:18,2:2,3:3,7:7,8:8}],15:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var i=r(e(3)),o=r(e(2)),a=r(e(7)),l=e(8),s=e(18),d=e(19),u=e(17),c={isIframeStarted:!1,isIframeLoaded:!1,iframeQueue:[],enqueueIframe:function(e){c.isLoaded="undefined"!=typeof YT&&YT.loaded,c.isLoaded?c.createIframe(e):(c.loadIframeApi(),c.iframeQueue.push(e))},loadIframeApi:function(){c.isIframeStarted||((0,u.loadScript)("https://www.youtube.com/player_api"),c.isIframeStarted=!0)},iFrameReady:function(){for(c.isLoaded=!0,c.isIframeLoaded=!0;c.iframeQueue.length>0;){var e=c.iframeQueue.pop();c.createIframe(e)}},createIframe:function(e){return new YT.Player(e.containerId,e)},getYouTubeId:function(e){var t="";return e.indexOf("?")>0?""===(t=c.getYouTubeIdFromParam(e))&&(t=c.getYouTubeIdFromUrl(e)):t=c.getYouTubeIdFromUrl(e),(t=t.substring(t.lastIndexOf("/")+1).split("?"))[0]},getYouTubeIdFromParam:function(e){if(void 0===e||null===e||!e.trim().length)return null;for(var t=e.split("?")[1].split("&"),n="",r=0,i=t.length;r<i;r++){var o=t[r].split("=");if("v"===o[0]){n=o[1];break}}return n},getYouTubeIdFromUrl:function(e){return void 0!==e&&null!==e&&e.trim().length?(e=e.split("?")[0]).substring(e.lastIndexOf("/")+1):null},getYouTubeNoCookieUrl:function(e){if(void 0===e||null===e||!e.trim().length||-1===e.indexOf("//www.youtube"))return e;var t=e.split("/");return t[2]=t[2].replace(".com","-nocookie.com"),t.join("/")}},f={name:"youtube_iframe",options:{prefix:"youtube_iframe",youtube:{autoplay:0,controls:0,disablekb:1,end:0,loop:0,modestbranding:0,playsinline:0,rel:0,showinfo:0,start:0,iv_load_policy:3,nocookie:!1,imageQuality:null}},canPlayType:function(e){return~["video/youtube","video/x-youtube"].indexOf(e.toLowerCase())},create:function(e,t,n){var r={},l=[],d=null,u=!0,f=!1,m=null,p=1;r.options=t,r.id=e.id+"_"+t.prefix,r.mediaElement=e;for(var h=a.default.html5media.properties,v=0,g=h.length;v<g;v++)!function(t){var n=""+t.substring(0,1).toUpperCase()+t.substring(1);r["get"+n]=function(){if(null!==d){switch(t){case"currentTime":return d.getCurrentTime();case"duration":return d.getDuration();case"volume":return p=d.getVolume()/100;case"paused":return u;case"ended":return f;case"muted":return d.isMuted();case"buffered":var e=d.getVideoLoadedFraction(),n=d.getDuration();return{start:function(){return 0},end:function(){return e*n},length:1};case"src":return d.getVideoUrl();case"readyState":return 4}return null}return null},r["set"+n]=function(n){if(null!==d)switch(t){case"src":var i="string"==typeof n?n:n[0].src,o=c.getYouTubeId(i);e.originalNode.autoplay?d.loadVideoById(o):d.cueVideoById(o);break;case"currentTime":d.seekTo(n);break;case"muted":n?d.mute():d.unMute(),setTimeout(function(){var t=(0,s.createEvent)("volumechange",r);e.dispatchEvent(t)},50);break;case"volume":p=n,d.setVolume(100*n),setTimeout(function(){var t=(0,s.createEvent)("volumechange",r);e.dispatchEvent(t)},50);break;case"readyState":var a=(0,s.createEvent)("canplay",r);e.dispatchEvent(a)}else l.push({type:"set",propName:t,value:n})}}(h[v]);for(var y=a.default.html5media.methods,E=0,b=y.length;E<b;E++)!function(e){r[e]=function(){if(null!==d)switch(e){case"play":return u=!1,d.playVideo();case"pause":return u=!0,d.pauseVideo();case"load":return null}else l.push({type:"call",methodName:e})}}(y[E]);var w=function(t){var r="";switch(t.data){case 2:r="The request contains an invalid parameter value. Verify that video ID has 11 characters and that contains no invalid characters, such as exclamation points or asterisks.";break;case 5:r="The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.";break;case 100:r="The video requested was not found. Either video has been removed or has been marked as private.";break;case 101:case 105:r="The owner of the requested video does not allow it to be played in embedded players.";break;default:r="Unknown error."}e.generateError("Code "+t.data+": "+r,n)},_=o.default.createElement("div");_.id=r.id,r.options.youtube.nocookie&&(e.originalNode.src=c.getYouTubeNoCookieUrl(n[0].src)),e.originalNode.parentNode.insertBefore(_,e.originalNode),e.originalNode.style.display="none";var S="audio"===e.originalNode.tagName.toLowerCase(),N=S?"1":e.originalNode.height,j=S?"1":e.originalNode.width,A=c.getYouTubeId(n[0].src),T={id:r.id,containerId:_.id,videoId:A,height:N,width:j,playerVars:Object.assign({controls:0,rel:0,disablekb:1,showinfo:0,modestbranding:0,html5:1,iv_load_policy:3},r.options.youtube),origin:i.default.location.host,events:{onReady:function(t){if(e.youTubeApi=d=t.target,e.youTubeState={paused:!0,ended:!1},l.length)for(var n=0,i=l.length;n<i;n++){var o=l[n];if("set"===o.type){var a=o.propName,u=""+a.substring(0,1).toUpperCase()+a.substring(1);r["set"+u](o.value)}else"call"===o.type&&r[o.methodName]()}m=d.getIframe(),e.originalNode.muted&&d.mute();for(var c=["mouseover","mouseout"],f=0,p=c.length;f<p;f++)m.addEventListener(c[f],function(t){var n=(0,s.createEvent)(t.type,r);e.dispatchEvent(n)},!1);for(var h=["rendererready","loadedmetadata","loadeddata","canplay"],v=0,g=h.length;v<g;v++){var y=(0,s.createEvent)(h[v],r);e.dispatchEvent(y)}},onStateChange:function(t){var n=[];switch(t.data){case-1:n=["loadedmetadata"],u=!0,f=!1;break;case 0:n=["ended"],u=!1,f=!r.options.youtube.loop,r.options.youtube.loop||r.stopInterval();break;case 1:n=["play","playing"],u=!1,f=!1,r.startInterval();break;case 2:n=["pause"],u=!0,f=!1,r.stopInterval();break;case 3:n=["progress"],f=!1;break;case 5:n=["loadeddata","loadedmetadata","canplay"],u=!0,f=!1}for(var i=0,o=n.length;i<o;i++){var a=(0,s.createEvent)(n[i],r);e.dispatchEvent(a)}},onError:function(e){return w(e)}}};return(S||e.originalNode.hasAttribute("playsinline"))&&(T.playerVars.playsinline=1),e.originalNode.controls&&(T.playerVars.controls=1),e.originalNode.autoplay&&(T.playerVars.autoplay=1),e.originalNode.loop&&(T.playerVars.loop=1),(T.playerVars.loop&&1===parseInt(T.playerVars.loop,10)||e.originalNode.src.indexOf("loop=")>-1)&&!T.playerVars.playlist&&-1===e.originalNode.src.indexOf("playlist=")&&(T.playerVars.playlist=c.getYouTubeId(e.originalNode.src)),c.enqueueIframe(T),r.onEvent=function(t,n,r){null!==r&&void 0!==r&&(e.youTubeState=r)},r.setSize=function(e,t){null!==d&&d.setSize(e,t)},r.hide=function(){r.stopInterval(),r.pause(),m&&(m.style.display="none")},r.show=function(){m&&(m.style.display="")},r.destroy=function(){d.destroy()},r.interval=null,r.startInterval=function(){r.interval=setInterval(function(){var t=(0,s.createEvent)("timeupdate",r);e.dispatchEvent(t)},250)},r.stopInterval=function(){r.interval&&clearInterval(r.interval)},r.getPosterUrl=function(){var n=t.youtube.imageQuality,r=["default","hqdefault","mqdefault","sddefault","maxresdefault"],i=c.getYouTubeId(e.originalNode.src);return n&&r.indexOf(n)>-1&&i?"https://img.youtube.com/vi/"+i+"/"+n+".jpg":""},r}};i.default.onYouTubePlayerAPIReady=function(){c.iFrameReady()},d.typeChecks.push(function(e){return/\/\/(www\.youtube|youtu\.?be)/i.test(e)?"video/x-youtube":null}),l.renderer.add(f)},{17:17,18:18,19:19,2:2,3:3,7:7,8:8}],16:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0}),n.cancelFullScreen=n.requestFullScreen=n.isFullScreen=n.FULLSCREEN_EVENT_NAME=n.HAS_NATIVE_FULLSCREEN_ENABLED=n.HAS_TRUE_NATIVE_FULLSCREEN=n.HAS_IOS_FULLSCREEN=n.HAS_MS_NATIVE_FULLSCREEN=n.HAS_MOZ_NATIVE_FULLSCREEN=n.HAS_WEBKIT_NATIVE_FULLSCREEN=n.HAS_NATIVE_FULLSCREEN=n.SUPPORTS_NATIVE_HLS=n.SUPPORT_PASSIVE_EVENT=n.SUPPORT_POINTER_EVENTS=n.HAS_MSE=n.IS_STOCK_ANDROID=n.IS_SAFARI=n.IS_FIREFOX=n.IS_CHROME=n.IS_EDGE=n.IS_IE=n.IS_ANDROID=n.IS_IOS=n.IS_IPOD=n.IS_IPHONE=n.IS_IPAD=n.UA=n.NAV=void 0;for(var i=r(e(3)),o=r(e(2)),a=r(e(7)),l=n.NAV=i.default.navigator,s=n.UA=l.userAgent.toLowerCase(),d=n.IS_IPAD=/ipad/i.test(s)&&!i.default.MSStream,u=n.IS_IPHONE=/iphone/i.test(s)&&!i.default.MSStream,c=n.IS_IPOD=/ipod/i.test(s)&&!i.default.MSStream,f=(n.IS_IOS=/ipad|iphone|ipod/i.test(s)&&!i.default.MSStream,n.IS_ANDROID=/android/i.test(s)),m=n.IS_IE=/(trident|microsoft)/i.test(l.appName),p=(n.IS_EDGE="msLaunchUri"in l&&!("documentMode"in o.default)),h=n.IS_CHROME=/chrome/i.test(s),v=n.IS_FIREFOX=/firefox/i.test(s),g=n.IS_SAFARI=/safari/i.test(s)&&!h,y=n.IS_STOCK_ANDROID=/^mozilla\/\d+\.\d+\s\(linux;\su;/i.test(s),E=(n.HAS_MSE="MediaSource"in i.default),b=(n.SUPPORT_POINTER_EVENTS=function(){var e=o.default.createElement("x"),t=o.default.documentElement,n=i.default.getComputedStyle;if(!("pointerEvents"in e.style))return!1;e.style.pointerEvents="auto",e.style.pointerEvents="x",t.appendChild(e);var r=n&&"auto"===(n(e,"")||{}).pointerEvents;return e.remove(),!!r}()),w=n.SUPPORT_PASSIVE_EVENT=function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});i.default.addEventListener("test",null,t)}catch(e){}return e}(),_=["source","track","audio","video"],S=void 0,N=0,j=_.length;N<j;N++)S=o.default.createElement(_[N]);var A=n.SUPPORTS_NATIVE_HLS=g||f&&(h||y)||m&&/edge/i.test(s),T=void 0!==S.webkitEnterFullscreen,F=void 0!==S.requestFullscreen;T&&/mac os x 10_5/i.test(s)&&(F=!1,T=!1);var P=void 0!==S.webkitRequestFullScreen,x=void 0!==S.mozRequestFullScreen,L=void 0!==S.msRequestFullscreen,O=P||x||L,C=O,I="",k=void 0,U=void 0,M=void 0;x?C=o.default.mozFullScreenEnabled:L&&(C=o.default.msFullscreenEnabled),h&&(T=!1),O&&(P?I="webkitfullscreenchange":x?I="mozfullscreenchange":L&&(I="MSFullscreenChange"),n.isFullScreen=k=function(){return x?o.default.mozFullScreen:P?o.default.webkitIsFullScreen:L?null!==o.default.msFullscreenElement:void 0},n.requestFullScreen=U=function(e){P?e.webkitRequestFullScreen():x?e.mozRequestFullScreen():L&&e.msRequestFullscreen()},n.cancelFullScreen=M=function(){P?o.default.webkitCancelFullScreen():x?o.default.mozCancelFullScreen():L&&o.default.msExitFullscreen()});var R=n.HAS_NATIVE_FULLSCREEN=F,V=n.HAS_WEBKIT_NATIVE_FULLSCREEN=P,D=n.HAS_MOZ_NATIVE_FULLSCREEN=x,H=n.HAS_MS_NATIVE_FULLSCREEN=L,q=n.HAS_IOS_FULLSCREEN=T,z=n.HAS_TRUE_NATIVE_FULLSCREEN=O,B=n.HAS_NATIVE_FULLSCREEN_ENABLED=C,Y=n.FULLSCREEN_EVENT_NAME=I;n.isFullScreen=k,n.requestFullScreen=U,n.cancelFullScreen=M,a.default.Features=a.default.Features||{},a.default.Features.isiPad=d,a.default.Features.isiPod=c,a.default.Features.isiPhone=u,a.default.Features.isiOS=a.default.Features.isiPhone||a.default.Features.isiPad,a.default.Features.isAndroid=f,a.default.Features.isIE=m,a.default.Features.isEdge=p,a.default.Features.isChrome=h,a.default.Features.isFirefox=v,a.default.Features.isSafari=g,a.default.Features.isStockAndroid=y,a.default.Features.hasMSE=E,a.default.Features.supportsNativeHLS=A,a.default.Features.supportsPointerEvents=b,a.default.Features.supportsPassiveEvent=w,a.default.Features.hasiOSFullScreen=q,a.default.Features.hasNativeFullscreen=R,a.default.Features.hasWebkitNativeFullScreen=V,a.default.Features.hasMozNativeFullScreen=D,a.default.Features.hasMsNativeFullScreen=H,a.default.Features.hasTrueNativeFullScreen=z,a.default.Features.nativeFullScreenEnabled=B,a.default.Features.fullScreenEventName=Y,a.default.Features.isFullScreen=k,a.default.Features.requestFullScreen=U,a.default.Features.cancelFullScreen=M},{2:2,3:3,7:7}],17:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function i(e){return new Promise(function(t,n){var r=m.default.createElement("script");r.src=e,r.async=!0,r.onload=function(){r.remove(),t()},r.onerror=function(){r.remove(),n()},m.default.head.appendChild(r)})}function o(e){var t=e.getBoundingClientRect(),n=f.default.pageXOffset||m.default.documentElement.scrollLeft,r=f.default.pageYOffset||m.default.documentElement.scrollTop;return{top:t.top+r,left:t.left+n}}function a(e,t){y(e,t)?b(e,t):E(e,t)}function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=1);var r=null;f.default.requestAnimationFrame(function i(o){var a=o-(r=r||o),l=parseFloat(1-a/t,2);e.style.opacity=l<0?0:l,a>t?n&&"function"==typeof n&&n():f.default.requestAnimationFrame(i)})}function s(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=0);var r=null;f.default.requestAnimationFrame(function i(o){var a=o-(r=r||o),l=parseFloat(a/t,2);e.style.opacity=l>1?1:l,a>t?n&&"function"==typeof n&&n():f.default.requestAnimationFrame(i)})}function d(e,t){var n=[];e=e.parentNode.firstChild;do{t&&!t(e)||n.push(e)}while(e=e.nextSibling);return n}function u(e){return void 0!==e.getClientRects&&"function"===e.getClientRects?!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length):!(!e.offsetWidth&&!e.offsetHeight)}function c(e,t,n,r){var i=f.default.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),o="application/x-www-form-urlencoded; charset=UTF-8",a=!1,l="*/".concat("*");switch(t){case"text":o="text/plain";break;case"json":o="application/json, text/javascript";break;case"html":o="text/html";break;case"xml":o="application/xml, text/xml"}"application/x-www-form-urlencoded"!==o&&(l=o+", */*; q=0.01"),i&&(i.open("GET",e,!0),i.setRequestHeader("Accept",l),i.onreadystatechange=function(){if(!a&&4===i.readyState)if(200===i.status){a=!0;var e=void 0;switch(t){case"json":e=JSON.parse(i.responseText);break;case"xml":e=i.responseXML;break;default:e=i.responseText}n(e)}else"function"==typeof r&&r(i.status)},i.send())}Object.defineProperty(n,"__esModule",{value:!0}),n.removeClass=n.addClass=n.hasClass=void 0,n.loadScript=i,n.offset=o,n.toggleClass=a,n.fadeOut=l,n.fadeIn=s,n.siblings=d,n.visible=u,n.ajax=c;var f=r(e(3)),m=r(e(2)),p=r(e(7)),h=void 0,v=void 0,g=void 0;"classList"in m.default.documentElement?(h=function(e,t){return void 0!==e.classList&&e.classList.contains(t)},v=function(e,t){return e.classList.add(t)},g=function(e,t){return e.classList.remove(t)}):(h=function(e,t){return new RegExp("\\b"+t+"\\b").test(e.className)},v=function(e,t){y(e,t)||(e.className+=" "+t)},g=function(e,t){e.className=e.className.replace(new RegExp("\\b"+t+"\\b","g"),"")});var y=n.hasClass=h,E=n.addClass=v,b=n.removeClass=g;p.default.Utils=p.default.Utils||{},p.default.Utils.offset=o,p.default.Utils.hasClass=y,p.default.Utils.addClass=E,p.default.Utils.removeClass=b,p.default.Utils.toggleClass=a,p.default.Utils.fadeIn=s,p.default.Utils.fadeOut=l,p.default.Utils.siblings=d,p.default.Utils.visible=u,p.default.Utils.ajax=c,p.default.Utils.loadScript=i},{2:2,3:3,7:7}],18:[function(e,t,n){"use strict";function r(e){if("string"!=typeof e)throw new Error("Argument passed must be a string");var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};return e.replace(/[&<>"]/g,function(e){return t[e]})}function i(e,t){var n=this,r=arguments,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("function"!=typeof e)throw new Error("First argument must be a function");if("number"!=typeof t)throw new Error("Second argument must be a numeric value");var o=void 0;return function(){var a=n,l=r,s=i&&!o;clearTimeout(o),o=setTimeout(function(){o=null,i||e.apply(a,l)},t),s&&e.apply(a,l)}}function o(e){return Object.getOwnPropertyNames(e).length<=0}function a(e,t){var n=/^((after|before)print|(before)?unload|hashchange|message|o(ff|n)line|page(hide|show)|popstate|resize|storage)\b/,r={d:[],w:[]};return(e||"").split(" ").forEach(function(e){var i=e+(t?"."+t:"");i.startsWith(".")?(r.d.push(i),r.w.push(i)):r[n.test(e)?"w":"d"].push(i)}),r.d=r.d.join(" "),r.w=r.w.join(" "),r}function l(e,t){if("string"!=typeof e)throw new Error("Event name must be a string");var n=e.match(/([a-z]+\.([a-z]+))/i),r={target:t};return null!==n&&(e=n[1],r.namespace=n[2]),new window.CustomEvent(e,{detail:r})}function s(e,t){return!!(e&&t&&2&e.compareDocumentPosition(t))}function d(e){return"string"==typeof e}Object.defineProperty(n,"__esModule",{value:!0}),n.escapeHTML=r,n.debounce=i,n.isObjectEmpty=o,n.splitEvents=a,n.createEvent=l,n.isNodeAfter=s,n.isString=d;var u=function(e){return e&&e.__esModule?e:{default:e}}(e(7));u.default.Utils=u.default.Utils||{},u.default.Utils.escapeHTML=r,u.default.Utils.debounce=i,u.default.Utils.isObjectEmpty=o,u.default.Utils.splitEvents=a,u.default.Utils.createEvent=l,u.default.Utils.isNodeAfter=s,u.default.Utils.isString=d},{7:7}],19:[function(e,t,n){"use strict";function r(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=document.createElement("div");return t.innerHTML='<a href="'+(0,u.escapeHTML)(e)+'">x</a>',t.firstChild.href}function i(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e&&!t?a(e):t}function o(e){if("string"!=typeof e)throw new Error("`type` argument must be a string");return e&&e.indexOf(";")>-1?e.substr(0,e.indexOf(";")):e}function a(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");for(var t=0,n=c.length;t<n;t++){var r=c[t](e);if(r)return r}var i=s(l(e)),o="video/mp4";return i&&(~["mp4","m4v","ogg","ogv","webm","flv","mpeg","mov"].indexOf(i)?o="video/"+i:~["mp3","oga","wav","mid","midi"].indexOf(i)&&(o="audio/"+i)),o}function l(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=e.split("?")[0].split("\\").pop().split("/").pop();return~t.indexOf(".")?t.substring(t.lastIndexOf(".")+1):""}function s(e){if("string"!=typeof e)throw new Error("`extension` argument must be a string");switch(e){case"mp4":case"m4v":return"mp4";case"webm":case"webma":case"webmv":return"webm";case"ogg":case"oga":case"ogv":return"ogg";default:return e}}Object.defineProperty(n,"__esModule",{value:!0}),n.typeChecks=void 0,n.absolutizeUrl=r,n.formatType=i,n.getMimeFromType=o,n.getTypeFromFile=a,n.getExtension=l,n.normalizeExtension=s;var d=function(e){return e&&e.__esModule?e:{default:e}}(e(7)),u=e(18),c=n.typeChecks=[];d.default.Utils=d.default.Utils||{},d.default.Utils.typeChecks=c,d.default.Utils.absolutizeUrl=r,d.default.Utils.formatType=i,d.default.Utils.getMimeFromType=o,d.default.Utils.getTypeFromFile=a,d.default.Utils.getExtension=l,d.default.Utils.normalizeExtension=s},{18:18,7:7}],20:[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var i=r(e(2)),o=r(e(4));if([Element.prototype,CharacterData.prototype,DocumentType.prototype].forEach(function(e){e.hasOwnProperty("remove")||Object.defineProperty(e,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})}),function(){function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=i.default.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}if("function"==typeof window.CustomEvent)return!1;e.prototype=window.Event.prototype,window.CustomEvent=e}(),"function"!=typeof Object.assign&&(Object.assign=function(e){if(null===e||void 0===e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1,r=arguments.length;n<r;n++){var i=arguments[n];if(null!==i)for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t}),String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return t=t||0,this.substr(t,e.length)===e}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length-1;--n>=0&&t.item(n)!==this;);return n>-1}),window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(e){var t=(this.document||this.ownerDocument).querySelectorAll(e),n=void 0,r=this;do{for(n=t.length;--n>=0&&t.item(n)!==r;);}while(n<0&&(r=r.parentElement));return r}),function(){for(var e=0,t=["ms","moz","webkit","o"],n=0;n<t.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[t[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[t[n]+"CancelAnimationFrame"]||window[t[n]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(t){var n=(new Date).getTime(),r=Math.max(0,16-(n-e)),i=window.setTimeout(function(){t(n+r)},r);return e=n+r,i}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}(),/firefox/i.test(navigator.userAgent)){var a=window.getComputedStyle;window.getComputedStyle=function(e,t){var n=a(e,t);return null===n?{getPropertyValue:function(){}}:n}}window.Promise||(window.Promise=o.default),function(e){e&&e.prototype&&null===e.prototype.children&&Object.defineProperty(e.prototype,"children",{get:function(){for(var e=0,t=void 0,n=this.childNodes,r=[];t=n[e++];)1===t.nodeType&&r.push(t);return r}})}(window.Node||window.Element)},{2:2,4:4}]},{},[20,6,5,9,14,11,10,12,13,15]);