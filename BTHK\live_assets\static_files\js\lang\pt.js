'use strict';/*!
 * This is a `i18n` language object.
 *
 * Portuguese
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.pt === undefined) {
		exports.pt = {
			'mejs.plural-form': 1,			
			'mejs.download-file': '<PERSON><PERSON><PERSON><PERSON> o ficheiro',			
			'mejs.install-flash': 'Você está usando um navegador que não possui o player Flash ativado ou instalado. Por favor, ligue o plugin do Flash Player ou baixe a versão mais recente de https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Ecrã completo',			
			'mejs.play': 'Reprodução',
			'mejs.pause': 'Pausa',			
			'mejs.time-slider': 'Deslizador do tempo',
			'mejs.time-help-text': 'Use as teclas das setas para a esquerda/direita para avançar um segundo, e as setas para cima/baixo para avançar dez segundos.',
			'mejs.live-broadcast' : 'Transmissão ao vivo',
			
			
			'mejs.volume-help-text': 'Use as teclas das setas para cima/baixo para aumentar ou diminuir o volume.',
			'mejs.unmute': 'Voltar ao som',
			'mejs.mute': 'Silêncio',
			'mejs.volume-slider': 'Deslizador do volume',			
			'mejs.video-player': 'Leitor de vídeo',
			'mejs.audio-player': 'Leitor de áudio',			
			'mejs.captions-subtitles': 'Legendas',
			'mejs.captions-chapters': 'Capítulos',
			'mejs.none': 'Nenhum',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albanês',
			'mejs.arabic': 'Árabe',
			'mejs.belarusian': 'Bielorrusso',
			'mejs.bulgarian': 'Búlgaro',
			'mejs.catalan': 'Catalão',
			'mejs.chinese': 'Chinês',
			'mejs.chinese-simplified': 'Chinese (Simplified)',
			'mejs.chinese-traditional': 'Chinese (Traditional)',
			'mejs.croatian': 'Croata',
			'mejs.czech': 'Checo',
			'mejs.danish': 'Danish',
			'mejs.dutch': 'Dutch',
			'mejs.english': 'Inglês',
			'mejs.estonian': 'Estoniano',
			'mejs.filipino': 'Filipino',
			'mejs.finnish': 'Finlandês',
			'mejs.french': 'French',
			'mejs.galician': 'Galego',
			'mejs.german': 'Alemão',
			'mejs.greek': 'Grego',
			'mejs.haitian-creole': 'Crioulo Haitiano',
			'mejs.hebrew': 'Hebraico',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Húngaro',
			'mejs.icelandic': 'Islandês',
			'mejs.indonesian': 'Indonésio',
			'mejs.irish': 'Irish',
			'mejs.italian': 'Italiano',
			'mejs.japanese': 'Japonês',
			'mejs.korean': 'Coreano',
			'mejs.latvian': 'Letão',
			'mejs.lithuanian': 'Lithuanian',
			'mejs.macedonian': 'Macedônio',
			'mejs.malay': 'Malaio',
			'mejs.maltese': 'Maltês',
			'mejs.norwegian': 'Norwegian',
			'mejs.persian': 'Persa',
			'mejs.polish': 'Polish',
			'mejs.portuguese': 'Português',
			'mejs.romanian': 'Romanian',
			'mejs.russian': 'Russian',
			'mejs.serbian': 'Sérvio',
			'mejs.slovak': 'Slovak',
			'mejs.slovenian': 'Slovenian',
			'mejs.spanish': 'Espanhol',
			'mejs.swahili': 'Swahili',
			'mejs.swedish': 'sueco',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thai',
			'mejs.turkish': 'Turco',
			'mejs.ukrainian': 'Ucraniano',
			'mejs.vietnamese': 'Vietnamita',
			'mejs.welsh': 'Welsh',
			'mejs.yiddish': 'Iídiche'
		};
	}
})(mejs.i18n);