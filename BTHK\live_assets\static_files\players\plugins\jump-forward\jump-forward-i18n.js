'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.time-jump-forward'] = ['Salteu endavant 1 segon', 'Salta endavant %1 segons'];
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.time-jump-forward'] = ['Přeskočte o 1 sekundu dopředu', 'Přeskočte %1 vteřiny dopředu', 'Jump forward %1 seconds'];
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.time-jump-forward'] = ['1 Sekunde vorspulen', 'Přeskočte %1 vteřiny dopředu', 'Skok %1 vteřin'];
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.time-jump-forward'] = ['Adelantar 1 segundo', 'Adelantar %1 segundos'];
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.time-jump-forward'] = 'پرش به جلو 1% ثانیه';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.time-jump-forward'] = ['Avancer de %1 seconde', 'Avancer de %1 secondes'];
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.time-jump-forward'] = ['Skoči naprijed 1 sekundu', 'Skoči naprijed %1 sekunde', 'Skoči naprijed %1 sekundi'];
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.time-jump-forward'] = ['Ugrás előre 1 másodpercig', 'Ugrás előre %1 másodpercig'];
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.time-jump-forward'] = ['Salta in avanti per 1 secondo', 'Salta in avanti %1 secondi'];
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.time-jump-forward'] = '%1秒前にジャンプ';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.time-jump-forward'] = '%1 초 앞으로 뛰어 오르십시오';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.time-jump-forward'] = '%1 seconden vooruit springen';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.time-jump-forward'] = ['Przewiń do przodu o sekundę', 'Przewiń do przodu o %1 sekundy', 'Przewiń do przodu o %1 sekund'];
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.time-jump-forward'] = ['Avance 1 segundo', 'Avance %1 segundos'];
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.time-jump-forward'] = ['Salt înainte de 1 secundă', 'Salt înainte de %1 secunde'];
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.time-jump-forward'] = ['Перейти вперед на %1 секунду', 'Перейти вперед на %1 секунды', 'Перейти вперед на %1 секунд'];
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.time-jump-forward'] = ['Skočiť dopredu o 1 sekundu', 'Skok vpred o %1 sekundy', 'Skok dopredu %1 sekúnd'];
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.time-jump-forward'] = ['Hoppa fram 1 sekund', 'Hoppa fram %1 sekunder'];
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.time-jump-forward'] = ['Перейти вперед на %1 секунду', 'Перейти вперед на %1 секунди', 'Перейти вперед на %1 секунд'];
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.time-jump-forward'] = '向前跳%1秒';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.time-jump-forward'] = '向前跳%1秒';
}