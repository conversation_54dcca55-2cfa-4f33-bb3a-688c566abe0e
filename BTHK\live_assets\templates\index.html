<!DOCTYPE html>
<html>
  {% include "head.html" %}

  <body>
    <style>
      body {
        direction: rtl;
      }
    </style>

    <style>
      .headerText {
        text-shadow: 0 0 10px rgba(255, 255, 255, 1), 0 0 20px rgba(255, 255, 255, 1), 0 0 30px rgba(255, 255, 255, 1),
          0 0 40px #ff00de, 0 0 70px #ff00de, 0 0 80px #ff00de, 0 0 100px #ff00de;
        margin: 60px;
      }
    </style>
    <div>
      <center>
        <h1 class="headerText">قنوات البث المباشر حصرياً على {{main.main_name}}</h1>
      </center>
      <style>
        .tvcard {
          background: -moz-linear-gradient(
            top,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.65) 55%,
            rgba(0, 0, 0, 0.69) 60%,
            rgba(0, 0, 0, 1) 95%,
            rgba(0, 0, 0, 1) 100%
          );
          background: -webkit-linear-gradient(
            top,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.65) 55%,
            rgba(0, 0, 0, 0.69) 60%,
            rgba(0, 0, 0, 1) 95%,
            rgba(0, 0, 0, 1) 100%
          );
          background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.65) 55%,
            rgba(0, 0, 0, 0.69) 60%,
            rgba(0, 0, 0, 1) 95%,
            rgba(0, 0, 0, 1) 100%
          );
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#000000',GradientType=0 );

          height: 200px;
          width: 235px;

          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0px 0px 78px 2px #f1f1f152, 0px 0px 64px 0px #0000004f inset;
          position: relative;
          margin: auto;
          margin-top: 20px;
          cursor: pointer;
        }
        .tvimg {
          width: 100%;
          height: 100%;
          overflow: hidden;
          position: relative;
        }
        .tvimg > img {
          width: 100%;
          height: 100%;
          position: absolute;
          z-index: -1;
          left: 0px;
          top: 0px;
        }
        .lightplace {
          width: 15px;
          height: 15px;

          z-index: 999;
          position: absolute;
          top: 9px;
          left: 24px;
          border-radius: 20px;
          border: 1px solid #868383;
        }
        .live {
          box-shadow: 0px 0px 10px 1px #54ff00;
          background: #54ff00;
        }
        .nlive {
          box-shadow: 0px 0px 10px 1px #c32828;
          background: #ff0000;
        }
        .tvtext {
          color: #fff;

          z-index: 999;
          position: absolute;
          bottom: 17px;
          right: 12px;
          font-size: 22px;
        }
        .bgimg {
          background: url(/imgs/arabic_chanls.jpg);
          height: 100%;
          width: 100%;
          z-index: -1;
          position: absolute;
          top: 0px;
          opacity: 0.07;
        }
      </style>
      <div class="bgimg"></div>
      {% if channels.length==0 %}
      <div style="font-size: 70px; text-align: center">لايوجد اي قنوات حالياً</div>
      {% endif %}
      <div class="row" style="margin: 0px">
        {% for c in channels %}

        <div class="col-md-4 col-xs-12">
          <a href="/player/100f/470/MediaElementJS/{{c.id}}">
            <div class="tvcard">
              <div class="lightplace {%if c.islive %} live {% else %} nlive{% endif %}"></div>
              <div class="tvimg">
                <img src="/getChannelImg/{{c.id}}" />
              </div>
              <div class="tvtext">{{c.cha_name}}</div>
            </div>
          </a>
        </div>
        {% endfor %}
      </div>
    </div>
  </body>
  {% include "footer.html" %}
</html>
