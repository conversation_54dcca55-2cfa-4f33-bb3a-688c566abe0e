/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,n,r){function i(o,s){if(!n[o]){if(!t[o]){var u="function"==typeof require&&require;if(!s&&u)return u(o,!0);if(a)return a(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return i(n||e)},l,l.exports,e,t,n,r)}return n[o].exports}for(var a="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){"use strict";var r={promise:null,load:function(e){"undefined"!=typeof Vimeo?r._createPlayer(e):(r.promise=r.promise||mejs.Utils.loadScript("https://player.vimeo.com/api/player.js"),r.promise.then(function(){r._createPlayer(e)}))},_createPlayer:function(e){var t=new Vimeo.Player(e.iframe);window["__ready__"+e.id](t)},getVimeoId:function(e){return void 0===e||null===e?null:(e=e.split("?")[0],parseInt(e.substring(e.lastIndexOf("/")+1),10))}},i={name:"vimeo_iframe",options:{prefix:"vimeo_iframe"},canPlayType:function(e){return~["video/vimeo","video/x-vimeo"].indexOf(e.toLowerCase())},create:function(e,t,n){var i=[],a={},o=!0,s=1,u=s,c=0,l=0,d=!1,f=0,p=null,m="";a.options=t,a.id=e.id+"_"+t.prefix,a.mediaElement=e;for(var v=function(t){e.generateError("Code "+t.name+": "+t.message,n)},h=mejs.html5media.properties,y=0,g=h.length;y<g;y++)!function(t){var n=""+t.substring(0,1).toUpperCase()+t.substring(1);a["get"+n]=function(){if(null!==p){switch(t){case"currentTime":return c;case"duration":return f;case"volume":return s;case"muted":return 0===s;case"paused":return o;case"ended":return d;case"src":return p.getVideoUrl().then(function(e){m=e}).catch(function(e){return v(e)}),m;case"buffered":return{start:function(){return 0},end:function(){return l*f},length:1};case"readyState":return 4}return null}return null},a["set"+n]=function(n){if(null!==p)switch(t){case"src":var o="string"==typeof n?n:n[0].src,l=r.getVimeoId(o);p.loadVideo(l).then(function(){e.originalNode.autoplay&&p.play()}).catch(function(e){return v(e)});break;case"currentTime":p.setCurrentTime(n).then(function(){c=n,setTimeout(function(){var t=mejs.Utils.createEvent("timeupdate",a);e.dispatchEvent(t)},50)}).catch(function(e){return v(e)});break;case"volume":p.setVolume(n).then(function(){u=s=n,setTimeout(function(){var t=mejs.Utils.createEvent("volumechange",a);e.dispatchEvent(t)},50)}).catch(function(e){return v(e)});break;case"loop":p.setLoop(n).catch(function(e){return v(e)});break;case"muted":n?p.setVolume(0).then(function(){s=0,setTimeout(function(){var t=mejs.Utils.createEvent("volumechange",a);e.dispatchEvent(t)},50)}).catch(function(e){return v(e)}):p.setVolume(u).then(function(){s=u,setTimeout(function(){var t=mejs.Utils.createEvent("volumechange",a);e.dispatchEvent(t)},50)}).catch(function(e){return v(e)});break;case"readyState":var d=mejs.Utils.createEvent("canplay",a);e.dispatchEvent(d)}else i.push({type:"set",propName:t,value:n})}}(h[y]);for(var E=mejs.html5media.methods,U=0,b=E.length;U<b;U++)!function(e){a[e]=function(){if(null!==p)switch(e){case"play":return o=!1,p.play();case"pause":return o=!0,p.pause();case"load":return null}else i.push({type:"call",methodName:e})}}(E[U]);window["__ready__"+a.id]=function(t){if(e.vimeoPlayer=p=t,i.length)for(var n=0,r=i.length;n<r;n++){var u=i[n];if("set"===u.type){var m=u.propName,h=""+m.substring(0,1).toUpperCase()+m.substring(1);a["set"+h](u.value)}else"call"===u.type&&a[u.methodName]()}e.originalNode.muted&&(p.setVolume(0),s=0);for(var y=document.getElementById(a.id),g=void 0,E=0,U=(g=["mouseover","mouseout"]).length;E<U;E++)y.addEventListener(g[E],function(t){var n=mejs.Utils.createEvent(t.type,a);e.dispatchEvent(n)},!1);p.on("loaded",function(){p.getDuration().then(function(t){if((f=t)>0&&(l=f*t,e.originalNode.autoplay)){o=!1,d=!1;var n=mejs.Utils.createEvent("play",a);e.dispatchEvent(n)}}).catch(function(e){v(e)})}),p.on("progress",function(){p.getDuration().then(function(t){if((f=t)>0&&(l=f*t,e.originalNode.autoplay)){var n=mejs.Utils.createEvent("play",a);e.dispatchEvent(n);var r=mejs.Utils.createEvent("playing",a);e.dispatchEvent(r)}var i=mejs.Utils.createEvent("progress",a);e.dispatchEvent(i)}).catch(function(e){return v(e)})}),p.on("timeupdate",function(){p.getCurrentTime().then(function(t){c=t;var n=mejs.Utils.createEvent("timeupdate",a);e.dispatchEvent(n)}).catch(function(e){return v(e)})}),p.on("play",function(){o=!1,d=!1;var t=mejs.Utils.createEvent("play",a);e.dispatchEvent(t);var n=mejs.Utils.createEvent("playing",a);e.dispatchEvent(n)}),p.on("pause",function(){o=!0,d=!1;var t=mejs.Utils.createEvent("pause",a);e.dispatchEvent(t)}),p.on("ended",function(){o=!1,d=!0;var t=mejs.Utils.createEvent("ended",a);e.dispatchEvent(t)});for(var b=0,j=(g=["rendererready","loadedmetadata","loadeddata","canplay"]).length;b<j;b++){var w=mejs.Utils.createEvent(g[b],a);e.dispatchEvent(w)}};var j=e.originalNode.height,w=e.originalNode.width,N=document.createElement("iframe"),_="https://player.vimeo.com/video/"+r.getVimeoId(n[0].src),x=~n[0].src.indexOf("?")?"?"+n[0].src.slice(n[0].src.indexOf("?")+1):"";return x&&e.originalNode.autoplay&&-1===x.indexOf("autoplay")&&(x+="&autoplay=1"),x&&e.originalNode.loop&&-1===x.indexOf("loop")&&(x+="&loop=1"),N.setAttribute("id",a.id),N.setAttribute("width",w),N.setAttribute("height",j),N.setAttribute("frameBorder","0"),N.setAttribute("src",""+_+x),N.setAttribute("webkitallowfullscreen","true"),N.setAttribute("mozallowfullscreen","true"),N.setAttribute("allowfullscreen","true"),e.originalNode.parentNode.insertBefore(N,e.originalNode),e.originalNode.style.display="none",r.load({iframe:N,id:a.id}),a.hide=function(){a.pause(),p&&(N.style.display="none")},a.setSize=function(e,t){N.setAttribute("width",e),N.setAttribute("height",t)},a.show=function(){p&&(N.style.display="")},a.destroy=function(){},a}};mejs.Utils.typeChecks.push(function(e){return/(\/\/player\.vimeo|vimeo\.com)/i.test(e)?"video/x-vimeo":null}),mejs.Renderers.add(i)},{}]},{},[1]);