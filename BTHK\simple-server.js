const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3333;

// Simple in-memory storage
let channels = [
    { id: 1, name: 'قناة تجريبية', url: 'https://example.com/stream.m3u8', type: 'live', status: 'active' }
];
let files = [];
let settings = {
    system_name: 'BTHK Live System',
    server_ip: 'localhost',
    server_port: '3333'
};

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

function serveFile(res, filePath) {
    fs.readFile(filePath, (err, content) => {
        if (err) {
            if (err.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end('<h1>404 - الصفحة غير موجودة</h1>');
            } else {
                res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end('<h1>500 - خطأ في الخادم</h1>');
            }
        } else {
            res.writeHead(200, { 'Content-Type': getContentType(filePath) });
            res.end(content);
        }
    });
}

function handleAPI(req, res, pathname) {
    const method = req.method;
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    if (method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    if (pathname === '/api/channels') {
        if (method === 'GET') {
            res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({ channels: channels }));
        } else if (method === 'POST') {
            let body = '';
            req.on('data', chunk => body += chunk.toString());
            req.on('end', () => {
                try {
                    const data = JSON.parse(body);
                    const newChannel = {
                        id: channels.length + 1,
                        name: data.name,
                        url: data.url,
                        type: data.type || 'live',
                        status: 'active'
                    };
                    channels.push(newChannel);
                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({ id: newChannel.id, message: 'Channel added successfully' }));
                } catch (error) {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({ error: 'Invalid JSON' }));
                }
            });
        }
    } else if (pathname.startsWith('/api/channels/') && method === 'DELETE') {
        const id = parseInt(pathname.split('/')[3]);
        channels = channels.filter(c => c.id !== id);
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({ message: 'Channel deleted successfully' }));
    } else if (pathname === '/api/files') {
        res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({ files: files }));
    } else if (pathname === '/api/settings') {
        if (method === 'GET') {
            res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
            res.end(JSON.stringify({ settings: settings }));
        } else if (method === 'POST') {
            let body = '';
            req.on('data', chunk => body += chunk.toString());
            req.on('end', () => {
                try {
                    const data = JSON.parse(body);
                    settings = { ...settings, ...data };
                    res.writeHead(200, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({ message: 'Settings updated successfully' }));
                } catch (error) {
                    res.writeHead(400, { 'Content-Type': 'application/json; charset=utf-8' });
                    res.end(JSON.stringify({ error: 'Invalid JSON' }));
                }
            });
        }
    } else {
        res.writeHead(404, { 'Content-Type': 'application/json; charset=utf-8' });
        res.end(JSON.stringify({ error: 'API endpoint not found' }));
    }
}

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    console.log(`${req.method} ${pathname}`);
    
    if (pathname.startsWith('/api/')) {
        handleAPI(req, res, pathname);
    } else if (pathname === '/') {
        serveFile(res, path.join(__dirname, 'templates', 'viewer.html'));
    } else if (pathname === '/admin') {
        serveFile(res, path.join(__dirname, 'templates', 'admin.html'));
    } else if (pathname === '/socket.io/socket.io.js') {
        // Simple socket.io mock
        res.writeHead(200, { 'Content-Type': 'text/javascript' });
        res.end(`
            window.io = function() {
                return {
                    on: function(event, callback) {
                        if (event === 'connect') setTimeout(callback, 100);
                        if (event === 'viewerCount') setTimeout(() => callback(Math.floor(Math.random() * 50)), 1000);
                    },
                    emit: function(event, data) {
                        console.log('Socket emit:', event, data);
                    }
                };
            };
        `);
    } else {
        // Serve static files
        let filePath = path.join(__dirname, pathname.substring(1));
        
        // Security check
        if (!filePath.startsWith(__dirname)) {
            res.writeHead(403, { 'Content-Type': 'text/html; charset=utf-8' });
            res.end('<h1>403 - ممنوع</h1>');
            return;
        }
        
        serveFile(res, filePath);
    }
});

server.listen(PORT, () => {
    console.log('🚀 BTHK Live System (Simple Version) running!');
    console.log(`📺 Viewer page: http://localhost:${PORT}`);
    console.log(`⚙️  Admin panel: http://localhost:${PORT}/admin`);
    console.log('');
    console.log('Note: This is a simplified version without full Socket.IO support');
    console.log('For full features, install dependencies using install.bat first');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('Server closed.');
        process.exit(0);
    });
});
