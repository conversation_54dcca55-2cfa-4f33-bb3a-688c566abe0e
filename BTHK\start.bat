@echo off
echo Starting BTHK Live System...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js is not installed or not in PATH
    echo Please install Node.js first
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo npm is not installed or not in PATH
    echo Please install Node.js with npm
    pause
    exit /b 1
)

echo Checking dependencies...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo Starting BTHK Live System on Node.js...
echo Server will be available at: http://localhost:3333
echo Admin panel: http://localhost:3333/admin
echo.
echo Features available:
echo - Channel management
echo - File upload and streaming
echo - Live video capture from USB/HDMI (requires FFmpeg)
echo - Real-time viewer statistics
echo.
echo Note: For video capture features, make sure FFmpeg is installed.
echo See FFMPEG_SETUP.md for installation instructions.
echo.

REM Start the server
node server.js

pause
