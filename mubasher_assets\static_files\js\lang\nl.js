'use strict';/*!
 * This is a `i18n` language object.
 *
 * Dutch
 *
 * <AUTHOR>   <PERSON>, Twitter: @LeonarddR
 *   Jalios (Twitter: @Jalios)
 *   Sascha 'SoftCreatR' Greuel
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.nl === undefined) {
		exports.nl = {
			'mejs.plural-form': 1,			
			'mejs.download-file': 'Bestand downloaden',			
			'mejs.install-flash': 'U gebruikt een browser die geen Flash Player heeft ingeschakeld of geïnstalleerd. Zet de Flash Player plug-in of download de nieuwste versie van https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Volledig scherm',			
			'mejs.play': 'Afspelen',
			'mejs.pause': 'Pauzeren',			
			'mejs.time-slider': 'Tijd schuifbalk',
			'mejs.time-help-text': 'Gebruik pijl naar links/rechts om per seconde te springen, pijl omhoog/omlaag om per tien seconden te springen.',
			'mejs.live-broadcast' : 'Live uitzending',			
			'mejs.volume-help-text': 'Gebruik pijl omhoog/omlaag om het volume te verhogen/verlagen.',
			'mejs.unmute': 'Dempen opheffen',
			'mejs.mute': 'Dempen',
			'mejs.volume-slider': 'Volume schuifbalk',			
			'mejs.video-player': 'Videospeler',
			'mejs.audio-player': 'Audiospeler',			
			'mejs.captions-subtitles': 'Bijschriften/ondertiteling',
			'mejs.captions-chapters': 'Hoofdstukken',
			'mejs.none': 'Geen',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albanees',
			'mejs.arabic': 'Arabisch',
			'mejs.belarusian': 'Wit-Russisch',
			'mejs.bulgarian': 'Bulgaars',
			'mejs.catalan': 'Catalaans',
			'mejs.chinese': 'Chinees',
			'mejs.chinese-simplified': 'Chinees (Vereenvoudigd)',
			'mejs.chinese-traditional': 'Chinees (Traditioneel)',
			'mejs.croatian': 'Kroatisch',
			'mejs.czech': 'Tsjechisch',
			'mejs.danish': 'Deens',
			'mejs.dutch': 'Nederlands',
			'mejs.english': 'Engels',
			'mejs.estonian': 'Estlands',
			'mejs.filipino': 'Filipijns',
			'mejs.finnish': 'Finse',
			'mejs.french': 'Frans',
			'mejs.galician': 'Galicisch',
			'mejs.german': 'Duits',
			'mejs.greek': 'Grieks',
			'mejs.haitian-creole': 'Haïtiaanse Creoolse',
			'mejs.hebrew': 'Hebreeuws',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Hongaars',
			'mejs.icelandic': 'Icelandic',
			'mejs.indonesian': 'Indonesisch',
			'mejs.irish': 'Iers',
			'mejs.italian': 'Italiaans',
			'mejs.japanese': 'Japans',
			'mejs.korean': 'Koreaans',
			'mejs.latvian': 'Letlands',
			'mejs.lithuanian': 'Litouws',
			'mejs.macedonian': 'Macedonisch',
			'mejs.malay': 'Maleis',
			'mejs.maltese': 'Maltese',
			'mejs.norwegian': 'Noors',
			'mejs.persian': 'Perzisch',
			'mejs.polish': 'Pools',
			'mejs.portuguese': 'Portugees',
			'mejs.romanian': 'Roemeens',
			'mejs.russian': 'Russisch',
			'mejs.serbian': 'Servisch',
			'mejs.slovak': 'Slowaaks',
			'mejs.slovenian': 'Sloveens',
			'mejs.spanish': 'Spaans',
			'mejs.swahili': 'Swahili',
			'mejs.swedish': 'Zweeds',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thai',
			'mejs.turkish': 'Turks',
			'mejs.ukrainian': 'Oekraïens',
			'mejs.vietnamese': 'Vietnamese',
			'mejs.welsh': 'Welsh',
			'mejs.yiddish': 'Jiddisch'
		};
	}
})(mejs.i18n);