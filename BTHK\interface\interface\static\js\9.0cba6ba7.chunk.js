(this.webpackJsonpestra7ah5=this.webpackJsonpestra7ah5||[]).push([[9],{362:function(e,t,n){"use strict";var i=n(0),c=n(13),s=n(1),a=(n(367),n(365)),r=n(12),o=n(2),l=n(374),m=(n(368),n(369),n(48));var d=function(e){Object(s.useEffect)((function(){}),[]);var t="/itemView/"+e.item.item.type+"/"+e.item.item.id;if(e.item.item.content&&null!=e.item.item.content.contentJSON)try{JSON.parse(e.item.item.content.contentJSON)}catch(a){}function n(e){var t=parseInt(e/3600);t<10&&(t="0"+t);var n=parseInt(e%3600/60);n<10&&(n="0"+n);var i=Math.ceil(e%60);return i<10&&(i="0"+i),t+":"+n+":"+i}function c(){return Object(i.jsxs)(i.Fragment,{children:[Object(i.jsx)("div",{className:"over",title:e.item.item.name,children:Object(i.jsx)("div",{className:"inner",children:Object(i.jsx)("div",{children:Object(i.jsx)("i",{onClick:e.onClickIcon,className:"lni lni-trash"})})})}),Object(i.jsx)(m.a,{src:o.a.host+"ItemImage/"+e.item.item.id,alt:e.item.item.name,type:"movie"}),Object(i.jsx)("div",{className:"title",children:e.item.item.name&&e.item.item.name.length>20?"..."+e.item.item.name.substr(0,20):e.item.item.name}),Object(i.jsx)("div",{className:"det",children:Object(i.jsx)("div",{className:"time-line",style:{width:e.item.time/e.item.duration*100+"%"},children:Object(i.jsx)("span",{children:n(e.item.time)})})})]})}return"episod"==e.item.item.type||"vidsSameFolder"==e.item.item.type?Object(i.jsx)("a",{href:"/player/"+e.item.item.id,className:"movie-box-continue",item:e.item.item.id,children:c()},e.item.item.id):Object(i.jsx)(r.b,{to:t,className:"movie-box-continue",item:e.item.item.id,children:c()},e.item.item.id)};n(370);var j=function(e){if(Object(s.useEffect)((function(){}),[]),e.skeleton)return Object(i.jsx)("div",{children:"test"});if(!e.section)return Object(i.jsx)("div",{});var t=parseInt(e.section.createdAt)+259200>parseInt(+new Date/1e3);return Object(i.jsxs)(r.b,{to:"/sections/"+("linked"==e.section.type?e.section.linkedId:e.section.id),className:"section-box "+(e.isFloat&&"float"),onClick:e.onClick,children:[Object(i.jsx)("div",{className:"over",title:e.section.name,children:Object(i.jsx)("div",{children:Object(i.jsx)("i",{onClick:e.onClickIcon,className:"lni lni-"+e.centerIcon})})}),Object(i.jsx)(m.a,{src:o.a.host+"sectionImage/"+e.section.id,alt:e.section.name,type:"section"}),t&&Object(i.jsx)("div",{className:"new-text",children:"\u062c\u062f\u064a\u062f"}),"yes"===e.section.isVIP&&Object(i.jsx)("div",{title:"\u0641\u0642\u0637 \u0644\u0645\u0634\u062a\u0631\u0643\u064a\u0646 VIP",className:"vip-text",children:"VIP"}),Object(i.jsx)("div",{className:"title",children:e.section.name&&e.section.name.length>30?"..."+e.section.name.substr(0,30):e.section.name})]},e.section.id)},u=n(373),b=n(375),f=n(376),h=n(377),O=n(378),v=n(379);n(371);var p=function(e){return Object(i.jsx)("div",{className:"skeleton skeleton-animation"})};t.a=function(e){return Object(i.jsxs)("section",{style:Object(c.a)({},e.style),className:"items",children:[Object(i.jsx)(b.a,{icon:e.data.icon||e.icon,title:e.data.title||"\u0644\u0627\u064a\u0648\u062c\u062f \u0639\u0646\u0648\u0627\u0646",number:e.sections&&e.sections.length||e.items&&e.items.length}),Object(i.jsxs)("div",{children:[(!e.items&&!e.sections||"skeleton"===e.type)&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:window.innerWidth<500?[1,2].map((function(e){return Object(i.jsx)(p,{})})):[1,2,3,4,6,7,8,9,10].map((function(e){return Object(i.jsx)(p,{})}))})}),e.sections&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,isFloat:!0,autoMove:e.autoMove,children:e.sections&&e.sections.map((function(t){return Object(i.jsx)(j,{centerIcon:e.centerIcon,section:t,isFloat:!0,onClick:e.onItemClick})}))})}),e.items&&("movie"==e.type||"movies"==e.type||-1!==e.type.search("serieses")||-1!==e.type.search("series")||"seasons"==e.type)&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:e.items&&e.items.map((function(t){return t?Object(i.jsx)(l.a,{selected:e.selectedItemId===t.id,onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,appendLink:e.appendLink},t.id):Object(i.jsxs)(r.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:o.a.host+e.image+"error"})]},t.id)}))})}),e.items&&"eps"==e.type&&Object(i.jsx)("div",{className:"cont-all ",children:e.items&&e.items.map((function(t){return t?Object(i.jsx)(u.a,{selected:e.selectedItemId===t.id,onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,eps:e.items}):Object(i.jsxs)(r.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:o.a.host+e.image+"error"})]},t.id)}))}),e.items&&"continue-watch"==e.type&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:e.items&&e.items.map((function(t){return t?Object(i.jsx)(d,{item:t,onClickIcon:function(e){o.a.delWatch(t.item.id),e.preventDefault()}}):Object(i.jsxs)(r.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:o.a.host+e.image+"error"})]},t.id)}))})}),e.items&&"favs"==e.type&&Object(i.jsx)("div",{className:"cont-all",children:Object(i.jsx)(a.a,{rtl:!0,noEffects:!1,size:e.size,autoMove:e.autoMove,children:e.items&&e.items.map((function(t){return t?"app"==t.type||"appsSameFolder"==t.type?Object(i.jsx)("div",{style:{marginBottom:40},children:Object(i.jsx)(f.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){o.a.delFav(t.id),e.preventDefault()}})},t.id):"books"==t.type||"booksSameFolder"==t.type?Object(i.jsx)(h.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){o.a.delFav(t.id),e.preventDefault()}},t.id):"imagesSameFolder"==t.type?Object(i.jsx)(O.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){o.a.delFav(t.id),e.preventDefault()}},t.id):"audioSameFolder"==t.type||"singer"==t.type||"album"==t.type?Object(i.jsx)(v.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){o.a.delFav(t.id),e.preventDefault()}},t.id):"series"==t.type?Object(i.jsx)("div",{style:{marginBottom:40},children:Object(i.jsx)(l.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){o.a.delFav(t.id),e.preventDefault()}},t.id)}):"vidsSameFolder"==t.type?Object(i.jsx)("div",{style:{marginBottom:40},children:Object(i.jsx)(u.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){o.a.delFav(t.id),e.preventDefault()}},t.id)}):Object(i.jsx)(l.a,{onClick:e.onItemClick,item:t,centerIcon:e.centerIcon,onClickIcon:function(e){o.a.delFav(t.id),e.preventDefault()}},t.id):Object(i.jsxs)(r.b,{className:"box",children:[Object(i.jsx)("div",{className:"name",children:"error"}),Object(i.jsx)("img",{loading:"lazy",src:o.a.host+e.image+"error"})]},t.id)}))})})]})]})}},365:function(e,t,n){"use strict";var i=n(0),c=n(5),s=n(1);n(366);t.a=function(e){var t=Object(s.useState)(0),n=Object(c.a)(t,2),a=n[0],r=n[1];return e.isFloat?Object(i.jsx)("div",{className:"float-slider",children:e.children}):Object(i.jsxs)("div",{className:"slider-kc",children:[Object(i.jsx)("div",{className:"items",style:{left:a},children:e.children}),Object(i.jsxs)("div",{className:"arrows",children:[Object(i.jsx)("div",{className:"left-arrow",onClick:function(){r(a+400)},children:Object(i.jsx)("i",{className:"lni lni-chevron-left"})}),Object(i.jsx)("div",{className:"right-arrow",onClick:function(){r(a-400)},style:{display:0===a?"none":"block"},children:Object(i.jsx)("i",{className:"lni lni-chevron-right"})})]})]})}},366:function(e,t,n){},367:function(e,t,n){},369:function(e,t,n){},370:function(e,t,n){},371:function(e,t,n){},389:function(e,t,n){"use strict";var i=n(0),c=n(4),s=n.n(c),a=n(13),r=n(6),o=n(5),l=n(1),m=(n(390),n(8)),d=n(2);t.a=function(e){var t=e.name,n=(e.url_to_post,Object(l.useState)(null)),c=Object(o.a)(n,2),j=c[0],u=c[1],b=Object(l.useState)(e.defaultValue),f=Object(o.a)(b,2),h=f[0],O=f[1];function v(e){return p.apply(this,arguments)}function p(){return(p=Object(r.a)(s.a.mark((function t(n){var i;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.url){t.next=2;break}return t.abrupt("return");case 2:return(i=new FormData).append(e.name,n),t.next=6,d.a.fetch(e.url,{method:"POST",body:i,type:"post"}).then((function(e){return e.json()}));case 6:"ok"==t.sent.msg&&m.Store.addNotification({title:"\u062a\u0645 \u0627\u0644\u062d\u0641\u0638 \u0628\u0646\u062c\u0627\u062d",message:" \u062a\u0645 \u062d\u0641\u0638 : "+e.title,type:"success",insert:"top",container:"bottom-left",animationIn:["animate__animated","animate__fadeIn"],animationOut:["animate__animated","animate__fadeOut"],dismiss:{duration:1700}});case 8:case"end":return t.stop()}}),t)})))).apply(this,arguments)}return Object(l.useEffect)((function(){e.defaultValue!=h&&(""!=h&&"undefined"!=typeof h&&v(e.defaultValue),O(e.defaultValue))}),[e]),Object(i.jsxs)("div",{className:"input-count",style:Object(a.a)({},e.style),children:[Object(i.jsx)("div",{className:"head",children:e.title}),Object(i.jsx)("input",{ref:e.localref,className:e.dir,name:t,style:Object(a.a)(Object(a.a)({},e.inputStyle),{},{direction:e.dir}),defaultValue:e.defaultValue,onKeyPress:e.onKeyPress,onChange:function(t){if(e.onChange)e.onChange(t);else if(e.saveOnChange){null!=j&&clearTimeout(j);var n=t.currentTarget.value;u(setTimeout(Object(r.a)(s.a.mark((function e(){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:v(n),u(null);case 2:case"end":return e.stop()}}),e)}))),1300))}},type:e.type,placeholder:e.placeholder||e.title,required:e.required},e.key),Object(i.jsx)("div",{className:"checked ok "+e.dir,children:Object(i.jsx)("i",{className:"lni lni-"+e.icon})})]},e.key)}},390:function(e,t,n){},418:function(e,t,n){},430:function(e,t,n){"use strict";n.r(t);var i=n(0),c=n(4),s=n.n(c),a=n(6),r=n(5),o=n(1),l=(n(418),n(364)),m=n(11),d=n(389),j=n(2);function u(e){return j.a.fetch(j.a.host+"getItemsSearch/0/100/"+e,{method:"GET"}).then((function(e){return e.json()}))}var b=n(362);t.default=function(e){var t=Object(o.useState)([]),n=Object(r.a)(t,2),c=n[0],j=n[1],f=Object(o.useState)(2),h=Object(r.a)(f,2),O=(h[0],h[1]),v=Object(o.useRef)(null),p=Object(o.useState)(null),x=Object(r.a)(p,2),y=x[0],k=x[1],I=Object(o.useState)(!1),N=Object(r.a)(I,2),g=N[0],C=N[1];function w(e){for(var t={},n=0;n<e.length;n++){var i,c,s;t[null===(i=e[n].section)||void 0===i?void 0:i.name]||(t[null===(c=e[n].section)||void 0===c?void 0:c.name]=[]),t[null===(s=e[n].section)||void 0===s?void 0:s.name].push(e[n])}for(var a=Object.keys(t),r=[],o=0;o<a.length;o++)r.push({groupName:a[o],items:t[a[o]]});return r}function S(){return F.apply(this,arguments)}function F(){return(F=Object(a.a)(s.a.mark((function e(){var t,n,i;return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return C(!0),t=v.current.value,e.next=4,u([t]);case 4:if(e.t0=e.sent,e.t0){e.next=7;break}e.t0=[];case 7:n=e.t0,i=w(n),j(i),C(!1);case 11:case"end":return e.stop()}}),e)})))).apply(this,arguments)}return Object(o.useEffect)(Object(a.a)(s.a.mark((function e(){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:O(3),window.innerWidth<1150&&O(4),window.innerWidth<800&&O(4),window.innerWidth<600&&O(5);case 4:case"end":return e.stop()}}),e)}))),[e]),Object(i.jsxs)("div",{children:[Object(i.jsx)(l.a,{}),Object(i.jsxs)("div",{className:"search-items-count ",children:[Object(i.jsx)("div",{className:"bg-all"}),Object(i.jsx)("div",{className:"search-bar",children:Object(i.jsx)(m.Grid,{fluid:!0,children:Object(i.jsx)(m.Row,{children:Object(i.jsx)(m.Col,{xs:12,md:12,children:Object(i.jsx)(d.a,{localref:v,title:"\u0627\u062f\u062e\u0644 \u0643\u0645\u0644\u0629 \u0644\u0644\u0628\u062d\u062b",type:"text",required:!1,icon:"search",name:"",onChange:function(){null!=y&&clearTimeout(y),j([]),k(setTimeout(Object(a.a)(s.a.mark((function e(){return s.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:S(),k(null);case 2:case"end":return e.stop()}}),e)}))),500))},style:{width:"90%"}})})})})}),g&&Object(i.jsx)("div",{style:{padding:20},children:"\u064a\u062a\u0645 \u0627\u0644\u0628\u062d\u062b ..."}),!g&&c.map((function(e){e.items;return Object(i.jsx)(b.a,{size:"small",data:{title:e.groupName,icon:"pin"},items:e.items,type:"movie",image:"ItemImage/",centerIcon:"play"})}))]}),Object(i.jsx)("div",{style:{clear:"both"}})]})}}}]);