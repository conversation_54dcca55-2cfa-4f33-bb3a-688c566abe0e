'use strict';/*!
 * This is a `i18n` language object.
 *
 * French
 *
 * <AUTHOR>   <PERSON> (Twitter: @klohFR)
 *   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.fr === undefined) {
		exports.fr = {
			'mejs.plural-form': 2,			
			'mejs.download-file': 'Télécharger le fichier',			
			'mejs.install-flash': 'Vous utilisez un navigateur sur lequel Flash ne semble pas installé ou activé. Veuillez activer le plugin Flash ou télécharger la dernière version sur https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Plein écran',			
			'mejs.play': 'Lecture',
			'mejs.pause': 'Pause',			
			'mejs.time-slider': 'Curseur temporel',
			'mejs.time-help-text': 'Utilisez les flèches Gauche/Droite du clavier pour avancer d\'une seconde, les flèches Haut/Bas pour avancer de 10 secondes.',
			'mejs.live-broadcast' : 'Diffusion en direct',			
			'mejs.volume-help-text': 'Utilisez les flèches Haut/Bas du clavier pour augmenter ou diminuer le volume.',
			'mejs.unmute': 'Activer le son',
			'mejs.mute': 'Désactiver le son',
			'mejs.volume-slider': 'Volume',			
			'mejs.video-player': 'Lecteur Vidéo',
			'mejs.audio-player': 'Lecteur Audio',			
			'mejs.captions-subtitles': 'Sous-titres',
			'mejs.captions-chapters': 'Chapitres',
			'mejs.none': 'Aucun',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albanais',
			'mejs.arabic': 'Arabe',
			'mejs.belarusian': 'Biélorusse',
			'mejs.bulgarian': 'Bulgare',
			'mejs.catalan': 'Catalan',
			'mejs.chinese': 'Chinois',
			'mejs.chinese-simplified': 'Chinois (simplifié)',
			'mejs.chinese-traditional': 'Chinois (traditionnel)',
			'mejs.croatian': 'Croate',
			'mejs.czech': 'Tchèque',
			'mejs.danish': 'Danois',
			'mejs.dutch': 'Néerlandais',
			'mejs.english': 'Anglais',
			'mejs.estonian': 'Estonien',
			'mejs.filipino': 'Filipino',
			'mejs.finnish': 'Finnois',
			'mejs.french': 'Français',
			'mejs.galician': 'Galicien',
			'mejs.german': 'Allemand',
			'mejs.greek': 'Grec',
			'mejs.haitian-creole': 'Créole haïtien',
			'mejs.hebrew': 'Hébreu',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Hongrois',
			'mejs.icelandic': 'Islandais',
			'mejs.indonesian': 'Indonésien',
			'mejs.irish': 'Irlandais',
			'mejs.italian': 'Italien',
			'mejs.japanese': 'Japonais',
			'mejs.korean': 'Coréen',
			'mejs.latvian': 'Letton',
			'mejs.lithuanian': 'Lituanien',
			'mejs.macedonian': 'Macédonien',
			'mejs.malay': 'Malais',
			'mejs.maltese': 'Maltais',
			'mejs.norwegian': 'Norvégien',
			'mejs.persian': 'Perse',
			'mejs.polish': 'Polonais',
			'mejs.portuguese': 'Portugais',
			'mejs.romanian': 'Roumain',
			'mejs.russian': 'Russe',
			'mejs.serbian': 'Serbe',
			'mejs.slovak': 'Slovaque',
			'mejs.slovenian': 'Slovène',
			'mejs.spanish': 'Espagnol',
			'mejs.swahili': 'Swahili',
			'mejs.swedish': 'Suédois',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thaï',
			'mejs.turkish': 'Turque',
			'mejs.ukrainian': 'Ukrainien',
			'mejs.vietnamese': 'Vietnamien',
			'mejs.welsh': 'Gallois',
			'mejs.yiddish': 'Yiddish'
		};
	}
})(mejs.i18n);