<!doctype html><html lang="ar" style="background:#0d0c11;direction:rtl"><head><meta charset="utf-8"/><link rel="icon" href="/favicon.ico"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#000000"/><meta name="description" content="Media Server Estra7ah"/><link rel="apple-touch-icon" href="/logo192.png"/><link rel="manifest" href="/manifest.json"/><title>Estra7ah Manager</title><link href="/static/css/5.799f9037.chunk.css" rel="stylesheet"><link href="/static/css/main.3ad771fb.chunk.css" rel="stylesheet"></head><body><noscript>الرجاء تشغيل الجافاسكربت لتصفح الاستراحه</noscript><div id="the-block-message" style="display:none;position:fixed;top:50px;right:0;background:red;width:100%;z-index:99;padding:10px;text-align:center">الرجاء فتح الاستراحه عبر متصفح كروم ادخل هذا العنوان :<br/>e.com او ***********<div style="font-size:30px;font-weight:700;position:absolute;top:19px" onclick="return function(e){console.log(e),e.currentTarget.parentNode.remove()}(arguments[0]),!1">X</div></div><script>let dbm=document.getElementById("the-block-message");window.localStorage?dbm&&dbm.remove():dbm&&(dbm.style.display="block");try{window.gui=require("nw.gui")}catch(e){}</script><div id="root"><div style="color:#fff;height:100vh;display:flex;justify-content:center;align-items:center"><div style="text-align:center"><div><img src="/estra7ah.svg" style="width:150px"/></div><div style="font-size:12px" id="estra7ah-app"></div></div></div></div><script>const estra7ahAppDOM=document.getElementById("estra7ah-app");window.ReactNativeWebView?estra7ahAppDOM.innerText="اهلاً بك 👋":estra7ahAppDOM.innerText=" يتم التحميل ↻",setTimeout(()=>{document.getElementById("estra7ah-app")&&window.location.reload()},2e4)</script><script>!function(e){function t(t){for(var n,a,u=t[0],i=t[1],f=t[2],l=0,d=[];l<u.length;l++)a=u[l],Object.prototype.hasOwnProperty.call(o,a)&&o[a]&&d.push(o[a][0]),o[a]=0;for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n]);for(s&&s(t);d.length;)d.shift()();return c.push.apply(c,f||[]),r()}function r(){for(var e,t=0;t<c.length;t++){for(var r=c[t],n=!0,a=1;a<r.length;a++){var i=r[a];0!==o[i]&&(n=!1)}n&&(c.splice(t--,1),e=u(u.s=r[0]))}return e}var n={},a={4:0},o={4:0},c=[];function u(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,u),r.l=!0,r.exports}u.e=function(e){var t=[];a[e]?t.push(a[e]):0!==a[e]&&{0:1,1:1,2:1,6:1,7:1,8:1,9:1,10:1,11:1,12:1,13:1,14:1,15:1,16:1,17:1}[e]&&t.push(a[e]=new Promise((function(t,r){for(var n="static/css/"+({}[e]||e)+"."+{0:"44633078",1:"9c33f519",2:"89a5dc3c",6:"ae9a6588",7:"7eaa7938",8:"1229693c",9:"81b599c7",10:"9090c8c7",11:"6d0956a5",12:"62f90cd8",13:"d4ead9e6",14:"de43a2ac",15:"ffd56efb",16:"8d647e51",17:"143efd4b"}[e]+".chunk.css",o=u.p+n,c=document.getElementsByTagName("link"),i=0;i<c.length;i++){var f=(s=c[i]).getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(f===n||f===o))return t()}var l=document.getElementsByTagName("style");for(i=0;i<l.length;i++){var s;if((f=(s=l[i]).getAttribute("data-href"))===n||f===o)return t()}var d=document.createElement("link");d.rel="stylesheet",d.type="text/css",d.onload=t,d.onerror=function(t){var n=t&&t.target&&t.target.src||o,c=new Error("Loading CSS chunk "+e+" failed.\n("+n+")");c.code="CSS_CHUNK_LOAD_FAILED",c.request=n,delete a[e],d.parentNode.removeChild(d),r(c)},d.href=o,document.getElementsByTagName("head")[0].appendChild(d)})).then((function(){a[e]=0})));var r=o[e];if(0!==r)if(r)t.push(r[2]);else{var n=new Promise((function(t,n){r=o[e]=[t,n]}));t.push(r[2]=n);var c,i=document.createElement("script");i.charset="utf-8",i.timeout=120,u.nc&&i.setAttribute("nonce",u.nc),i.src=function(e){return u.p+"static/js/"+({}[e]||e)+"."+{0:"537afed0",1:"c15b734e",2:"70d45898",6:"4b97a3ad",7:"b08e9d53",8:"633cf2a2",9:"0cba6ba7",10:"f8a46efe",11:"8e01fbaa",12:"d4c2e687",13:"a7a4defe",14:"35d7d742",15:"76b88917",16:"db2a95f2",17:"cc7e61b9"}[e]+".chunk.js"}(e);var f=new Error;c=function(t){i.onerror=i.onload=null,clearTimeout(l);var r=o[e];if(0!==r){if(r){var n=t&&("load"===t.type?"missing":t.type),a=t&&t.target&&t.target.src;f.message="Loading chunk "+e+" failed.\n("+n+": "+a+")",f.name="ChunkLoadError",f.type=n,f.request=a,r[1](f)}o[e]=void 0}};var l=setTimeout((function(){c({type:"timeout",target:i})}),12e4);i.onerror=i.onload=c,document.head.appendChild(i)}return Promise.all(t)},u.m=e,u.c=n,u.d=function(e,t,r){u.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},u.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u.t=function(e,t){if(1&t&&(e=u(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(u.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)u.d(r,n,function(t){return e[t]}.bind(null,n));return r},u.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return u.d(t,"a",t),t},u.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},u.p="/",u.oe=function(e){throw console.error(e),e};var i=this.webpackJsonpestra7ah5=this.webpackJsonpestra7ah5||[],f=i.push.bind(i);i.push=t,i=i.slice();for(var l=0;l<i.length;l++)t(i[l]);var s=f;r()}([])</script><script src="/static/js/5.41700f1e.chunk.js"></script><script src="/static/js/main.f9f29ac5.chunk.js"></script></body></html>