<!DOCTYPE html>
<html>
  {% include "admin/head.html" %}
  <body dir="rtl" class="noselect">
    {% include "admin/mnu.html" %}
    <style>
      #generated_iframe_code,
      #generated_iframe_code_all,
      #channel_url {
        height: 165px;
        font-family: monospace;
        direction: ltr;
        color: #bfbfbf;
      }
    </style>

    <div class="nopadding">
      <div class="page">
        <div class="msg info">
          من هنا تستطيع اضافة صفحة البث المباشر او القناه المناسبه في واجهة صفحة الميكروتك لديك
        </div>
        <h4>اضافة قناة لصفحة الـHotspot</h4>
        <div class="msg info">
          اختر قناتك ثم حدد الطول والعرض لمشغل البث المابشر وحدد المشغل المناسب ثم انسخ الكود اسفل والصقه في اي صفحه من
          صفحات الميكروتك
        </div>
        <div class="content">
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">اختر القناة التي تريد عرضها في واجهة صفحتك</div>
              <div class="in-e col-sm-10">
                <select id="channels_select">
                  {% for c in channels %}

                  <option value="{{c.id}}">{{c.cha_name}}</option>

                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <hr />
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">طول مشغل البث المباشر</div>
              <div class="in-e col-sm-10">
                <input name="p_w" id="p_w" value="100f" placeholder="طول مشغل البث المباشر" />
                <span style="color: #999">يمكنك وضع حرف f في نهايه الرقم لكي يكون عرض كامل الشاشه مثال 100f</span>
              </div>
            </div>
          </div>
          <hr />
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">عرض مشغل البث المباشر</div>
              <div class="in-e col-sm-10">
                <input name="p_h" id="p_h" value="470" placeholder="عرض مشغل البث المباشر" />
              </div>
            </div>
          </div>
          <hr />
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">مشغل البث المباشر</div>
              <div class="in-e col-sm-10">
                <select id="playerselect">
                  <option value="Flowplayer">FlowPlayer</option>
                  <option value="MediaElementJS" selected>MediaElementJS</option>
                  <option value="hdwplayer">HDW Player</option>
                </select>
              </div>
            </div>
          </div>
          <hr />
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">كود الصفحه</div>
              <div class="in-e col-sm-10">
                <textarea id="generated_iframe_code" disabled> </textarea>
              </div>
            </div>
          </div>
          <hr />
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">لعرض كل القنوات</div>
              <div class="in-e col-sm-10">
                <textarea id="generated_iframe_code_all" disabled> </textarea>
              </div>
            </div>
          </div>
          <script>
            function generated_iframe_code() {
              const ip = location.hostname;
              var channels_select = $("#channels_select").val();
              var wf = $("#p_w").val();
              var w = $("#p_w").val();
              var wf = wf.replace("f", "%");
              var h = $("#p_h").val();
              var playerselect = $("#playerselect").val();

              var port = "{{port}}";
              $("#generated_iframe_code").val(
                "<iframe src='http://" +
                  ip +
                  ":" +
                  port +
                  "/player/" +
                  w +
                  "/" +
                  h +
                  "/" +
                  playerselect +
                  "/" +
                  channels_select +
                  "' width='" +
                  wf +
                  "' height='" +
                  h +
                  "' ></iframe> "
              );

              $("#generated_iframe_code_all").val(
                "<iframe src='http://" + ip + ":" + port + "/' width='" + wf + "' height='" + h + "' ></iframe> "
              );
            }
            function when_chane() {
              generated_iframe_code();
            }
            $("#channels_select").change(when_chane);
            $("#playerselect").change(when_chane);
            $("#p_w").change(when_chane);
            $("#p_h").change(when_chane);
            generated_iframe_code();
          </script>
        </div>
        <h4>اضافة قناة لاستراحة مانجر</h4>
        <div class="msg info">يمكنك اضافة عدة قنوات الى نظام استراحة مانجر على شكل اقسام</div>
        <ol style="margin: 10px; font-size: 18px; margin-bottom: 20px">
          <li>قم بانشاء قسم رئيسي جديد وحدد له صورة وسمه مثلاً قنوات البث المباشر</li>
          <li>في داخل القسم اضف القناة الاولى كمثال قناه بن سبورت 1 واضف لها صوره</li>
          <li>قبل حفظ القسم اختر في خانه "عرض القسم كـ" اختر "رابط فيديو او بث مباشر (من الانترنت مباشرة)"</li>
          <li>قم بادخل رابط القناه الذي تريده باختيار القناة تحت</li>
          <li>احفظ القسم في نظام استراحه مانجر وسيتم تشغيل القناة في نظام استراحة مانجر بواسطه نظام مباشر</li>
        </ol>
        <div class="content">
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">اختر القناة التي تريد ربطها بنظام الاستراحة</div>
              <div class="in-e col-sm-10">
                <select id="channels_select_for_url">
                  {% for c in channels %}
                  <option value="{{c.id}}">{{c.cha_name}}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <hr />
          <hr />
          <div class="input">
            <div class="row nopadding">
              <div class="text col-sm-2">رابط البث للقناة</div>
              <div class="in-e col-sm-10">
                <textarea id="channel_url" disabled></textarea>
              </div>
            </div>
          </div>
          <script>
            function geturl() {
              var cha_id = $("#channels_select_for_url").val();
              $.ajax({
                url: "/{{admin_panel_path}}/getChannelURL/" + cha_id,
                success: function (src) {
                  $("#channel_url").val(src.url);
                },
              });
            }
            geturl();
            $("#channels_select_for_url").change(geturl);
          </script>
        </div>
      </div>
    </div>

    {% include "admin/footer.html" %}
  </body>
</html>
