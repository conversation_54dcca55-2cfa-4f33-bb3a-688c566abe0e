'use strict';/*!
 * This is a `i18n` language object.
 *
 * Brazilian (Portuguese)
 *
 * <AUTHOR>   <PERSON><PERSON> (Twitter: @odnamrataizem)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports['pt-BR'] === undefined) {
		exports['pt-BR'] = {
			"mejs.plural-form": 2,			
			"mejs.download-file": "Baixar arquivo",			
			// "mejs.install-flash": "You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/",			
			"mejs.fullscreen": "Tela inteira",			
			//"mejs.play": "Play",
			//"mejs.pause": "Pause",			
			//"mejs.time-slider": "Time Slider",
			//"mejs.time-help-text": "Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds.",
			//"mejs.live-broadcast" : "Live Broadcast",			
			//"mejs.time-skip-back": "Skip back %1 second(s)",			
			//"mejs.volume-help-text": "Use Up/Down Arrow keys to increase or decrease volume.",
			"mejs.unmute": "Tirar silêncio",
			"mejs.mute": "Silenciar",
			//"mejs.volume-slider": "Volume Slider",			
			//"mejs.video-player": "Video Player",
			//"mejs.audio-player": "Audio Player",			
			"mejs.captions-subtitles": "Legendas",
			// "mejs.captions-chapters": "Chapters",
			"mejs.none": "Sem legendas"
			// "mejs.afrikaans": "Afrikaans",
			// "mejs.albanian": "Albanian",
			// "mejs.arabic": "Arabic",
			// "mejs.belarusian": "Belarusian",
			// "mejs.bulgarian": "Bulgarian",
			// "mejs.catalan": "Catalan",
			// "mejs.chinese": "Chinese",
			// "mejs.chinese-simplified": "Chinese (Simplified)",
			// "mejs.chinese-traditional": "Chinese (Traditional)",
			// "mejs.croatian": "Croatian",
			// "mejs.czech": "Czech",
			// "mejs.danish": "Danish",
			// "mejs.dutch": "Dutch",
			// "mejs.english": "English",
			// "mejs.estonian": "Estonian",
			// "mejs.filipino": "Filipino",
			// "mejs.finnish": "Finnish",
			// "mejs.french": "French",
			// "mejs.galician": "Galician",
			// "mejs.german": "German",
			// "mejs.greek": "Greek",
			// "mejs.haitian-creole": "Haitian Creole",
			// "mejs.hebrew": "Hebrew",
			// "mejs.hindi": "Hindi",
			// "mejs.hungarian": "Hungarian",
			// "mejs.icelandic": "Icelandic",
			// "mejs.indonesian": "Indonesian",
			// "mejs.irish": "Irish",
			// "mejs.italian": "Italian",
			// "mejs.japanese": "Japanese",
			// "mejs.korean": "Korean",
			// "mejs.latvian": "Latvian",
			// "mejs.lithuanian": "Lithuanian",
			// "mejs.macedonian": "Macedonian",
			// "mejs.malay": "Malay",
			// "mejs.maltese": "Maltese",
			// "mejs.norwegian": "Norwegian",
			// "mejs.persian": "Persian",
			// "mejs.polish": "Polish",
			// "mejs.portuguese": "Portuguese",
			// "mejs.romanian": "Romanian",
			// "mejs.russian": "Russian",
			// "mejs.serbian": "Serbian",
			// "mejs.slovak": "Slovak",
			// "mejs.slovenian": "Slovenian",
			// "mejs.spanish": "Spanish",
			// "mejs.swahili": "Swahili",
			// "mejs.swedish": "Swedish",
			// "mejs.tagalog": "Tagalog",
			// "mejs.thai": "Thai",
			// "mejs.turkish": "Turkish",
			// "mejs.ukrainian": "Ukrainian",
			// "mejs.vietnamese": "Vietnamese",
			// "mejs.welsh": "Welsh",
			// "mejs.yiddish": "Yiddish"
		};
	}
})(mejs.i18n);