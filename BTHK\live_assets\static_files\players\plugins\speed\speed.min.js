/*!
 * MediaElement.js
 * http://www.mediaelementjs.com/
 *
 * Wrapper that mimics native HTML5 MediaElement (audio and video)
 * using a variety of technologies (pure JavaScript, Flash, iframe)
 *
 * Copyright 2010-2017, <PERSON> (http://j.hn/)
 * License: MIT
 *
 */
!function e(t,s,o){function n(l,i){if(!s[l]){if(!t[l]){var a="function"==typeof require&&require;if(!i&&a)return a(l,!0);if(r)return r(l,!0);var d=new Error("Cannot find module '"+l+"'");throw d.code="MODULE_NOT_FOUND",d}var p=s[l]={exports:{}};t[l][0].call(p.exports,function(e){var s=t[l][1][e];return n(s||e)},p,p.exports,e,t,s,o)}return s[l].exports}for(var r="function"==typeof require&&require,l=0;l<o.length;l++)n(o[l]);return n}({1:[function(e,t,s){"use strict";mejs.i18n.en["mejs.speed-rate"]="Speed Rate",Object.assign(mejs.MepDefaults,{speeds:["2.00","1.50","1.25","1.00","0.75"],defaultSpeed:"1.00",speedChar:"x",speedText:null}),Object.assign(MediaElementPlayer.prototype,{buildspeed:function(e,t,s,o){var n=this;if(null!==n.media.rendererName&&/(native|html5)/i.test(n.media.rendererName)){for(var r=[],l=mejs.Utils.isString(n.options.speedText)?n.options.speedText:mejs.i18n.t("mejs.speed-rate"),i=function(e){for(var t=0,s=r.length;t<s;t++)if(r[t].value===e)return r[t].name},a=void 0,d=!1,p=0,c=n.options.speeds.length;p<c;p++){var u=n.options.speeds[p];"string"==typeof u?(r.push({name:""+u+n.options.speedChar,value:u}),u===n.options.defaultSpeed&&(d=!0)):(r.push(u),u.value===n.options.defaultSpeed&&(d=!0))}d||r.push({name:n.options.defaultSpeed+n.options.speedChar,value:n.options.defaultSpeed}),r.sort(function(e,t){return parseFloat(t.value)-parseFloat(e.value)}),n.cleanspeed(e),e.speedButton=document.createElement("div"),e.speedButton.className=n.options.classPrefix+"button "+n.options.classPrefix+"speed-button",e.speedButton.innerHTML='<button type="button" aria-controls="'+n.id+'" title="'+l+'" aria-label="'+l+'" tabindex="0">'+i(n.options.defaultSpeed)+'</button><div class="'+n.options.classPrefix+"speed-selector "+n.options.classPrefix+'offscreen"><ul class="'+n.options.classPrefix+'speed-selector-list"></ul></div>',n.addControlElement(e.speedButton,"speed");for(var f=0,v=r.length;f<v;f++){var m=n.id+"-speed-"+r[f].value;e.speedButton.querySelector("ul").innerHTML+='<li class="'+n.options.classPrefix+'speed-selector-list-item"><input class="'+n.options.classPrefix+'speed-selector-input" type="radio" name="'+n.id+'_speed"disabled="disabled" value="'+r[f].value+'" id="'+m+'"  '+(r[f].value===n.options.defaultSpeed?' checked="checked"':"")+'/><label for="'+m+'" class="'+n.options.classPrefix+"speed-selector-label"+(r[f].value===n.options.defaultSpeed?" "+n.options.classPrefix+"speed-selected":"")+'">'+r[f].name+"</label></li>"}a=n.options.defaultSpeed,e.speedSelector=e.speedButton.querySelector("."+n.options.classPrefix+"speed-selector");for(var h=["mouseenter","focusin"],S=["mouseleave","focusout"],x=e.speedButton.querySelectorAll('input[type="radio"]'),b=e.speedButton.querySelectorAll("."+n.options.classPrefix+"speed-selector-label"),g=0,y=h.length;g<y;g++)e.speedButton.addEventListener(h[g],function(){mejs.Utils.removeClass(e.speedSelector,n.options.classPrefix+"offscreen"),e.speedSelector.style.height=e.speedSelector.querySelector("ul").offsetHeight,e.speedSelector.style.top=-1*parseFloat(e.speedSelector.offsetHeight)+"px"});for(var P=0,j=S.length;P<j;P++)e.speedSelector.addEventListener(S[P],function(){mejs.Utils.addClass(this,n.options.classPrefix+"offscreen")});for(var B=0,E=x.length;B<E;B++){var C=x[B];C.disabled=!1,C.addEventListener("click",function(){var t=this,s=t.value;a=s,o.playbackRate=parseFloat(s),e.speedButton.querySelector("button").innerHTML=i(s);for(var r=e.speedButton.querySelectorAll("."+n.options.classPrefix+"speed-selected"),l=0,d=r.length;l<d;l++)mejs.Utils.removeClass(r[l],n.options.classPrefix+"speed-selected");t.checked=!0;for(var p=mejs.Utils.siblings(t,function(e){return mejs.Utils.hasClass(e,n.options.classPrefix+"speed-selector-label")}),c=0,u=p.length;c<u;c++)mejs.Utils.addClass(p[c],n.options.classPrefix+"speed-selected")})}for(var U=0,q=b.length;U<q;U++)b[U].addEventListener("click",function(){var e=mejs.Utils.siblings(this,function(e){return"INPUT"===e.tagName})[0],t=mejs.Utils.createEvent("click",e);e.dispatchEvent(t)});e.speedSelector.addEventListener("keydown",function(e){e.stopPropagation()}),o.addEventListener("loadedmetadata",function(){a&&(o.playbackRate=parseFloat(a))})}},cleanspeed:function(e){e&&(e.speedButton&&e.speedButton.parentNode.removeChild(e.speedButton),e.speedSelector&&e.speedSelector.parentNode.removeChild(e.speedSelector))}})},{}]},{},[1]);