const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs-extra');

class FFmpegManager {
    constructor() {
        this.activeStreams = new Map();
        this.streamPort = 8554; // RTSP port
        this.hlsPort = 8080; // HLS port
        this.outputDir = path.join(__dirname, 'live_streams');
        this.ensureOutputDir();
    }

    ensureOutputDir() {
        fs.ensureDirSync(this.outputDir);
    }

    // Get available video/audio devices
    async getAvailableDevices() {
        return new Promise((resolve, reject) => {
            const devices = {
                video: [],
                audio: []
            };

            // Get DirectShow devices (Windows)
            exec('ffmpeg -list_devices true -f dshow -i dummy', (error, stdout, stderr) => {
                const output = stderr || stdout;
                
                // Parse video devices
                const videoMatches = output.match(/\[dshow.*?\] "([^"]+)" \(video\)/g);
                if (videoMatches) {
                    videoMatches.forEach(match => {
                        const deviceName = match.match(/"([^"]+)"/)[1];
                        devices.video.push({
                            name: deviceName,
                            id: deviceName,
                            type: 'dshow'
                        });
                    });
                }

                // Parse audio devices
                const audioMatches = output.match(/\[dshow.*?\] "([^"]+)" \(audio\)/g);
                if (audioMatches) {
                    audioMatches.forEach(match => {
                        const deviceName = match.match(/"([^"]+)"/)[1];
                        devices.audio.push({
                            name: deviceName,
                            id: deviceName,
                            type: 'dshow'
                        });
                    });
                }

                resolve(devices);
            });
        });
    }

    // Start capturing from USB/HDMI device
    startCapture(options) {
        const {
            streamId,
            videoDevice,
            audioDevice,
            resolution = '1920x1080',
            framerate = '30',
            bitrate = '2000k',
            audioSampleRate = '44100'
        } = options;

        if (this.activeStreams.has(streamId)) {
            throw new Error('Stream already active');
        }

        const outputPath = path.join(this.outputDir, streamId);
        fs.ensureDirSync(outputPath);

        // HLS output configuration
        const hlsPath = path.join(outputPath, 'playlist.m3u8');
        const segmentPath = path.join(outputPath, 'segment_%03d.ts');

        // FFmpeg command arguments
        const ffmpegArgs = [
            // Input video device
            '-f', 'dshow',
            '-video_size', resolution,
            '-framerate', framerate,
            '-i', `video=${videoDevice}`,
            
            // Input audio device (if specified)
            ...(audioDevice ? [
                '-f', 'dshow',
                '-sample_rate', audioSampleRate,
                '-i', `audio=${audioDevice}`
            ] : []),

            // Video encoding
            '-c:v', 'libx264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-b:v', bitrate,
            '-maxrate', bitrate,
            '-bufsize', '4000k',
            '-g', '60',
            '-keyint_min', '60',

            // Audio encoding (if audio device specified)
            ...(audioDevice ? [
                '-c:a', 'aac',
                '-b:a', '128k',
                '-ar', audioSampleRate
            ] : ['-an']), // No audio if no device

            // HLS output
            '-f', 'hls',
            '-hls_time', '2',
            '-hls_list_size', '10',
            '-hls_flags', 'delete_segments',
            '-hls_segment_filename', segmentPath,
            hlsPath
        ];

        console.log('Starting FFmpeg with args:', ffmpegArgs.join(' '));

        const ffmpegProcess = spawn('ffmpeg', ffmpegArgs);

        // Handle process events
        ffmpegProcess.stdout.on('data', (data) => {
            console.log(`FFmpeg stdout: ${data}`);
        });

        ffmpegProcess.stderr.on('data', (data) => {
            console.log(`FFmpeg stderr: ${data}`);
        });

        ffmpegProcess.on('close', (code) => {
            console.log(`FFmpeg process exited with code ${code}`);
            this.activeStreams.delete(streamId);
        });

        ffmpegProcess.on('error', (error) => {
            console.error('FFmpeg error:', error);
            this.activeStreams.delete(streamId);
        });

        // Store stream info
        this.activeStreams.set(streamId, {
            process: ffmpegProcess,
            hlsPath: hlsPath,
            outputPath: outputPath,
            options: options,
            startTime: new Date()
        });

        return {
            streamId,
            hlsUrl: `/live_streams/${streamId}/playlist.m3u8`,
            status: 'starting'
        };
    }

    // Stop capture
    stopCapture(streamId) {
        const stream = this.activeStreams.get(streamId);
        if (!stream) {
            throw new Error('Stream not found');
        }

        // Kill FFmpeg process
        stream.process.kill('SIGTERM');
        
        // Clean up files after a delay
        setTimeout(() => {
            fs.removeSync(stream.outputPath);
        }, 5000);

        this.activeStreams.delete(streamId);
        
        return { streamId, status: 'stopped' };
    }

    // Get active streams
    getActiveStreams() {
        const streams = [];
        this.activeStreams.forEach((stream, streamId) => {
            streams.push({
                streamId,
                hlsUrl: `/live_streams/${streamId}/playlist.m3u8`,
                options: stream.options,
                startTime: stream.startTime,
                status: 'active'
            });
        });
        return streams;
    }

    // Check if stream is active
    isStreamActive(streamId) {
        return this.activeStreams.has(streamId);
    }

    // Get stream info
    getStreamInfo(streamId) {
        const stream = this.activeStreams.get(streamId);
        if (!stream) {
            return null;
        }

        return {
            streamId,
            hlsUrl: `/live_streams/${streamId}/playlist.m3u8`,
            options: stream.options,
            startTime: stream.startTime,
            status: 'active'
        };
    }

    // Test FFmpeg installation
    async testFFmpeg() {
        return new Promise((resolve) => {
            exec('ffmpeg -version', (error, stdout, stderr) => {
                if (error) {
                    resolve({
                        installed: false,
                        error: error.message
                    });
                } else {
                    const versionMatch = stdout.match(/ffmpeg version ([^\s]+)/);
                    resolve({
                        installed: true,
                        version: versionMatch ? versionMatch[1] : 'unknown'
                    });
                }
            });
        });
    }
}

module.exports = FFmpegManager;
