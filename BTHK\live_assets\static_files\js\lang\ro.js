'use strict';/*!
 * This is a `i18n` language object.
 *
 * Romanian
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.ro === undefined) {
		exports.ro = {
			'mejs.plural-form': 5,			
			'mejs.download-file': 'Descarcă fişierul',			
			'mejs.install-flash': 'Utilizați un browser care nu are activat sau instalat playerul Flash. Porniți pluginul Flash player sau descărcați cea mai recentă versiune de la https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Ecran complet',			
			'mejs.play': 'Redare',
			'mejs.pause': 'Pauză',			
			'mejs.time-slider': '<PERSON>ursor timp',
			'mejs.time-help-text': 'Utilizează tastele săgeată Stânga/Dreapta pentru a avansa o secundă şi săgeţile Su<PERSON>/Jos pentru a avansa zece secunde.',
			'mejs.live-broadcast' : 'Difuzare în direct',			
			'mejs.volume-help-text': 'Utilizează tastele de săgeată Sus/Jos pentru a creşte/micşora volumul',
			'mejs.unmute': 'Cu sunet',
			'mejs.mute': 'Fără sunet',
			'mejs.volume-slider': 'Cursor volum',			
			'mejs.video-player': 'Player video',
			'mejs.audio-player': 'Player audio',			
			'mejs.captions-subtitles': 'Legende/Subtitrări',
			'mejs.captions-chapters': 'Capitolele',
			'mejs.none': 'Niciunul',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albanez',
			'mejs.arabic': 'Arabă',
			'mejs.belarusian': 'Belarusian',
			'mejs.bulgarian': 'Bulgară',
			'mejs.catalan': 'Catalană',
			'mejs.chinese': 'Chinezesc',
			'mejs.chinese-simplified': 'Chineză (Simplificată)',
			'mejs.chinese-traditional': 'Chineză (Tradițională)',
			'mejs.croatian': 'Croată',
			'mejs.czech': 'Cehă',
			'mejs.danish': 'Daneză',
			'mejs.dutch': 'Olandeză',
			'mejs.english': 'Engleză',
			'mejs.estonian': 'Estonă',
			'mejs.filipino': 'Filipinez',
			'mejs.finnish': 'Finlandeză',
			'mejs.french': 'Franceză',
			'mejs.galician': 'Galiciană',
			'mejs.german': 'Germană',
			'mejs.greek': 'Greacă',
			'mejs.haitian-creole': 'Creolele Haitiene',
			'mejs.hebrew': 'Ebraică',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Maghiar',
			'mejs.icelandic': 'Islandeză',
			'mejs.indonesian': 'Indonezian',
			'mejs.irish': 'Irlandeză',
			'mejs.italian': 'Italiană',
			'mejs.japanese': 'Japoneză',
			'mejs.korean': 'Coreeană',
			'mejs.latvian': 'Letonă',
			'mejs.lithuanian': 'Lituanian',
			'mejs.macedonian': 'Macedonean',
			'mejs.malay': 'Malay',
			'mejs.maltese': 'Malteză',
			'mejs.norwegian': 'Norvegiană',
			'mejs.persian': 'Persană',
			'mejs.polish': 'Polonez',
			'mejs.portuguese': 'Portugheză',
			'mejs.romanian': 'Română',
			'mejs.russian': 'Rusă',
			'mejs.serbian': 'Sârbă',
			'mejs.slovak': 'Slovacă',
			'mejs.slovenian': 'Slovenă',
			'mejs.spanish': 'Spaniolă',
			'mejs.swahili': 'Swahili',
			'mejs.swedish': 'Suedeză',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thai',
			'mejs.turkish': 'Turcă',
			'mejs.ukrainian': 'Ucrainean',
			'mejs.vietnamese': 'Vietnamez',
			'mejs.welsh': 'Welsh',
			'mejs.yiddish': 'Idiș'
		};
	}
})(mejs.i18n);