<!DOCTYPE html>
<html>
  {% include "admin/head.html" %}
  <body dir="rtl" class="noselect">
    {% include "admin/mnu.html" %}

    <div class="nopadding">
      <div class="page">
        <div class="msg info">من هنا تستطيع اضافة عدد من السيرفرات الخاصه بالبث لكي تستخدمها لاحقاً .</div>
        <h4>اضافة سيرفر جديد</h4>
        <form
          action="/{{admin_panel_path}}/save/{{admin_page}}"
          id="saveForm"
          method="POST"
          enctype="multipart/form-data"
        >
          <div class="content">
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اسم السيرفر</div>
                <div class="in-e col-sm-10">
                  <input name="server_name" id="server_name" placeholder="اسم السيرفر" />
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">تقنية البث</div>
                <div class="in-e col-sm-10">
                  <select name="server_tech" id="server_tech">
                    <option value="RTMP">RTMP (الاسرع)</option>
                    <option value="HLS">HLS</option>
                    <option value="Player">عبر المشغل مباشرة</option>
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اعدادات تقنية HLS</div>
                <div class="in-e col-sm-10">
                  <div>زمن التاخير المتوقع :<span id="setSUg"></span> ثواني/ثانية</div>
                  <div>عدد القطع</div>
                  <input
                    name="server_hls_num"
                    id="server_hls_num"
                    type="text"
                    data-provide="slider"
                    data-slider-ticks="[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"
                    data-slider-ticks-labels="[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"
                    data-slider-min="1"
                    data-slider-max="4"
                    data-slider-step="1"
                    data-slider-value="4"
                    data-slider-tooltip="hide"
                  />
                  <br />
                  <div>مدة القطعة بالثواني</div>
                  <input
                    name="server_hls_time"
                    id="server_hls_time"
                    type="text"
                    data-provide="slider"
                    data-slider-ticks="[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"
                    data-slider-ticks-labels="[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"
                    data-slider-min="1"
                    data-slider-max="4"
                    data-slider-step="1"
                    data-slider-value="4"
                    data-slider-tooltip="hide"
                  />
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">بث عبر</div>
                <div class="in-e col-sm-10">
                  <select name="server_by" id="server_by">
                    <option>VLC</option>
                    <option>FFMPEG</option>
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">جودة البث</div>
                <div class="in-e col-sm-10">
                  <input
                    name="server_quality"
                    id="server_quality"
                    type="text"
                    data-provide="slider"
                    data-slider-ticks="[1, 2, 3, 4, 5]"
                    data-slider-ticks-labels='["ضعيف 0.25", "متوسط 0.5","جيد 0.75", "عالي 1.0","اوتوماتيكي"]'
                    data-slider-min="1"
                    data-slider-max="5"
                    data-slider-step="1"
                    data-slider-value="4"
                    data-slider-tooltip="hide"
                  />
                  <!--<div class="msg info">جودة البث تؤثر على سرعة البث</div>-->
                </div>
              </div>
            </div>
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">معدل الفريمات</div>
                <div class="in-e col-sm-10">
                  <select name="server_framerate" id="server_framerate">
                    <option value="30" selected>30</option>
                    <option value="25">25</option>
                    <option value="20">20</option>
                    <option value="10">10</option>
                    <option value="60">60</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">تعديل حجم الصورة</div>
                <div class="in-e col-sm-10">
                  <select name="server_video_size" id="server_video_size">
                    <option value="-1" selected>اختيار تلقائي</option>
                    <option value="1920x1080">1920x1080</option>
                    <option value="1600x1200">1600x1200</option>
                    <option value="1360x768">1360x768</option>
                    <option value="1280x1024">1280x1024</option>
                    <option value="1280x960">1280x960</option>
                    <option value="1280x720">1280x720</option>
                    <option value="1024x768">1024x768</option>
                    <option value="800x600">800x600</option>
                    <option value="720x576">720x576</option>
                    <option value="720x480">720x480</option>
                    <option value="640x480">640x480</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">الـbitrate</div>
                <div class="in-e col-sm-10">
                  <select name="server_bitrate" id="server_bitrate">
                    <option value="-1" selected>بدون تحديد</option>
                    <option value="256K">256 KBit/s</option>
                    <option value="512K">512 KBit/s</option>
                    <option value="1M">1 MBit/s</option>
                    <option value="2M">2 MBit/s</option>
                    <option value="3M">3 MBit/s</option>
                    <option value="4M">4 MBit/s</option>
                    <option value="5M">5 MBit/s</option>
                    <option value="6M">6 MBit/s</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">تعديل صوت البث</div>
                <div class="in-e col-sm-10">
                  <select name="server_volume" id="server_volume">
                    <option value="no" selected>تلقائي</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    <option value="7">7</option>
                  </select>
                </div>
              </div>
            </div>

            <hr class="hard" />
            <div class="input">
              <div class="row nopadding">
                <div class="sbmt-btn" id="saveServer">
                  <div class="reg-icon icon col-xs-4 fa fa-plus"></div>
                  <div class="text col-xs-8">اضافة</div>
                </div>
              </div>
            </div>
          </div>
        </form>

        <div class="warppingTable">
          <table id="example" class="display" width="100%"></table>
          <script>
            $(document).ready(function () {
              const TABLE = "{{admin_page}}";
              var table = $("#example").DataTable({
                processing: true,
                serverSide: true,
                ajax: "/{{admin_panel_path}}/get/" + TABLE,

                language: {
                  decimal: "",
                  emptyTable: "لايوجد اي بيانات في الجدول",
                  info: "عرض _START_ الى _END_ من _TOTAL_ عناصر",
                  infoEmpty: "عرض 0 الى 0 من 0 عناصر",
                  infoFiltered: "(فلترة من _MAX_ مجموعة عناصر)",
                  infoPostFix: "",
                  thousands: ",",
                  lengthMenu: "عرض _MENU_ عناصر",
                  loadingRecords: "تحميل...",
                  processing: "معالجة...",
                  search: "بحث:",
                  zeroRecords: "لايوجد سجلات بحسب بحثك",
                  paginate: {
                    first: "الاول",
                    last: "الاخر",
                    next: "التالي",
                    previous: "السابق",
                  },
                  aria: {
                    sortAscending: ": تفعيل ترتيب العمود من الاصغر للاكبر",
                    sortDescending: ": تفعيل ترتيب العمود من الاكبر للاصغر",
                  },
                },
                columns: [
                  {
                    title: "id",
                    render: function (data, type, row, meta) {
                      return row.id;
                    },
                  },
                  {
                    title: "اسم السيرفر",
                    render: function (data, type, row, meta) {
                      return row.server_name;
                    },
                  },
                  {
                    title: "تقنية البث",
                    render: function (data, type, row, meta) {
                      return row.server_tech;
                    },
                  },
                  {
                    title: "عدد القطع",
                    render: function (data, type, row, meta) {
                      return row.server_hls_num;
                    },
                  },
                  {
                    title: "مدة القطعة",
                    render: function (data, type, row, meta) {
                      return row.server_hls_time;
                    },
                  },
                  {
                    title: "بث عبر",
                    render: function (data, type, row, meta) {
                      return row.server_by;
                    },
                  },
                  {
                    title: "جودة البث",
                    render: function (data, type, row, meta) {
                      switch (row.server_quality) {
                        case "1":
                          return "ضعيف";
                        case "2":
                          return "متوسط";
                        case "3":
                          return "جيد";
                        case "4":
                          return "عالي";
                        case "6":
                          return "اوتوماتيكي";
                      }
                      return row.server_quality;
                    },
                  },
                  {
                    title: "معدل الفريمات",
                    render: function (data, type, row, meta) {
                      return row.server_framerate;
                    },
                  },
                  {
                    title: "تعديل حجم الصورة",
                    render: function (data, type, row, meta) {
                      if (typeof row.server_video_size != "-1") return row.server_video_size;
                      else return "الافتراضي";
                    },
                  },
                  {
                    title: "bitrate",
                    render: function (data, type, row, meta) {
                      if (typeof row.server_bitrate != "-1") return row.server_bitrate;
                      else return "الافتراضي";
                    },
                  },
                  {
                    title: "درجة الصوت",
                    render: function (data, type, row, meta) {
                      if (typeof row.server_volume != "undefined") return row.server_volume;
                      else return "الافتراضي";
                    },
                  },

                  {
                    title: "icons",
                    render: function (data, type, row, meta) {
                      return "<i class='deleteROW fa fa-times' dataid='" + row.id + "' ></i>";
                    },
                  },
                ],
                columnDefs: [
                  {
                    width: "2%",
                    targets: 0,
                    width: "8%",
                    targets: 6,
                  },
                ],
              });
              $("body").delegate(".deleteROW", "click", function () {
                var id = $(this).attr("dataid");

                swal(
                  {
                    title: "حذف هذا السجل : " + id,
                    text: "هل انت متاكد من حذف هذا السجل  : " + id,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "نعم",
                    cancelButtonText: "لا",
                    closeOnConfirm: true,
                    html: true,
                  },
                  function (isConfirm) {
                    if (!isConfirm) return;
                    $.ajax({
                      url: "/{{admin_panel_path}}/delete/" + TABLE + "/" + id,
                      success: function () {
                        table.ajax.reload();
                        setTimeout(function (data) {
                          var channel_name = data.channel_name;
                          if (data.msg == "ok") {
                            swal("تم الحذف بنجاح", "تم حذف السجل بنجاح", "success");
                          } else {
                            swal("يجب حذف القناة اولاً", "خطاء هذا السجل موجود في قناة :" + channel_name, "error");
                          }
                        }, 300);
                      },
                    });
                  }
                );
              });

              $("#example tbody").on("click", "tr", function () {
                // var id = $(this)[0].childNodes[0].innerHTML;
                //$.ajax({url:"/delete/"+id,success:function(){
                //    table.ajax.reload();
                //}});
              });

              $("#button").click(function () {
                table.row(".selected").remove().draw(false);
              });

              $("#saveServer").click(function () {
                var formData = new FormData($("#saveForm")[0]);

                $.ajax({
                  url: $("#saveForm").attr("action"),
                  type: "POST",
                  data: formData,
                  success: function (data) {
                    if (data.msg == "ok") {
                      setTimeout(function () {
                        swal("تم الحفظ بنجاح", "تم حفظ بياناتك بنجاح", "success");
                      }, 500);

                      table.ajax.reload();
                    } else {
                      swal("خطاء", "حدث خطاء غير معروف!", "error");
                    }
                  },
                  error: function () {},
                  cache: false,
                  contentType: false,
                  processData: false,
                });
              });
            });
            // calc data
            $("input[name=server_hls_time]").change(function () {
              var valt = $(this).val();
              var valn = $("input[name=server_hls_num]").val();
              $("#setSUg").text(valt * valn);
            });
            $("input[name=server_hls_num]").change(function () {
              var valn = $(this).val();
              var valt = $("input[name=server_hls_time]").val();
              $("#setSUg").text(valt * valn);
            });
          </script>
        </div>
      </div>
    </div>

    {% include "admin/footer.html" %}
  </body>
</html>
