const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs-extra');
const bodyParser = require('body-parser');
const cors = require('cors');
const multer = require('multer');
const sqlite3 = require('sqlite3').verbose();
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const FFmpegManager = require('./ffmpeg-manager');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = 3333;

// Initialize FFmpeg Manager
const ffmpegManager = new FFmpegManager();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false,
  crossOriginEmbedderPolicy: false
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// CORS
app.use(cors());

// Body parser
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

// Static files
app.use('/interface', express.static(path.join(__dirname, 'interface')));
app.use('/assets', express.static(path.join(__dirname, 'assets')));
app.use('/live_assets', express.static(path.join(__dirname, 'live_assets')));
app.use('/live_streams', express.static(path.join(__dirname, 'live_streams')));

// Database setup
const dbPath = path.join(__dirname, 'live_db', 'bthk.db');
let db;

// Initialize database
function initDatabase() {
  db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
      console.error('Error opening database:', err.message);
    } else {
      console.log('Connected to SQLite database');
      createTables();
    }
  });
}

function createTables() {
  // Create channels table
  db.run(`CREATE TABLE IF NOT EXISTS channels (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    type TEXT DEFAULT 'live',
    status TEXT DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // Create settings table
  db.run(`CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,
    value TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // Create files table
  db.run(`CREATE TABLE IF NOT EXISTS files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    path TEXT NOT NULL,
    type TEXT DEFAULT 'video',
    size INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);

  // Insert default settings
  db.run(`INSERT OR IGNORE INTO settings (key, value) VALUES ('system_name', 'BTHK Live System')`);
  db.run(`INSERT OR IGNORE INTO settings (key, value) VALUES ('server_ip', 'localhost')`);
  db.run(`INSERT OR IGNORE INTO settings (key, value) VALUES ('server_port', '3333')`);
}

// File upload configuration
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, 'assets', 'uploads');
    fs.ensureDirSync(uploadPath);
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueName = uuidv4() + path.extname(file.originalname);
    cb(null, uniqueName);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB limit
  }
});

// Routes

// Main viewer page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'templates', 'viewer.html'));
});

// Admin panel
app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, 'templates', 'admin.html'));
});

// API Routes

// Get all channels
app.get('/api/channels', (req, res) => {
  db.all('SELECT * FROM channels ORDER BY created_at DESC', (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json({ channels: rows });
  });
});

// Add new channel
app.post('/api/channels', (req, res) => {
  const { name, url, type } = req.body;
  
  if (!name || !url) {
    return res.status(400).json({ error: 'Name and URL are required' });
  }

  db.run('INSERT INTO channels (name, url, type) VALUES (?, ?, ?)', 
    [name, url, type || 'live'], 
    function(err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      res.json({ 
        id: this.lastID, 
        message: 'Channel added successfully' 
      });
      
      // Broadcast to all connected clients
      io.emit('channelAdded', { id: this.lastID, name, url, type });
    }
  );
});

// Delete channel
app.delete('/api/channels/:id', (req, res) => {
  const { id } = req.params;
  
  db.run('DELETE FROM channels WHERE id = ?', [id], function(err) {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json({ message: 'Channel deleted successfully' });
    
    // Broadcast to all connected clients
    io.emit('channelDeleted', { id });
  });
});

// Get settings
app.get('/api/settings', (req, res) => {
  db.all('SELECT * FROM settings', (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    
    const settings = {};
    rows.forEach(row => {
      settings[row.key] = row.value;
    });
    
    res.json({ settings });
  });
});

// Update settings
app.post('/api/settings', (req, res) => {
  const settings = req.body;
  
  const promises = Object.keys(settings).map(key => {
    return new Promise((resolve, reject) => {
      db.run('INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)', 
        [key, settings[key]], 
        (err) => {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  });
  
  Promise.all(promises)
    .then(() => {
      res.json({ message: 'Settings updated successfully' });
      io.emit('settingsUpdated', settings);
    })
    .catch(err => {
      res.status(500).json({ error: err.message });
    });
});

// File upload
app.post('/api/upload', upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file uploaded' });
  }
  
  const { originalname, filename, size, mimetype } = req.file;
  
  db.run('INSERT INTO files (name, path, type, size) VALUES (?, ?, ?, ?)', 
    [originalname, filename, mimetype, size], 
    function(err) {
      if (err) {
        res.status(500).json({ error: err.message });
        return;
      }
      
      res.json({ 
        id: this.lastID,
        message: 'File uploaded successfully',
        file: {
          id: this.lastID,
          name: originalname,
          path: filename,
          size: size
        }
      });
    }
  );
});

// Get files
app.get('/api/files', (req, res) => {
  db.all('SELECT * FROM files ORDER BY created_at DESC', (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
      return;
    }
    res.json({ files: rows });
  });
});

// FFmpeg API endpoints

// Test FFmpeg installation
app.get('/api/ffmpeg/test', async (req, res) => {
  try {
    const result = await ffmpegManager.testFFmpeg();
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get available devices
app.get('/api/ffmpeg/devices', async (req, res) => {
  try {
    const devices = await ffmpegManager.getAvailableDevices();
    res.json(devices);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start capture
app.post('/api/ffmpeg/start', (req, res) => {
  try {
    const {
      videoDevice,
      audioDevice,
      resolution,
      framerate,
      bitrate,
      audioSampleRate
    } = req.body;

    if (!videoDevice) {
      return res.status(400).json({ error: 'Video device is required' });
    }

    const streamId = uuidv4();
    const result = ffmpegManager.startCapture({
      streamId,
      videoDevice,
      audioDevice,
      resolution,
      framerate,
      bitrate,
      audioSampleRate
    });

    // Add to channels database
    db.run('INSERT INTO channels (name, url, type, status) VALUES (?, ?, ?, ?)',
      [`Live Capture - ${videoDevice}`, result.hlsUrl, 'capture', 'active'],
      function(err) {
        if (err) {
          console.error('Error adding capture channel to database:', err);
        }
      }
    );

    res.json(result);

    // Broadcast to all connected clients
    io.emit('captureStarted', result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Stop capture
app.post('/api/ffmpeg/stop/:streamId', (req, res) => {
  try {
    const { streamId } = req.params;
    const result = ffmpegManager.stopCapture(streamId);

    // Remove from channels database
    db.run('DELETE FROM channels WHERE url LIKE ?', [`%${streamId}%`], (err) => {
      if (err) {
        console.error('Error removing capture channel from database:', err);
      }
    });

    res.json(result);

    // Broadcast to all connected clients
    io.emit('captureStopped', result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get active streams
app.get('/api/ffmpeg/streams', (req, res) => {
  try {
    const streams = ffmpegManager.getActiveStreams();
    res.json({ streams });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get stream info
app.get('/api/ffmpeg/streams/:streamId', (req, res) => {
  try {
    const { streamId } = req.params;
    const stream = ffmpegManager.getStreamInfo(streamId);

    if (!stream) {
      return res.status(404).json({ error: 'Stream not found' });
    }

    res.json(stream);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
  
  // Handle channel switching
  socket.on('switchChannel', (channelId) => {
    socket.broadcast.emit('channelSwitched', channelId);
  });
  
  // Handle viewer count
  socket.on('joinViewer', () => {
    socket.join('viewers');
    io.emit('viewerCount', io.sockets.adapter.rooms.get('viewers')?.size || 0);
  });
  
  socket.on('leaveViewer', () => {
    socket.leave('viewers');
    io.emit('viewerCount', io.sockets.adapter.rooms.get('viewers')?.size || 0);
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Initialize database and start server
initDatabase();

server.listen(PORT, () => {
  console.log(`🚀 BTHK Live System running on http://localhost:${PORT}`);
  console.log(`📺 Viewer page: http://localhost:${PORT}`);
  console.log(`⚙️  Admin panel: http://localhost:${PORT}/admin`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err.message);
      } else {
        console.log('Database connection closed.');
      }
    });
  }
  server.close(() => {
    console.log('Server closed.');
    process.exit(0);
  });
});
