'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.speed-rate'] = 'Velocitat';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.speed-rate'] = 'Rychlost';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.speed-rate'] = 'Geschwindigkeitsrate';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.speed-rate'] = 'Velocidad';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.speed-rate'] = 'نرخ سرعت';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.speed-rate'] = 'Vitesse';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.speed-rate'] = '<PERSON><PERSON><PERSON> reproduk<PERSON>';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.speed-rate'] = 'Sebesség';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.speed-rate'] = 'Velocità';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.speed-rate'] = '高速';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.speed-rate'] = '속도 속도';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.speed-rate'] = 'Snelheidsgraad';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.speed-rate'] = 'Prędkość';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.speed-rate'] = 'Taxa de velocidade';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.speed-rate'] = 'Viteză de viteză';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.speed-rate'] = 'Скорость воспроизведения';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.speed-rate'] = 'Rýchlosť';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.speed-rate'] = 'Hastighet';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.speed-rate'] = 'Швидкість відтворення';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.speed-rate'] = '速度';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.speed-rate'] = '速度';
}