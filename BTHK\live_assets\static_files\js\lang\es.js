/*!
 * This is a `i18n` language object.
 *
 * Spanish
 *
 * <AUTHOR>   <PERSON><PERSON><PERSON> (Twitter: @Jalios)
 *   <PERSON><PERSON><PERSON> (Twitter: @SoftCreatR)
 *   <PERSON> (GitHub: @rafa8626)
 *
 * @see core/i18n.js
 */(function (exports) {
	if (exports.es === undefined) {
		exports.es = {
			'mejs.plural-form': 1,			
			'mejs.download-file': 'Descargar archivo',			
			'mejs.install-flash': 'Esta usando un navegador que no tiene activado o instalado el reproductor de Flash. Por favor active el plugin del reproductor de Flash o descargue la versión más reciente en https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Pantalla completa',			
			'mejs.play': 'Reproducción',
			'mejs.pause': 'Pausa',			
			'mejs.time-slider': 'Control deslizante de tiempo',
			'mejs.time-help-text': 'Use las flechas Izquierda/Derecha para avanzar un segundo y las flechas Arriba/Abajo para avanzar diez segundos.',
			'mejs.live-broadcast': 'Transmisión en Vivo',			
			'mejs.volume-help-text': 'Use las flechas Arriba/Abajo para subir o bajar el volumen.',
			'mejs.unmute': 'Reactivar silencio',
			'mejs.mute': 'Silencio',
			'mejs.volume-slider': 'Control deslizante de volumen',			
			'mejs.video-player': 'Reproductor de video',
			'mejs.audio-player': 'Reproductor de audio',			
			'mejs.captions-subtitles': 'Leyendas/Subtítulos',
			'mejs.captions-chapters': 'Capítulos',
			'mejs.none': 'Ninguno',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albano',
			'mejs.arabic': 'Árabe',
			'mejs.belarusian': 'Bielorruso',
			'mejs.bulgarian': 'Búlgaro',
			'mejs.catalan': 'Catalán',
			'mejs.chinese': 'Chino',
			'mejs.chinese-simplified': 'Chino (Simplificado)',
			'mejs.chinese-traditional': 'Chino (Tradicional)',
			'mejs.croatian': 'Croata',
			'mejs.czech': 'Checo',
			'mejs.danish': 'Danés',
			'mejs.dutch': 'Holandés',
			'mejs.english': 'Inglés',
			'mejs.estonian': 'Estoniano',
			'mejs.filipino': 'Filipino',
			'mejs.finnish': 'Finlandés',
			'mejs.french': 'Francés',
			'mejs.galician': 'Gallego',
			'mejs.german': 'Alemán',
			'mejs.greek': 'Griego',
			'mejs.haitian-creole': 'Haitiano Criollo',
			'mejs.hebrew': 'Hebreo',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Húngaro',
			'mejs.icelandic': 'Islandés',
			'mejs.indonesian': 'Indonesio',
			'mejs.irish': 'Irlandés',
			'mejs.italian': 'Italiano',
			'mejs.japanese': 'Japonés',
			'mejs.korean': 'Coreano',
			'mejs.latvian': 'Letón',
			'mejs.lithuanian': 'Lituano',
			'mejs.macedonian': 'Macedonio',
			'mejs.malay': 'Malayo',
			'mejs.maltese': 'Maltés',
			'mejs.norwegian': 'Noruego',
			'mejs.persian': 'Persa',
			'mejs.polish': 'Polaco',
			'mejs.portuguese': 'Portugués',
			'mejs.romanian': 'Rumano',
			'mejs.russian': 'Ruso',
			'mejs.serbian': 'Serbio',
			'mejs.slovak': 'Eslovaco',
			'mejs.slovenian': 'Eslovenio',
			'mejs.spanish': 'Español',
			'mejs.swahili': 'Swahili',
			'mejs.swedish': 'Suizo',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Tailandés',
			'mejs.turkish': 'Turco',
			'mejs.ukrainian': 'Ucraniano',
			'mejs.vietnamese': 'Vietnamita',
			'mejs.welsh': 'Galés',
			'mejs.yiddish': 'Yiddish'
		};
	}
})(mejs.i18n);