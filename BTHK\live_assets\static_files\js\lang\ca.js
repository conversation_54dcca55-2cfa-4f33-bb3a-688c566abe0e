'use strict';/*!
 * This is a `i18n` language object.
 *
 * Catalan
 *
 * <AUTHOR> 	Tongro
 *
 * @see core/i18n.js
 */
(function (exports) {
	if (exports.ca === undefined) {
		exports.ca = {
			'mejs.plural-form': 1,			
			'mejs.download-file': 'Descar<PERSON>gar arxiu',			
			'mejs.install-flash': 'Esteu utilitzant un navegador que no tingui Flash Player activat o instal·lat. Activeu el vostre complement Flash Player o descarregueu la versió més recent de https://get.adobe.com/flashplayer/',			
			'mejs.fullscreen': 'Pantalla completa',			
			'mejs.play': 'Reproducció',
			'mejs.pause': 'Pausa',			
			'mejs.time-slider': 'Control lliscant de temps',
			'mejs.time-help-text': 'Utilitzeu les tecles de fletxa esquerra / dreta per avançar un segon, fletxes amunt / avall per avançar deu segons.',
			'mejs.live-broadcast' : 'Transmissió en directe',			
			'mejs.volume-help-text': 'Utilitzeu les tecles de fletxa amunt / avall per augmentar o disminuir el volum.',
			'mejs.unmute': 'Reactivar silenci',
			'mejs.mute': 'Silenci',
			'mejs.volume-slider': 'Control deslizador de volum',			
			'mejs.video-player': 'Reproductor de vídeo',
			'mejs.audio-player': 'Reproductor d\'àudio',			
			'mejs.captions-subtitles': 'Llegendes/Subtítols',
			'mejs.captions-chapters': 'Capítols',
			'mejs.none': 'Ningú',
			'mejs.afrikaans': 'Afrikaans',
			'mejs.albanian': 'Albanès',
			'mejs.arabic': 'Àrab',
			'mejs.belarusian': 'Bielorús',
			'mejs.bulgarian': 'Búlgar',
			'mejs.catalan': 'Català',
			'mejs.chinese': 'Xinès',
			'mejs.chinese-simplified': 'Xinès (Simplificat)',
			'mejs.chinese-traditional': 'Xinès (Tradicional)',
			'mejs.croatian': 'Croat',
			'mejs.czech': 'Txec',
			'mejs.danish': 'Danès',
			'mejs.dutch': 'Holandès',
			'mejs.english': 'Anglès',
			'mejs.estonian': 'Estonià',
			'mejs.filipino': 'Filipí',
			'mejs.finnish': 'Finlandès',
			'mejs.french': 'Francès',
			'mejs.galician': 'Gallec',
			'mejs.german': 'Alemany',
			'mejs.greek': 'Grec',
			'mejs.haitian-creole': 'Crioll haitià',
			'mejs.hebrew': 'Hebreu',
			'mejs.hindi': 'Hindi',
			'mejs.hungarian': 'Hongarès',
			'mejs.icelandic': 'Islandès',
			'mejs.indonesian': 'Indonesi',
			'mejs.irish': 'Irlandès',
			'mejs.italian': 'Italià',
			'mejs.japanese': 'Japonès',
			'mejs.korean': 'Coreà',
			'mejs.latvian': 'Letó',
			'mejs.lithuanian': 'Lituà',
			'mejs.macedonian': 'Macedoni',
			'mejs.malay': 'Malai',
			'mejs.maltese': 'Maltès',
			'mejs.norwegian': 'Noruec',
			'mejs.persian': 'Persa',
			'mejs.polish': 'Polonès',
			'mejs.portuguese': 'Portuguès',
			'mejs.romanian': 'Romanès',
			'mejs.russian': 'Rus',
			'mejs.serbian': 'Serbi',
			'mejs.slovak': 'Eslovac',
			'mejs.slovenian': 'Eslovè',
			'mejs.spanish': 'Espanyol',
			'mejs.swahili': 'Suahili',
			'mejs.swedish': 'Suec',
			'mejs.tagalog': 'Tagalog',
			'mejs.thai': 'Thai',
			'mejs.turkish': 'Turc',
			'mejs.ukrainian': 'Ucraïnès',
			'mejs.vietnamese': 'Vietnamita',
			'mejs.welsh': 'Gal·lès',
			'mejs.yiddish': 'Yiddish'
		};
	}
})(mejs.i18n);