
body{

    font-family: sans-serif;

}
html, html a {
    -webkit-font-smoothing: antialiased !important;
}
a:hover,a { text-decoration: none ; }
ul { list-style-type: none;padding: 0px;margin: 0px; }



 @media only screen and (min-width: 700px) {
    .loginbox{
      background: #151515;
      border: 1px solid #1f1f1f;
      padding: 22px;
      border-radius: 6px;
      width: 40%;
      text-align: center;
      margin-right: auto;
      margin-left: auto;
    }
 }
 @media only screen and (max-width: 700px) {
    .loginbox{

       padding: 22px;
       background: #151515;
       border: 1px solid #1f1f1f;
       border-radius: 6px;
       width: 100%;
       text-align: center;
       margin-right: auto;
       margin-left: auto;
    }
 }
 .form{
   
 }
 .form > .input{
     margin: 5px;
 }
 .form > .input > .textinput{
    
    padding-top: 8px;
    clear: both;
 }
 .form > .input > input,select{

    padding: 1px;
    background: #636363;
    border: none;
    border-radius: 5px;
    color: #fff;
 }
 .form > .inputbtn{
     background: #4d962f;
     color: #d7d9da;
     padding: 15px;
     border-radius: 4px;
     margin-top: 20px;
     cursor: pointer;
     border: none;
 }
 .form > .inputbtn:hover{
     background: #397520;

 }
 .form > hr{
    border-color: #23292f;
 }
 .trans-2-all{
    -webkit-transition: all 2s;
    transition: all 2s;
 }
 .trans-1-all{
    -webkit-transition: all 1s;
    transition: all 1s;
 }
 .trans-0d5-all{
    -webkit-transition: all .5s;
    transition: all .5s;
 }
 .trans-0d2-all{
    -webkit-transition: all .2s;
    transition: all .2s;
 }
.nopadding {
   padding: 0 !important;
   margin: 0 !important;
}
.mnu{
  background: #313131;
  border: 1px solid #000;
  box-shadow: 0px 0px 1px 1px #484848 inset;
  font-family: cpanel-sego-medium;
  margin-top: 5px;
  margin-bottom: 50px;
  margin-right: 20px;
  border-radius: 20px;
  padding-top: 20px;
  padding-bottom: 20px;

 }
 .mnu > li,.mnu > a{
    padding: 16px;
    border-top: 1px solid #37414b;
    border-bottom: 1px solid #1f262d;
    cursor: pointer;
    display: list-item;

 }
 .mnu > .active{
    background: #1c2127;

 }
 .mnu > li:hover,.mnu > a:hover,{
    background: #1c2127;

 }
 .mnu > li:hover > a,.mnu > a:hover > li{
.mnu
    color: #f3fdf8;

 }

 .mnu > li >a,.mnu > a > li{

    color: #939da8;

 }
 .mnu > li > a > i ,.mnu > a > li > i{
  margin-right: 2px;
  margin-left: 11px;
 }
 .mnu > li > a > img,.mnu > a > li > img{
  float: left;
  margin-left: -16px;
  margin-top: 6px;
 }
 .msg{
    color: #ededed;
    padding: 10px;
    margin: 7px;
    border-radius: 5px;
    font-family: cpanel-sego-bold;
 }
 .info:before{
     font-family: FontAwesome;
    content: "\f05a";
    margin-left: 10px;
 }
 .info{
    
    background: #3bb74a;
 }
 .error{
  background: #b7593b;
  
 }
 .error:before{
  font-family: FontAwesome;
  content: "\f00d";
  margin-left: 10px;

  
 }
 .warn{
  background: #b4b73b;
 }
 .warn:before{
  font-family: FontAwesome;
  content: "\f071";
  margin-left: 10px;
 }
 .num-info{
width: 85px;
float: right;
margin-right: 20px;
border-radius: 9px;
background: #ffffff;
text-align: center;
height: 85px;
padding-top: 10px;
color: #2e363f;
font-weight: bold;
 }
 .box-of-stats{

  border-radius: 5px;
  padding:10px;
  margin-left: auto;
  margin-right:auto;
  margin: 10px;
 }
.reg-box{
  border-radius: 6px;
  overflow: hidden;
  margin:20px;
  border: 1px solid #d8d8d8;
}
.reg-box > .title{
  background: #424c56;
  color: #c2c8d0;

  
}
.reg-box > .content{

  background: #fbfbfb;

}
.reg-box > .title > .reg-icon{
    width: 41px;
    text-align: center;
    padding: 15px;
    border-left:1px solid #181a1d;
}
.reg-box > .title > .expand-icon{
    width: 41px;
    text-align: center;
    padding: 15px;
    border-right:1px solid #181a1d;
    float: left;
    cursor: pointer;
}
.reg-box > .title > .expand-icon:hover{
    opacity: .5;

}
.reg-box > .title > .title-text{
      padding: 5px;
      font-family: cpanel-sego-medium;
}
.input{
  padding: 10px;

}

.input > .row > .text{
    padding-top: 15px;
    text-align: center;
}
.input > .row > .in-e > input,.input > .row > .in-e >select,.input > .row > .in-e >textarea{
  width: 100%;
  padding: 13px;
  height: 48px;
  transition: all 0.5s ease;
  font-size: 16px;
  font-family: cpanel-sego-light;
  background: #242424;
  border: 1px solid #000;
  box-shadow: 0px 0px 1px 1px #484848;
}
.input > .row > .in-e > input:focus,.input > .row > .in-e > select:focus{
   -webkit-box-shadow: inset 0px 0px 0px 2px #000;
    -moz-box-shadow: inset 0px 0px 0px 2px #000;
    box-shadow: inset 0px 0px 0px 2px #000;
  border: none;

  
} 
.input > .row > .in-e > .desc{
  font-style: italic;
  font-size: 11px;
  font-family: sans-serif;
  color: #a9b2b9;
  
} 

.input > .row > .colbsbmbtn{
  background: #d22c2c;
  font-family: cpanel-sego-bold;
  color: #fff;
  width: 222px;
  border-radius: 4px;
  overflow: hidden;
  float: left;
  cursor: pointer;
  transition: all .5s ease;
}
.input > .row > .colbsbmbtn > .icon{
  padding: 8px;
  padding-right: 10px;
  float: right;
  border-left: 1px solid #650606;
  text-align: center;
  font-size: 19px;
}
.input > .row > .colbsbmbtn > .text{
  padding-top: 8px;
  text-align: center;
}       
.input > .row > .colbsbmbtn:hover{
  background: #711212;
  
}

.input > .row > .orgsbmbtn{
  background: #5b626b;
  font-family: cpanel-sego-bold;
  color: #d0d0d0;
  width: 222px;
  border-radius: 4px;
  overflow: hidden;
  float: left;
  cursor: pointer;
  transition: all .5s ease;
}
.input > .row > .orgsbmbtn > .icon{
  padding: 8px;
  padding-right: 10px;
  float: right;
  font-size: 19px;
  border-left: 1px solid #2e363f;
  text-align: center;

}
.input > .row > .orgsbmbtn > .text{
  padding-top: 8px;
  text-align: center;
}
        
.input > .row > .orgsbmbtn:hover{
  background: #39414a;
  
}

.input > .row > .colcsbmbtn{
  background: #d8b345;
  font-family: cpanel-sego-bold;
  color: #fff;
  width: 222px;
  border-radius: 4px;
  overflow: hidden;
  float: left;
  cursor: pointer;
  transition: all .5s ease;
}
.input > .row > .colcsbmbtn > .icon{
  padding: 8px;
  padding-right: 10px;
  float: right;
  border-left: 1px solid #a9892c;
  text-align: center;
  font-size: 19px;
}
.input > .row > .colcsbmbtn > .text{
  padding-top: 8px;
  text-align: center;
}
        
.input > .row > .colcsbmbtn:hover{
  background: #9a7e2b;
  
}


.input > .row > .colasbmbtn{
  background: #d65454;
  font-family: cpanel-sego-bold;
  color: #fff;
  width: 170px;
  border-radius: 4px;
  overflow: hidden;
  float: left;
 
  cursor: pointer;
  transition: all .5s ease;
}
.input > .row > .colasbmbtn > .icon{
  padding: 8px;
  padding-right: 10px;
  float: right;
  border-left: 1px solid #b73f60;
  text-align: center;
  font-size: 19px;
}
.input > .row > .colasbmbtn > .text{
  padding-top: 8px;
  text-align: center;
}
        
.input > .row > .colasbmbtn:hover{
  background: #983131;
  
}
.input > .row > .sbmt-btn{
  background: #484848;
  font-family: cpanel-sego-bold;
  color: #fff;
  width: 170px;
  border-radius: 4px;
  overflow: hidden;
  float: left;
 
  cursor: pointer;
  transition: all .5s ease;
}
.input > .row > .sbmt-btn > .icon{
  padding: 8px;
  padding-right: 10px;
  float: right;
  border-left: 1px solid #313131;
  text-align: center;
  font-size: 19px;
}
.input > .row > .sbmt-btn > .text{
  padding-top: 8px;
  text-align: center;
}
        
.input > .row > .sbmt-btn:hover{
  background: #222;
  
}
::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: #666;
}
::-moz-placeholder { /* Firefox 19+ */
  color: #666;
}
:-ms-input-placeholder { /* IE 10+ */
  color: #666;
}
:-moz-placeholder { /* Firefox 18- */
  color: #666;
}
input:focus,
select:focus,
textarea:focus,
button:focus {
    outline: none;
}
hr.hard{
  border:1px solid #212121;
  margin: 0px;
}
hr{
  margin: 0px;
  margin-top:7px;
  margin-bottom: 7px; 
  border-color: #212121;
}
.infoclass{
  padding: 10px;
  font-family: cpanel-sego-bold;
  color: #ffffff;
  font-size: 16px;
}
.infoclass > .before{
    
    margin-left: 10px;
}
.table{
  margin:0px;
}
.table >.header{
  font-size:15px;
  border-bottom: 1px solid #e6e6e6;
}
.table >.header, .table > .row >div{
  text-align: center;
  border-left: 1px solid #e6e6e6;
  padding-top: 5px;
  padding-bottom: 5px;
}
 .pagecontiner {
    background: rgb(204,204,204); 
 }
page {
  background: white;
  display: block;
  margin: 0 auto;
  margin-bottom: 0.5cm;
  box-shadow: 0 0 0.5cm rgba(0,0,0,0.5);
  overflow: hidden;
}
page[size="A4"] {  
  width: 21cm;
  height: 29.7cm; 
}
page[size="A4"][layout="portrait"] {
  width: 29.7cm;
  height: 21cm;  
}
page[size="A3"] {
  width: 29.7cm;
  height: 42cm;
}
page[size="A3"][layout="portrait"] {
  width: 42cm;
  height: 29.7cm;  
}
page[size="A5"] {
  width: 14.8cm;
  height: 21cm;
}
page[size="A5"][layout="portrait"] {
  width: 21cm;
  height: 14.8cm;  
}
.dontprint{

}
.pagecontiner{
  padding: 20px;
}
@media print {
  body,page {
    margin: 0;
    box-shadow: 0;
    page-break-after: always;
  }
  
  .dontprint{
    display: none;
  }
  .pagecontiner{
    padding: 0px;
  }
}
.colorpicke,.colorbgpicke,.colorbpicke{
  width: 20px;
  height: 20px;
  margin: 5px;
  cursor: pointer;
  float: right;
  border: 1px solid #000;
}