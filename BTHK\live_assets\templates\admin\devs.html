<!DOCTYPE html>
<html>
  {% include "admin/head.html" %}
  <body dir="rtl" class="noselect">
    {% include "admin/mnu.html" %}
    <div class="nopadding">
      <div class="page">
        <div class="msg info">من هنا تستطيع اضافة اجهزه البث المباشر المرتبطه بالسيرفر مباشره .</div>
        <h4>اضافة جهاز بث جديد</h4>
        <form
          action="/{{admin_panel_path}}/save/{{admin_page}}"
          id="saveForm"
          method="POST"
          enctype="multipart/form-data"
        >
          <div class="content">
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">اسم الجهاز</div>
                <div class="in-e col-sm-10">
                  <input name="e_name" id="e_name" placeholder="اسم الجهاز" />
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">جهاز الصورة</div>
                <div class="in-e col-sm-10">
                  <select name="e_video_input" id="e_video_input">
                    {% for dev in vids %}
                    <option value="{{dev.alternativeName}}">{{dev.name}}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">جهاز الصوت</div>
                <div class="in-e col-sm-10">
                  <select name="e_audio_input" id="e_audio_input">
                    {% for dev in auds %}
                    <option value="{{dev.alternativeName}}">{{dev.name}}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <script>
              $(document).ready(function () {
                $("#e_video_input").change(function () {
                  $.ajax({
                    url: "/{{admin_panel_path}}/getPins/" + $("#e_video_input").val(),
                    success: function (data) {
                      console.log(data);

                      for (var i = 0; i < data.videopins.length; i++) {
                        $("#e_video_pin").append(
                          "<option value='" + data.videopins[i] + "'>" + data.videopins[i] + "</option>"
                        );
                      }
                      for (var i = 0; i < data.audiopins.length; i++) {
                        $("#e_audio_pin").append(
                          "<option value='" + data.audiopins[i] + "'>" + data.audiopins[i] + "</option>"
                        );
                      }
                    },
                  });
                  $.ajax({
                    url: "/{{admin_panel_path}}/getDevInfo/" + $("#e_video_input").val(),
                    success: function (data) {
                      $("#e_info").html(data);
                    },
                  });
                });
                $("#e_video_input").change();
                $("#testEasyCAP").click(function () {
                  var vpin = $("#e_video_pin").val();
                  var apin = $("#e_audio_pin").val();
                  $(".fa-play").addClass("fa-spinner");
                  $(".fa-play").addClass("fa-spin");
                  $(".fa-play").removeClass("fa-play");
                  swal({
                    title: "يتم الاختبار انتظر قليلاً",
                    text: "يتم اختبار البث",
                    icon: "info",
                    buttons: false,
                    timer: 30000,
                  });
                  //escape
                  let vidVal = encodeURIComponent($("#e_video_input").val());
                  let audioVal = encodeURIComponent($("#e_audio_input").val());
                  $.ajax({
                    url: "/{{admin_panel_path}}/checkEasyCap/" + vidVal + "/" + audioVal + "/" + vpin + "/" + apin,
                    success: function (data) {
                      if (data.is_ok) {
                        swal("الاعدادات صحيحة", "تم تخطي الاختبار بنجاح", "success");
                      } else {
                        swal("يوجد خطاء في الاعدادات", "خطاء في تخطي الاختبار", "error");
                      }
                      $(".fa-spinner").addClass("fa-play");
                      $(".fa-play").removeClass("fa-spinner");
                      $(".fa-play").removeClass("fa-spin");
                    },
                  });
                });
              });
            </script>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">مدخل الصوت</div>
                <div class="in-e col-sm-10">
                  <select name="e_video_pin" id="e_video_pin">
                    <option value="0">0</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    <option value="7">7</option>
                    <option value="8">8</option>
                    <option value="9">9</option>
                    <option value="10">10</option>
                    <option value="-1">جهاز غير مرتبط</option>
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">مدخل الصوت</div>
                <div class="in-e col-sm-10">
                  <select name="e_audio_pin" id="e_audio_pin">
                    <option value="0">0</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    <option value="7">7</option>
                    <option value="8">8</option>
                    <option value="9">9</option>
                    <option value="10">10</option>
                    <option value="-1">جهاز غير مرتبط</option>
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">صيغه الصورة</div>
                <div class="in-e col-sm-10">
                  <select name="input_format" id="input_format">
                    <option value="-1" selected>اختيار تلقائي</option>
                    <option value="mjpeg">mjpeg</option>
                    <option value="yuyv422">yuyv422</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">حجم الصورة</div>
                <div class="in-e col-sm-10">
                  <select name="video_size" id="video_size">
                    <option value="-1" selected>اختيار تلقائي</option>
                    <option value="1920:1080">1920:1080</option>
                    <option value="1600:1200">1600:1200</option>
                    <option value="1360:768">1360:768</option>
                    <option value="1280:1024">1280:1024</option>
                    <option value="1280:960">1280:960</option>
                    <option value="1280:720">1280:720</option>
                    <option value="1024:768">1024:768</option>
                    <option value="800:600">800:600</option>
                    <option value="720:576">720:576</option>
                    <option value="720:480">720:480</option>
                    <option value="640:480">640:480</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">معدل الفريمات</div>
                <div class="in-e col-sm-10">
                  <select name="framerate" id="framerate">
                    <option value="-1" selected>اختيار تلقائي</option>
                    <option value="20">20</option>
                    <option value="25">25</option>
                    <option value="30">30</option>
                    <option value="50">50</option>
                    <option value="60">60</option>
                  </select>
                </div>
              </div>
            </div>
            <hr />
            <div class="input">
              <div class="row nopadding">
                <div class="text col-sm-2">معلومات الجهاز</div>
                <div class="in-e col-sm-10">
                  <textarea
                    id="e_info"
                    style="font-size: 14px; direction: rtl; text-align: right; height: 300px"
                  ></textarea>
                </div>
              </div>
            </div>

            <hr class="hard" />
            <div class="input">
              <div class="row nopadding">
                <div class="sbmt-btn" id="saveServer">
                  <div class="reg-icon icon col-xs-4 fa fa-plus"></div>
                  <div class="text col-xs-8">اضافة</div>
                </div>
                <div class="colasbmbtn" id="testEasyCAP" style="margin-left: 10px">
                  <div class="reg-icon icon col-xs-4 fa fa-play"></div>
                  <div class="text col-xs-8">اختبار البث</div>
                </div>
              </div>
            </div>
          </div>
        </form>

        <div class="warppingTable">
          <table id="example" class="display" width="100%"></table>
          <script>
            $(document).ready(function () {
              const TABLE = "{{admin_page}}";
              var table = $("#example").DataTable({
                processing: true,
                serverSide: true,
                ajax: "/{{admin_panel_path}}/get/" + TABLE,

                language: {
                  decimal: "",
                  emptyTable: "لايوجد اي بيانات في الجدول",
                  info: "عرض _START_ الى _END_ من _TOTAL_ عناصر",
                  infoEmpty: "عرض 0 الى 0 من 0 عناصر",
                  infoFiltered: "(فلترة من _MAX_ مجموعة عناصر)",
                  infoPostFix: "",
                  thousands: ",",
                  lengthMenu: "عرض _MENU_ عناصر",
                  loadingRecords: "تحميل...",
                  processing: "معالجة...",
                  search: "بحث:",
                  zeroRecords: "لايوجد سجلات بحسب بحثك",
                  paginate: {
                    first: "الاول",
                    last: "الاخر",
                    next: "التالي",
                    previous: "السابق",
                  },
                  aria: {
                    sortAscending: ": تفعيل ترتيب العمود من الاصغر للاكبر",
                    sortDescending: ": تفعيل ترتيب العمود من الاكبر للاصغر",
                  },
                },
                columns: [
                  {
                    title: "id",
                    render: function (data, type, row, meta) {
                      return row.id;
                    },
                  },
                  {
                    title: "اسم الجهاز",
                    render: function (data, type, row, meta) {
                      return row.e_name;
                    },
                  },
                  {
                    title: "جهاز الصورة",
                    render: function (data, type, row, meta) {
                      return row.e_video_input;
                    },
                  },
                  {
                    title: "جهاز الصوت",
                    render: function (data, type, row, meta) {
                      return row.e_audio_input;
                    },
                  },
                  {
                    title: "مدخل الصورة",
                    render: function (data, type, row, meta) {
                      return row.e_video_pin;
                    },
                  },
                  {
                    title: "مدخل الصوت",
                    render: function (data, type, row, meta) {
                      return row.e_audio_pin;
                    },
                  },
                  {
                    title: "تحكم",
                    render: function (data, type, row, meta) {
                      return (
                        "<i class='testit fa fa-play' dataid='" +
                        row.id +
                        "' ></i> <i class='deleteROW fa fa-times' dataid='" +
                        row.id +
                        "' ></i>"
                      );
                    },
                  },
                ],
                columnDefs: [
                  {
                    width: "2%",
                    targets: 0,
                    width: "10%",
                    targets: 6,
                  },
                ],
              });
              $("body").delegate(".deleteROW", "click", function () {
                var id = $(this).attr("dataid");

                swal(
                  {
                    title: "حذف هذا السجل : " + id,
                    text: "هل انت متاكد من حذف هذا السجل  : " + id,
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "نعم",
                    cancelButtonText: "لا",
                    closeOnConfirm: true,
                    html: true,
                  },
                  function (isConfirm) {
                    if (!isConfirm) return;
                    $.ajax({
                      url: "/{{admin_panel_path}}/delete/" + TABLE + "/" + id,
                      success: function () {
                        table.ajax.reload();
                        setTimeout(function () {
                          swal("تم الحذف بنجاح", "تم حذف السجل بنجاح", "success");
                        }, 300);
                      },
                    });
                  }
                );
              });

              $("#example tbody").on("click", "tr", function () {
                // var id = $(this)[0].childNodes[0].innerHTML;
                //$.ajax({url:"/delete/"+id,success:function(){
                //    table.ajax.reload();
                //}});
              });

              $("#button").click(function () {
                table.row(".selected").remove().draw(false);
              });

              $("#saveServer").click(function () {
                var formData = new FormData($("#saveForm")[0]);

                $.ajax({
                  url: $("#saveForm").attr("action"),
                  type: "POST",
                  data: formData,
                  success: function (data) {
                    if (data.msg == "ok") {
                      setTimeout(function () {
                        swal("تم الحفظ بنجاح", "تم حفظ بياناتك بنجاح", "success");
                      }, 500);

                      table.ajax.reload();
                    } else {
                      swal("خطاء", "حدث خطاء غير معروف!", "error");
                    }
                  },
                  error: function () {},
                  cache: false,
                  contentType: false,
                  processData: false,
                });
              });
            });
            // calc data
            $("input[name=server_hls_time]").change(function () {
              var valt = $(this).val();
              var valn = $("input[name=server_hls_num]").val();
              $("#setSUg").text(valt * valn);
            });
            $("input[name=server_hls_num]").change(function () {
              var valn = $(this).val();
              var valt = $("input[name=server_hls_time]").val();
              $("#setSUg").text(valt * valn);
            });
            $("body").delegate(".testit", "click", function () {
              swal({
                title: "يتم الاختبار انتظر قليلاً",
                text: "يتم اختبار البث",
                icon: "info",
                buttons: false,
                timer: 30000,
              });
              var dataid = $(this).attr("dataid");

              $.ajax({
                url: "/{{admin_panel_path}}/testdvs/" + dataid,
                success: function (data) {
                  if (data.is_ok) {
                    swal("الاعدادات صحيحة", "تم تخطي الاختبار بنجاح", "success");
                  } else {
                    swal("تاكد من تركيب الجهاز", "خطاء في تخطي الاختبار", "error");
                  }
                },
              });
            });
          </script>
        </div>
      </div>
    </div>

    {% include "admin/footer.html" %}
  </body>
</html>
