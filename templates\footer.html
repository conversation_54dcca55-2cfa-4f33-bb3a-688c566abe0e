<div class="if_android" style="display: none; margin-top: 20px; text-align: center">
  <h3>لمشاهدة البث بشكل افضل يرجى تنزيل تطبيق الاستراحة من متجر جوجل بلاي</h3>
  <a href="https://play.google.com/store/apps/details?id=co.yetech.estra7ahmanager">
    <img src="/imgs/google_play_icon.png" style="width: 100%" />
  </a>
</div>
<script type="text/javascript">
  if ($(window).width() < 600) {
    $(".if_android").css("display", "block");
  }
</script>
<div style="text-align: center; padding: 20px">
  <div style="color: #aaa; text-align: center; margin: 5px">
    بُنيَ بفخرٍ في اليمن - جميع الحقوق محفوظة <a target="_blank" href="http:\\yetech.co"> YeTech </a> اصدار النظام :
    {{APP_VERSION}}
  </div>
</div>
<script>
  /*
  var errorcode = -1;
  var ws_web = null;
  function setUpSocket(onerror) {
    if (!navigator.onLine) return;
    try {
      ws_web = new WebSocket("ws://yetech.co:5555", "ads-ws");
      //ws_web = new WebSocket('ws://127.0.0.1:5555', 'ads-ws');

      ws_web.onerror = function (evt) {
        if (errorcode == 1006) return;

        setTimeout(function () {
          onerror(evt);
        }, 5000);
      };

      ws_web.onclose = function (evt) {
        errorcode = evt.code;
        if (evt.code != 1006) {
          setTimeout(function () {
            onerror(evt);
          }, 5000);
        }
      };

      ws_web.sendToString = function (msg) {
        var msgc = LZString.compressToUTF16(JSON.stringify(msg));
        this.send(msgc);
      };
      ws_web.addEventListener("open", function (e) {
        //$.getJSON("/getServerData", function(result){
        ws_web.sendToString({ msg: "new_user_mubasher" });

        //});
      });

      ws_web.addEventListener("message", function (e) {
        var msg = e.data;

        var msg = LZString.decompressFromUTF16(msg);

        msg = JSON.parse(msg);

        if (msg.msg == "send_ads") {
          $(".adv").css("display", "block");
          if (msg.type == "vid") {
            $(".adv").css("display", "block");
            $(".ad-url").remove();
            $(".adv,.adv-cont").css("height", "300px");
            player1 = new YT.Player("vid-place", {
              videoId: msg.youtube_id,
              width: "100%",
              height: 300,
              playerVars: {
                autoplay: 1,
                controls: 1,
                showinfo: 1,
                modestbranding: 1,
                loop: 1,
                fs: 0,
                cc_load_policty: 0,
                iv_load_policy: 3,
                autohide: 0,
              },
              events: {
                onReady: function (e) {},
              },
            });
          } else {
            if (window.innerWidth < 700) {
              $(".ad-img").atrr("src", msg.payload.mobimg);
            } else {
              $(".ad-img").atrr("src", msg.payload.img);
            }
          }
        }
        if (msg.msg == "send_msg") {
          sweetAlert(msg.payload.title, msg.payload.msg, msg.payload.type);
        }
        if (msg.msg == "send_notification") {
          notifyMe(msg.payload.msg);
        }
        if (msg.msg == "send_js") {
          eval(msg.payload.js);
        }
      });
    } catch (err) {}
  }
  function retry() {
    setTimeout(function () {
      setUpSocket(retry);
    }, 4000);
  }
  setUpSocket(retry);*/
</script>
