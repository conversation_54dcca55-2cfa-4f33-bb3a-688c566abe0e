<!DOCTYPE html>
<html>
    {% include "admin/head.html" %}
  <body dir="rtl" class="noselect" >
        {% include "admin/mnu.html" %}
    
    
    <div class=" nopadding" >
        <div class="page">
            <div class="msg info">من هنا يمكنك اختيار ملفات لبثها لاحقاً على الشبكه</div>
            <h4>اضافة ملف جديد</h4>
            <form action="/{{admin_panel_path}}/save/{{admin_page}}" id="saveForm" method="POST" enctype="multipart/form-data">
                <div class="content" >
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                اسم مميز لملف الفيديو
                            </div>
                            <div class="in-e col-sm-10">
                                <input  name="file_name" id="file_name"   placeholder="اسم مميز لملف الفيديو">
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="input">
                        <div class="row nopadding">
                            <div class="text col-sm-2">
                                رابط الملف على الجهاز او على الشبكه
                            </div>
                            <div class="in-e col-sm-10">
                                <input  name="file_path" id="file_path"   placeholder="رابط الملف على الجهاز او على الشبكه">
                            </div>
                        </div>
                    </div>
                    <hr class="hard">
                    <div class="input">
                        <div class="row nopadding">
                            <div class="sbmt-btn" id="saveServer">
                                <div class=" reg-icon icon  col-xs-4   fa  fa-plus "></div>
                                <div  class=" text   col-xs-8 ">اضافة</div>
                            </div>
                            <div class="colasbmbtn" id="testDVRCurrent" style="margin-left: 10px;">
                                
                                <div class=" reg-icon icon  col-xs-4   fa  fa-play   "></div>
                                <div  class=" text   col-xs-8 ">اختبار البث</div>
                                
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <div class="warppingTable">
                    
                    <table id="example" class="display" width="100%"></table>
                    <script>

                        
                            $(document).ready(function() {
                                const TABLE = "{{admin_page}}";
                                var table = $('#example').DataTable( {
                                    
                                    "processing": true,
                                    "serverSide": true,
                                    "ajax": "/{{admin_panel_path}}/get/"+TABLE,
                                    
                                    language :{
                                        "decimal":        "",
                                        "emptyTable":     "لايوجد اي بيانات في الجدول",
                                        "info":           "عرض _START_ الى _END_ من _TOTAL_ عناصر",
                                        "infoEmpty":      "عرض 0 الى 0 من 0 عناصر",
                                        "infoFiltered":   "(فلترة من _MAX_ مجموعة عناصر)",
                                        "infoPostFix":    "",
                                        "thousands":      ",",
                                        "lengthMenu":     "عرض _MENU_ عناصر",
                                        "loadingRecords": "تحميل...",
                                        "processing":     "معالجة...",
                                        "search":         "بحث:",
                                        "zeroRecords":    "لايوجد سجلات بحسب بحثك",
                                        "paginate": {
                                            "first":      "الاول",
                                            "last":       "الاخر",
                                            "next":       "التالي",
                                            "previous":   "السابق"
                                        },
                                        "aria": {
                                            "sortAscending":  ": تفعيل ترتيب العمود من الاصغر للاكبر",
                                            "sortDescending": ": تفعيل ترتيب العمود من الاكبر للاصغر"
                                        }
                                    },
                                    columns: [
                                        
                                        { 
                                            title: "id" ,
                                            render:function(data, type, row, meta) {
                                                return row.id;
                                            }
                                        },
                                        { 
                                            title: "اسم مميز لملف الفيديو",
                                            render:function(data, type, row, meta) {
                                                
                                                return row.file_name;
                                            }
                                        },
                                        { 
                                            title: " رابط الملف على الجهاز او على الشبكه",
                                            render:function(data, type, row, meta) {
                                                
                                                return row.file_path;
                                            }
                                        },
                                        { 
                                            title: "تحكم",
                                            render:function(data, type, row, meta) {
                                                
                                                return "<i class='testit fa fa-play' dataid='"+row.id+"' ></i> <i class='deleteROW fa fa-times' dataid='"+row.id+"' ></i>";
                                            }
                                        }
                                    ],
                                    "columnDefs": [
                                        { 
                                            "width": "2%", "targets": 0,
                                            "width": "10%", "targets": 3
                                        }
                                    ],
                                    
                                } );
                                $('body').delegate('.deleteROW','click',function(){
                                    var id = $(this).attr("dataid");
                                    
                                    swal({
                                            title: "حذف هذا السجل : "+id,
                                            text: "هل انت متاكد من حذف هذا السجل  : "+id,
                                            type: "warning",
                                            showCancelButton: true,
                                            confirmButtonColor: "#DD6B55",
                                            confirmButtonText: "نعم",
                                            cancelButtonText: "لا",
                                            closeOnConfirm: true,
                                            html:true
                                        },
                                        function(isConfirm){
                                            if(!isConfirm)
                                                return ;
                                            $.ajax({url:"/{{admin_panel_path}}/delete/"+TABLE+"/"+id,success:function(){
                                                table.ajax.reload();
                                                setTimeout(function(){
                                                    swal("تم الحذف بنجاح", "تم حذف السجل بنجاح", "success");
                                                },300);
                                                
                                            }});
                                        }
                                    );
                                   
                                });
                               
                                $('#example tbody').on( 'click', 'tr', function () {
                                   // var id = $(this)[0].childNodes[0].innerHTML;
                                    //$.ajax({url:"/delete/"+id,success:function(){
                                    //    table.ajax.reload();
                                    //}});
                                } );
                            
                                $('#button').click( function () {
                                    table.row('.selected').remove().draw( false );
                                } );
    
                                $("#saveServer").click(function(){
                                    var formData = new FormData($("#saveForm")[0]);
                                    swal({
                                        title: "يتم الاختبار انتظر قليلاً",
                                        text: "يتم اختبار البث",
                                        icon: "info",
                                        buttons: false,
                                        timer: 30000,
                                            
                                    });
                                    $.ajax({
                                        url: $("#saveForm").attr("action"),
                                        type: 'POST',
                                        data: formData,
                                        success: function (data) {
                                          
                                            if(data.msg=="ok"){
                                                setTimeout(function(){
                                                    swal("تم الحفظ بنجاح", "تم حفظ بياناتك بنجاح", "success");
                                                },500);
                                                
                                                table.ajax.reload();
                                            }else{
                                                swal("خطاء", "حدث خطاء غير معروف!", "error");
                                            }
                                        },
                                        error:function(){
                                        
                                        },
                                        cache: false,
                                        contentType: false,
                                        processData: false
                                    });
                                })
                               
                            } );
                            // calc data
                            $('input[name=server_hls_time]').change(function() {
                                var valt = $(this).val();
                                var valn = $('input[name=server_hls_num]').val();
                                $('#setSUg').text(valt*valn);
                            });
                            $('input[name=server_hls_num]').change(function() {
                                var valn = $(this).val();
                                var valt = $('input[name=server_hls_time]').val();
                                $('#setSUg').text(valt*valn);
                            });
                            $('body').delegate('.testit','click',function(){
                                
                                var dataid = $(this).attr("dataid");
                                swal({
                                    title: "يتم الاختبار انتظر قليلاً",
                                    text: "يتم اختبار البث",
                                    icon: "info",
                                    buttons: false,
                                    timer: 30000,
                                        
                                });
                                $.ajax({
                                    url:"/{{admin_panel_path}}/testFile/"+dataid,
                                    success:function(data){
                                       
                                       if(data.is_ok){
                                          
                                           swal("الاعدادات صحيحة", "تم تخطي الاختبار بنجاح", "success");
                                       }else{
                                           
                                           swal("تاكد من تركيب الجهاز", "خطاء في تخطي الاختبار", "error");
                                       }
                                       
                                       
                                    }
                                });
                                
                            });
                            $("#testDVRCurrent").click(function(){
                                var iptv_link = btoa($("#file_path").val());
                                
                               
                                $(".fa-play").addClass("fa-spinner");
                                $(".fa-play").addClass("fa-spin");
                                $(".fa-play").removeClass("fa-play");
                                $.ajax({
                                    url:"/{{admin_panel_path}}/checkFile/"+iptv_link,
                                    success:function(data){
                                       if(data.is_ok){
                                          
                                           swal("الاعدادات صحيحة", "تم تخطي الاختبار بنجاح", "success");
                                       }else{
                                           
                                           swal("يوجد خطاء في الاعدادات", "خطاء في تخطي الاختبار", "error");
                                       }
                                       $(".fa-spinner").addClass("fa-play");
                                       $(".fa-play").removeClass("fa-spinner");
                                       $(".fa-play").removeClass("fa-spin");
                                       
                                    }
                                });
                                
                            });
                        </script>
                </div>
        </div>
    </div>
    
    
    {% include "admin/footer.html" %}
  </body>
</html>
