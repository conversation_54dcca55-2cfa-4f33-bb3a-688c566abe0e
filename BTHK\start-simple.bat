@echo off
echo Starting BTHK Live System (Simple Version)...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js is not installed!
    echo Please download and install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

echo Starting BTHK Live System...
echo Server will be available at: http://localhost:3333
echo Admin panel: http://localhost:3333/admin
echo.
echo Note: This is a simplified version.
echo For full features, run install.bat first, then use start.bat
echo.

REM Start the simple server
node simple-server.js

pause
