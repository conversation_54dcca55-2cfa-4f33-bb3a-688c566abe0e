.mejs__sourcechooser-button,
.mejs-sourcechooser-button {
    position: relative;
}

.mejs__sourcechooser-button > button,
.mejs-sourcechooser-button > button {
    background: url('settings.svg') transparent no-repeat;
    background-position: 0 1px;
}

.mejs__sourcechooser-button .mejs__sourcechooser-selector,
.mejs-sourcechooser-button .mejs-sourcechooser-selector {
    background: rgba(50, 50, 50, 0.7);
    border: solid 1px transparent;
    border-radius: 0;
    bottom: 40px;
    height: 100px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    right: -10px;
    width: 130px;
}

.mejs__sourcechooser-selector ul,
.mejs-sourcechooser-selector ul {
    display: block;
    list-style-type: none !important;
    margin: 0;
    overflow: hidden;
    padding: 0;
}

.mejs__sourcechooser-selector li,
.mejs-sourcechooser-selector li {
    color: #fff;
    display: block;
    list-style-type: none !important;
    margin: 0;
    overflow: hidden;
    padding: 5px 0;
}
.mejs__sourcechooser-selector li:hover,
.mejs-sourcechooser-selector li:hover {
    background-color: rgba(255, 255, 255, 0.2);
    cursor: pointer;
}

.mejs__sourcechooser-selector input,
.mejs-sourcechooser-selector input {
    clear: both;
    float: left;
    margin: 3px 3px 0 0;
}

.mejs__sourcechooser-selector label,
.mejs-sourcechooser-selector label {
    display: inline-block;
    float: left;
    font-size: 12px;
    line-height: 15px;
    padding: 4px 0 0;
    width: 100px;
}
