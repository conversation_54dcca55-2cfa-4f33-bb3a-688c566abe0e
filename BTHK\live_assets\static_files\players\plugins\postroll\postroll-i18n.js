'use strict';

if (mejs.i18n.ca !== undefined) {
	mejs.i18n.ca['mejs.close'] = 'Tancar';
}
if (mejs.i18n.cs !== undefined) {
	mejs.i18n.cs['mejs.close'] = 'Zavř<PERSON>t';
}
if (mejs.i18n.de !== undefined) {
	mejs.i18n.de['mejs.close'] = 'Schließen';
}
if (mejs.i18n.es !== undefined) {
	mejs.i18n.es['mejs.close'] = 'Cerrar';
}
if (mejs.i18n.fa !== undefined) {
	mejs.i18n.fa['mejs.close'] = 'نزدیک';
}
if (mejs.i18n.fr !== undefined) {
	mejs.i18n.fr['mejs.close'] = 'Fermer';
}
if (mejs.i18n.hr !== undefined) {
	mejs.i18n.hr['mejs.close'] = 'Zatvori';
}
if (mejs.i18n.hu !== undefined) {
	mejs.i18n.hu['mejs.close'] = 'Bez<PERSON>rás';
}
if (mejs.i18n.it !== undefined) {
	mejs.i18n.it['mejs.close'] = 'Chiudere';
}
if (mejs.i18n.ja !== undefined) {
	mejs.i18n.ja['mejs.close'] = '閉じる';
}
if (mejs.i18n.ko !== undefined) {
	mejs.i18n.ko['mejs.close'] = '종료';
}
if (mejs.i18n.nl !== undefined) {
	mejs.i18n.nl['mejs.close'] = 'Sluiten';
}
if (mejs.i18n.pl !== undefined) {
	mejs.i18n.pl['mejs.close'] = 'Zamknij';
}
if (mejs.i18n.pt !== undefined) {
	mejs.i18n.pt['mejs.close'] = 'Fechar';
}
if (mejs.i18n.ro !== undefined) {
	mejs.i18n.ro['mejs.close'] = 'Închide';
}
if (mejs.i18n.ru !== undefined) {
	mejs.i18n.ru['mejs.close'] = 'Закрыть';
}
if (mejs.i18n.sk !== undefined) {
	mejs.i18n.sk['mejs.close'] = 'Zavrieť';
}
if (mejs.i18n.sv !== undefined) {
	mejs.i18n.sv['mejs.close'] = 'Stäng';
}
if (mejs.i18n.uk !== undefined) {
	mejs.i18n.uk['mejs.close'] = 'Закрити';
}
if (mejs.i18n.zh !== undefined) {
	mejs.i18n.zh['mejs.close'] = '關閉';
}
if (mejs.i18n['zh-CN'] !== undefined) {
	mejs.i18n['zh-CN']['mejs.close'] = '关闭';
}