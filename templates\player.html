<!DOCTYPE html>
<html>
  {% include "head.html" %}
  <style>
    body {
      direction: ltr;
    }
    input {
      width: 100%;
      background: #000;
      border: none;
      font-size: 25px;
      text-align: center;
    }
  </style>
  <body>
    <center>
      <h1 class="headerText">{{channel.cha_name}}</h1>

      <script src="/players/js/hls.min.js"></script>
      <script src="/players/js/flv.min.js"></script>
      {% if player=="MediaElementJS" %}
      <div class="media-wrapper">
        <div class="media-wrapper">
          <div id="setDOM"></div>
        </div>
      </div>

      <link rel="stylesheet" href="/players/css/mediaelementplayer.min.css" />
      <style>
        .mejs__overlay-button {
          background-image: url("/players/css/mejs-controls.svg");
        }
        .mejs__overlay-loading-bg-img {
          background-image: url("/players/css/mejs-controls.svg");
        }
        .mejs__button > button {
          background-image: url("/players/css/mejs-controls.svg");
        }
      </style>
      <script src="/players/js/mediaelement-and-player.min.js"></script>

      <script>
        let src = "{{src}}"
          .replace("${ip}", window.location.hostname)
          .replace("${ip}", window.location.hostname)
          .replace("${ip}", window.location.hostname);
        if (src === "undefined") {
          src = window.location.hostname;
        }
        function createPlayer() {
          $("#setDOM")[0].innerHTML = "";

          var domvideo = `<video width="{{ width }}" height="{{ height }}"  controls autoplay>
                        <source src="${src}">
                    </video>`;

          $("#setDOM")[0].innerHTML = domvideo;
          setTimeout(function () {
            $("video, audio").mediaelementplayer({
              pluginPath: "/players/swf/",
              renderers: ["native_dash", "flash_dash", "native_flv", "flash_video", "native_hls"],

              success: function (player, node, p) {
                p.play();
                player.addEventListener("ended", function (e) {
                  setTimeout(function () {
                    createPlayer();
                  }, 5000);
                });
                player.addEventListener("playing", function (e) {});
                //setTimeout(function(){ createPlayer();},30*1000*60);
              },
              error: function () {
                setTimeout(function () {
                  createPlayer();
                }, 5000);
              },
            });
          }, 1000);
        }
        createPlayer();
      </script>

      {% elseif player=="Flowplayer" %}
      <link rel="stylesheet" href="/players/css/skin.css" />

      <script src="/players/js/flowplayer.min.js"></script>
      <style>
        .flowplayer {
            width: {{width}};
            height: {{height}};
        }
      </style>
      <div id="container"></div>
      {% if type=="rtmp" %}

      <script>
        flowplayer("#container", {
          live: true,
          swf: "/players/swf/flowplayer.swf",

          clip: {
            sources: [{ type: "video/flash", src }],
          },
        });
      </script>
      {% elseif type=="hls" %}
      <script>
        flowplayer("#container", {
          live: true,
          swfHls: "/players/swf/flowplayerhls.swf",
          clip: {
            sources: [{ type: "application/x-mpegurl", src }],
          },
        });
      </script>
      {% elseif type=="Player" %}
      <script>
        flowplayer("#container", {
          live: true,
          swfHls: "/players/swf/flowplayerhls.swf",
          clip: {
            sources: [{ type: "video/flash", src }],
          },
        });
      </script>
      {% endif %} {% else %}
      <script src="/players/js/majhdwplayer.js"></script>
      <script src="/players/js/html5.hdwplayer.js"></script>
      <link rel="stylesheet" href="/players/css/html5.hdwstyles.css" type="text/css" media="all" />
      <div id="player" class="hdwhtml5player" style="width:{{width}}; height:{{height}};"></div>
      {% if type=="rtmp" %}
      <script>
        $j(document).ready(function () {
          hdwplayer({
            id: "player",
            swf: "/players/swf/player.swf",
            width: "{{width}}",
            height: "{{height}}",
            type: "rtmp",
            streamer: "{{streamer}}",
            video: "{{stream_name}}",
            stretch: "exactfit",
            autoStart: "true",
          });
        });
      </script>
      {% elseif type=="Player" %}
      <script>
        $j(document).ready(function () {
          hdwplayer({
            id: "player",
            swf: "/players/swf/player.swf",
            width: "{{width}}",
            height: "{{height}}",
            video: src,
            stretch: "exactfit",
            autoStart: "true",
          });
        });
      </script>
      {% else%}
      <script>
        $j(document).ready(function () {
          hdwplayer({
            id: "player",
            swf: "/players/swf/player.swf",
            width: "{{width}}",
            height: "{{height}}",
            type: "rtmp",
            streamer: "{{streamer}}",
            video: "{{stream_name}}",
            hls: src,
            stretch: "exactfit",
            autoStart: "true",
          });
        });
      </script>
      {% endif %} {% endif %}
      <div>رابط البث يمكنك تشغيله في اي مشغل خارجي</div>
      <input type="text" value="{{src}}" style="width: 100%" readonly />
      <div style="display: flex; gap: 10px; color: black; justify-content: center">
        <a style="color: black" href="vlc://{{src}}#Intent;scheme=http;package=org.videolan.vlc;end">
          <button class="btn"><span>الفتح في مشغل VLC</span></button>
        </a>

        <a style="color: black" href="intent:{{src}}#Intent;package=com.mxtech.videoplayer.ad;end">
          <button class="btn"><span>الفتح في مشغل MX Player</span></button>
        </a>

        <a style="color: black" href="intent:{{src}}#Intent;package=com.kmplayer;end">
          <button class="btn"><span>الفتح في مشغل KMPlayer</span></button>
        </a>
      </div>
    </center>
  </body>
</html>
